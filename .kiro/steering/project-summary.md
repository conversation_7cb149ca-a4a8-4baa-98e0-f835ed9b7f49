<!------------------------------------------------------------------------------------
   Add Rules to this file or a short description and have <PERSON><PERSON> refine them for you:   
-------------------------------------------------------------------------------------> 

这是一个安卓上使用的应用，要求小巧、专注、高效, 英文名叫LingTxt，中文名叫灵文本

# 场景考虑：
- 小巧，安装包小，启动占用资源小，没有后台进程，占用缓存小
- 大部分在手机等小屏幕上查看的操作(7-8英寸内)，很少在8寸以上的安卓设备使用，需要考虑界面大小和安卓端交互的特性
- 使用对象主要是技术人员为主，查看文件都是文本文件，包括.xml/.yaml/.yml/.md/.txt/.java/.python/.go/.html/.css/.js等等所有代码语言编写的文本文件，能自动关联打开

# 主要核心功能点：
- 自动关联业界相关的文本文件
- 以看文件内容为主，支持常见开发语言的语法高亮，显示行号，按行号跳转，显示不可见字符，支持代码折叠。
- 编辑功能比较简单（常见的输入、删除文本、整行删除、选择、复制、粘贴、按列选中、搜索、查找/替换、选中缩进与反缩进）
- 自动检测文件编码、支持手动切换编码
- 支持大文本打开

# 交互与体验优化
- 手势操作，充分利用触摸屏的优势，如：
  - 双指缩放: 快速调整字体大小
  - 侧滑菜单: 从屏幕边缘滑动，快速打开文件列表或设置
- 自定义配置
  - 字体选择与大小调整
  - 英文和中文语言界面设置
  - 支持随系统切换深色或浅色界面，也可以主动切换
- 最近访问文件列表

# 预留扩展：语音操作