# LingTxt(灵文本) 设计文档

## 概述

LingTxt是一个专为安卓平台设计的轻量级文本编辑器，采用现代安卓开发技术栈构建。应用遵循Material Design设计语言，使用MVVM架构模式，确保代码的可维护性和可扩展性。

## 技术架构

### 整体架构

```
┌─────────────────────────────────────────┐
│                UI Layer                 │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │  Activities │  │    Fragments    │   │
│  │  Composables│  │   (Optional)    │   │
│  └─────────────┘  └─────────────────┘   │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│              Domain Layer               │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │  ViewModels │  │  Use Cases      │   │
│  └─────────────┘  └─────────────────┘   │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│               Data Layer                │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │ Repositories│  │  Data Sources   │   │
│  │             │  │ (File, Prefs)   │   │
│  └─────────────┘  └─────────────────┘   │
└─────────────────────────────────────────┘
```

### 技术栈选择

- **开发语言**: Java (主要) + 少量Kotlin (仅用于必要的现代Android组件)
- **UI框架**: 传统View系统 + 部分Jetpack组件
- **架构模式**: MVVM + Repository Pattern
- **依赖注入**: Dagger 2 (Java友好)
- **异步处理**: RxJava 3 + AsyncTask/ExecutorService
- **文件操作**: Java NIO + Apache Commons IO
- **语法高亮**: 自定义实现基于正则表达式
- **配置存储**: SharedPreferences + 简单文件存储
- **最小SDK**: API 24 (Android 7.0)
- **目标SDK**: API 34 (Android 14)

## 组件和接口

### 核心组件架构

#### 1. 文件管理组件
```java
public interface FileRepository {
    Observable<FileContent> openFile(Uri uri);
    Completable saveFile(Uri uri, String content);
    Single<String> detectEncoding(Uri uri);
    Single<FileInfo> getFileInfoOnly(Uri uri);
    Single<Boolean> isFileAccessible(Uri uri);
}

public class FileContent {
    private final String content;
    private final String encoding;
    private final long size;
    private final long lastModified;

    // 构造函数和getter方法
}

public class FileInfo {
    private final Uri uri;
    private final String fileName;
    private final String mimeType;
    private final long fileSize;
    private final String encoding;
    private final LargeFileStrategy.FileInfo strategy;

    // 文件类型判断
    public boolean isTextFile();
    public String getFileSizeFormatted();
}
```

#### 2. 文件类型管理组件
```java
public class SupportedFileTypes {
    // 内置支持的文件扩展名（按分类组织）
    private static final Map<String, String[]> BUILTIN_EXTENSIONS;

    // 检查文件是否被支持
    public static boolean isSupportedFile(String fileName);
    public static boolean isSupportedFile(String fileName, Context context);

    // 扩展名管理
    public static boolean isSupportedExtension(String extension);
    public static boolean isSupportedExtension(String extension, Context context);

    // 用户自定义扩展名管理
    public static void addCustomExtension(Context context, String extension);
    public static void removeCustomExtension(Context context, String extension);
    public static Set<String> getCustomExtensions(Context context);

    // 获取支持的MIME类型
    public static String[] getSupportedMimeTypes();

    // 分类和统计
    public static Map<String, Integer> getCategoryStats(Context context);
    public static String getFormattedExtensionsList(Context context);
}
```

#### 3. 大文件处理组件
```java
public class LargeFileStrategy {
    public enum Strategy {
        NORMAL_LOAD,      // 正常加载（小文件）
        PREVIEW_LOAD,     // 预览加载（中等文件）
        VIRTUALIZED_LOAD, // 虚拟化加载（大文件）
        STREAMING_LOAD,   // 流式加载（超大文件）
        REJECT_LOAD       // 拒绝加载（过大文件）
    }

    public static class FileInfo {
        private final Strategy strategy;
        private final String reason;
        private final String fileSizeFormatted;

        // 获取推荐的处理策略
        public static FileInfo analyzeFile(long fileSize);
        public static int getRecommendedPreviewLines(Strategy strategy, long fileSize);
    }
}

public class LargeFileHandler {
    // 分块读取大文件
    public static Observable<String> readFileInChunks(Context context, Uri uri, String encoding);

    // 按行读取文件
    public static Observable<LineData> readFileByLines(Context context, Uri uri, String encoding, int maxLines);

    // 检查文件是否过大
    public static Single<Boolean> isFileTooLarge(Context context, Uri uri, long maxSize);
}
```

#### 4. 错误处理组件
```java
public class UserFeedbackManager {
    private static UserFeedbackManager instance;

    // 显示各种类型的用户反馈
    public void showToast(Context context, String message);
    public void showSnackbar(View view, String message);
    public void showErrorDialog(Context context, String title, String message);
    public void showConfirmDialog(Context context, String title, String message, Runnable onConfirm);

    // 显示加载对话框
    public AlertDialog showLoadingDialog(Context context, String message);
    public void updateLoadingProgress(AlertDialog dialog, int progress);
}

public class GlobalExceptionHandler implements Thread.UncaughtExceptionHandler {
    // 全局异常处理
    public void uncaughtException(Thread thread, Throwable throwable);

    // 崩溃恢复
    public void handleCrashRecovery(Context context);
}
```

#### 5. 权限管理组件
```java
public class PermissionManager {
    // 检查各种权限
    public static boolean hasStoragePermission(Context context);
    public static boolean hasBasicStoragePermission(Context context);
    public static boolean canAccessAllFiles(Context context);

    // 请求权限
    public static void requestStoragePermission(Activity activity);
    public static void requestManageExternalStoragePermission(Activity activity);

    // 权限状态检查
    public static PermissionStatus checkPermissionStatus(Context context);
}
```

#### 6. 语法高亮组件
```java
public interface SyntaxHighlighter {
    SpannableString highlight(String content, String fileExtension);
    List<Language> getSupportedLanguages();
}

public class Language {
    private final String name;
    private final List<String> extensions;
    private final List<String> keywords;
    private final Map<TokenType, Pattern> patterns;
    
    // 构造函数和getter方法
}
```

#### 3. 编辑器组件
```java
public interface EditorController {
    void insertText(String text);
    void deleteSelection();
    void selectAll();
    String copy();
    void paste(String text);
    void undo();
    void redo();
    void goToLine(int line);
    List<TextRange> find(String query);
    int replace(String query, String replacement);
    
    // 双模式支持
    void setViewMode(ViewMode mode);
    ViewMode getCurrentMode();
    void enterEditMode(boolean showKeyboard);
    void enterViewMode();
    boolean isViewOnlyMode();
    boolean isEditMode();
    
    // 状态监听器
    void addTextChangeListener(TextChangeListener listener);
    void addSelectionChangeListener(SelectionChangeListener listener);
    void addModeChangeListener(ModeChangeListener listener);
}

// 模式枚举和监听器
public enum ViewMode {
    VIEW_ONLY,    // 查看模式：只读，不显示键盘
    EDIT_MODE     // 编辑模式：可编辑，显示键盘
}

public interface ModeChangeListener {
    void onModeChanged(ViewMode newMode);
    void onRequestEdit();     // 用户请求进入编辑模式
    void onRequestView();     // 用户请求进入查看模式
}
```

#### 7. 配置管理组件
```java
public interface SettingsRepository {
    Observable<Float> getFontSize();
    Observable<String> getFontFamily();
    Observable<Theme> getTheme();
    Observable<AppLanguage> getLanguage();
    Observable<Boolean> getShowLineNumbers();
    Observable<Boolean> getShowInvisibleChars();
    
    Completable updateFontSize(float size);
    Completable updateTheme(Theme theme);
    // 其他设置更新方法
}
```

### 主要Activity和Fragment

#### MainActivity
- 负责文件关联处理和Intent处理
- 集成双模式文本编辑器（CodeEditText）
- 管理文件加载和保存
- 处理权限请求和错误处理
- 协调搜索、跳转、设置等功能
- 支持大文件处理策略

#### RecentFilesActivity
- 最近文件列表界面
- 支持文件快速打开
- 提供文件历史管理
- 集成文件选择器

#### SettingsActivity
- 用户设置界面
- 主题切换和字体配置
- 语言选择和个性化设置
- 文件类型管理入口

#### SupportedFileTypesDialog
- 文件类型管理对话框
- 显示分类的文件扩展名
- 支持自定义扩展名添加/删除
- 提供重置功能

## 数据模型

### 核心数据类

```java
// 文件信息
public class FileInfo {
    private final Uri uri;
    private final String name;
    private final String path;
    private final long size;
    private final long lastModified;
    private final String encoding;
    private final String language;
    
    // 构造函数、getter和setter方法
}

// 编辑器状态
public class EditorState {
    private String content = "";
    private TextRange selection = new TextRange(0, 0);
    private int scrollPosition = 0;
    private boolean isModified = false;
    private FileInfo currentFile = null;
    private ViewMode currentMode = ViewMode.VIEW_ONLY;  // 当前模式
    private boolean keyboardVisible = false;            // 键盘状态
    
    // 构造函数、getter和setter方法
}

// 应用设置
public class AppSettings {
    private float fontSize = 14f;
    private String fontFamily = "monospace";
    private Theme theme = Theme.SYSTEM;
    private AppLanguage language = AppLanguage.SYSTEM;
    private boolean showLineNumbers = true;
    private boolean showInvisibleChars = false;
    private boolean wordWrap = true;
    private boolean autoIndent = true;
    
    // 构造函数、getter和setter方法
}

// 最近文件
public class RecentFile {
    private final FileInfo fileInfo;
    private final long lastAccessed;
    private final int scrollPosition;
    
    // 构造函数、getter和setter方法
}
```

### 数据库设计

使用SQLite数据库存储最近文件，SharedPreferences存储用户偏好：

```java
// SQLite表结构
public class DatabaseHelper extends SQLiteOpenHelper {
    private static final String CREATE_RECENT_FILES = 
        "CREATE TABLE recent_files (" +
        "uri TEXT PRIMARY KEY," +
        "name TEXT NOT NULL," +
        "path TEXT NOT NULL," +
        "last_accessed INTEGER NOT NULL," +
        "scroll_position INTEGER DEFAULT 0)";
}

// 数据访问对象
public class RecentFileDao {
    public void insertRecentFile(RecentFile file);
    public List<RecentFile> getRecentFiles(int limit);
    public void deleteRecentFile(String uri);
    public void clearAllRecentFiles();
}
```

## 界面设计

### 布局结构

#### 竖屏布局（单手操作优化）
```
┌─────────────────────────────┐
│      Minimal Top Bar        │ ← 仅显示文件名，无操作按钮
├─────────────────────────────┤
│                             │
│                             │
│      Text Editor Area       │ ← 主要编辑区域(75%+)
│                             │
│                             │
│                             │
├─────────────────────────────┤
│    Bottom Action Bar        │ ← 主要操作区域
│  [菜单] [搜索] [跳转] [更多] │ ← 拇指可达区域
└─────────────────────────────┘
```

#### 横屏布局
```
┌─────────────────────────────────────┐
│           Minimal Top Bar           │ ← 文件名显示
├──────────────────────┬──────────────┤
│                      │              │
│   Text Editor Area   │ Quick Actions│ ← 右侧快捷操作
│                      │   [搜索]     │
│                      │   [跳转]     │
│                      │   [菜单]     │
└──────────────────────┴──────────────┘
```

#### 单手操作热区设计
```
手机屏幕拇指可达区域分析：
┌─────────────────────────────┐
│ ❌ 难以触达 (顶部区域)        │ ← 避免放置常用功能
├─────────────────────────────┤
│ ⚠️  需要调整握持 (中上部)     │
│                             │
│ ✅ 舒适区域 (中部)           │ ← 次要功能
│                             │
│ ✅ 最佳区域 (底部)           │ ← 主要操作按钮
│ [主菜单] [搜索] [跳转] [设置] │
└─────────────────────────────┘
```

### Material Design 组件使用（移动端优化）

- **简化Toolbar**: 仅显示文件名，无操作按钮
- **BottomAppBar**: 底部操作栏，放置主要功能按钮
- **BottomNavigationView**: 底部导航（文件、搜索、跳转、设置）
- **BottomSheetDialog**: 从底部弹出的菜单和设置面板
- **FloatingActionButton**: 位于底部的快速编辑操作
- **Snackbar**: 操作反馈，从底部弹出
- **RecyclerView**: 文件列表，支持快速滚动

### 实用交互设计亮点

#### 底部菜单跟随键盘
```
智能菜单定位：
┌─────────────────────────────┐
│      Text Content           │
│                             │
├─────────────────────────────┤ ← 键盘区域
│ ████████████████████████████ │
│ ████ 键盘区域 ████████████ │
├─────────────────────────────┤
│ [菜单] [搜索] [跳转] [设置] │ ← 菜单跟随到键盘上方
└─────────────────────────────┘
- 键盘显示时自动上移到键盘上方
- 键盘隐藏时恢复到底部位置
- 平滑动画过渡，用户体验流畅
- 保持所有功能的可访问性
```

#### 沉浸式阅读体验
- **渐进式隐藏**: 静止3秒后自动隐藏UI元素
- **智能恢复**: 轻触屏幕边缘或轻微滑动即可恢复界面
- **阅读进度**: 侧边显示微妙的阅读进度条

#### 精细化交互动效
- **磁吸效果**: 光标自动吸附到单词边界，提高编辑精度
- **流水动画**: 搜索结果高亮的波纹扩散效果
- **视觉辅助**: 编辑光标移动时显示放大镜效果，在放大镜中精确定位光标位置

#### 双模式交互设计

**核心理念：**
基于"以看文件内容为主"的产品定位，采用双模式设计解决移动端编辑器的核心痛点：

```
📖 查看模式（VIEW_ONLY）
├─ 默认状态：只读，无光标，无键盘
├─ 支持功能：双指缩放、滚动、搜索、复制
├─ 进入编辑：双击文本区域 或 点击底部编辑按钮
├─ 按钮状态：底部显示编辑图标（笔形图标）
└─ 优势：避免意外键盘弹出，专注内容查看

✏️ 编辑模式（EDIT_MODE）
├─ 激活状态：显示光标，支持键盘输入
├─ 支持功能：所有查看功能 + 文本编辑
├─ 智能键盘：缩放时临时隐藏，结束后恢复
├─ 按钮状态：底部显示退出编辑图标（勾选图标，活跃颜色）
└─ 退出编辑：点击底部退出编辑按钮（自动保存）
```

**交互流程图：**
```
打开文件 → 查看模式 ─双击文本/点击编辑按钮─→ 编辑模式
    ↑           ↓                                ↓
    └─────── 点击退出编辑按钮(自动保存) ←─────────────┘

按钮状态变化：
查看模式：底部显示编辑图标（笔形）
编辑模式：底部显示退出编辑图标（勾选，活跃颜色）

缩放手势处理：
查看模式：直接缩放，无键盘干扰
编辑模式：临时隐藏键盘 → 缩放 → 恢复键盘
```

**技术实现：**
```java
public enum ViewMode {
    VIEW_ONLY,    // 查看模式：setFocusable(false), setCursorVisible(false)
    EDIT_MODE     // 编辑模式：setFocusable(true), setCursorVisible(true)
}

// 智能键盘管理
private void handleScaleGesture() {
    if (isEditMode() && scaleGestureDetector.isInProgress()) {
        wasKeyboardVisible = isKeyboardVisible();
        if (wasKeyboardVisible) hideKeyboard();
    }
}
```

#### 手势操作优化

**基础手势：**
- **双指缩放**: 调整字体大小（任何模式，智能键盘管理）
- **双击文本**: 查看模式下进入编辑模式
- **长按文本**: 编辑模式下显示光标放大镜
- **单击定位**: 编辑模式下光标磁吸到单词边界
- **点击编辑按钮**: 查看模式下进入编辑模式
- **点击退出编辑按钮**: 编辑模式下退出编辑并自动保存

**移除的功能：**
- ~~**智能悬浮工具球**~~: 与系统功能重复，已移除
  - 原计划的可拖拽悬浮助手功能
  - 改为使用底部菜单跟随键盘的方案
- ~~**边缘滑动**~~: 与系统手势冲突，已移除
  - 原计划的左右上下边缘滑动功能
  - 改为使用底部操作栏和菜单按钮

### 主题设计

支持三种主题模式：
- **浅色主题**: 白色背景，深色文字
- **深色主题**: 深色背景，浅色文字  
- **跟随系统**: 自动切换

代码编辑器配色方案：
- **浅色**: VS Code Light风格
- **深色**: VS Code Dark风格
- **高对比度**: 针对可访问性优化

## 错误处理

### 错误类型定义

```java
public abstract class AppError extends Exception {
    public static class FileNotFound extends AppError {
        private final String path;
        public FileNotFound(String path) { this.path = path; }
        public String getPath() { return path; }
    }
    
    public static class FileAccessDenied extends AppError {
        private final String path;
        public FileAccessDenied(String path) { this.path = path; }
        public String getPath() { return path; }
    }
    
    public static class EncodingError extends AppError {
        private final String encoding;
        public EncodingError(String encoding) { this.encoding = encoding; }
        public String getEncoding() { return encoding; }
    }
    
    public static class FileTooLarge extends AppError {
        private final long size;
        public FileTooLarge(long size) { this.size = size; }
        public long getSize() { return size; }
    }
    
    public static class NetworkError extends AppError {}
    
    public static class UnknownError extends AppError {
        public UnknownError(Throwable cause) { super(cause); }
    }
}
```

### 错误处理策略

1. **文件操作错误**
   - 显示用户友好的错误消息
   - 提供重试机制
   - 记录错误日志

2. **内存不足**
   - 分块加载大文件
   - 清理不必要的缓存
   - 降级处理（禁用语法高亮）

3. **权限错误**
   - 引导用户授权
   - 提供替代方案
   - 优雅降级

### 异常恢复机制

```java
public class ErrorHandler {
    public ErrorAction handleFileError(AppError error) {
        if (error instanceof AppError.FileNotFound) {
            return ErrorAction.showMessage("文件未找到");
        } else if (error instanceof AppError.FileAccessDenied) {
            return ErrorAction.requestPermission();
        } else if (error instanceof AppError.FileTooLarge) {
            return ErrorAction.confirmLargeFile();
        } else {
            return ErrorAction.showGenericError();
        }
    }
}
```

## 测试策略

### 测试金字塔

1. **单元测试 (70%)**
   - Repository层测试
   - ViewModel逻辑测试
   - 工具类测试
   - 语法高亮算法测试

2. **集成测试 (20%)**
   - 文件操作集成测试
   - 数据库操作测试
   - 设置同步测试

3. **UI测试 (10%)**
   - 关键用户流程测试
   - 界面交互测试
   - 可访问性测试

### 测试工具

- **JUnit 5**: 单元测试框架
- **Mockk**: Kotlin模拟框架
- **Espresso**: UI自动化测试
- **Compose Testing**: Compose UI测试
- **Robolectric**: 本地单元测试

### 性能测试

- **启动时间**: 目标 < 3秒
- **内存使用**: 正常使用 < 100MB
- **大文件处理**: 10MB文件流畅编辑
- **电池消耗**: 后台零消耗

## 性能优化

### 内存优化

1. **文本处理优化**
   ```java
   public class OptimizedTextBuffer {
       private final List<String> chunks = new ArrayList<>();
       private static final int CHUNK_SIZE = 8192;
       
       public void loadLargeFile(String content) {
           // 分块加载，避免大字符串占用内存
           for (int i = 0; i < content.length(); i += CHUNK_SIZE) {
               int end = Math.min(i + CHUNK_SIZE, content.length());
               chunks.add(content.substring(i, end));
           }
       }
   }
   ```

2. **语法高亮优化**
   - 可视区域高亮渲染
   - 后台预处理
   - 缓存高亮结果

3. **图像资源优化**
   - 矢量图标使用
   - WebP格式图片
   - 资源压缩

### 渲染优化

1. **RecyclerView优化**
   ```java
   public class OptimizedEditorAdapter extends RecyclerView.Adapter<LineViewHolder> {
       private List<String> lines;
       
       @Override
       public void onBindViewHolder(LineViewHolder holder, int position) {
           // 只渲染可见行，实现虚拟化
           String line = lines.get(position);
           holder.bind(line, position + 1); // 显示行号
       }
       
       @Override
       public int getItemCount() {
           return lines != null ? lines.size() : 0;
       }
   }
   ```

2. **滚动优化**
   - 虚拟化长文本
   - 平滑滚动动画
   - 预加载机制

### 文件I/O优化

1. **异步操作**
   ```java
   public class FileRepository {
       private final ExecutorService executor = Executors.newCachedThreadPool();
       
       public Observable<String> readFileAsync(Uri uri) {
           return Observable.fromCallable(() -> {
               // 后台线程读取文件
               try (InputStream stream = contentResolver.openInputStream(uri);
                    BufferedReader reader = new BufferedReader(new InputStreamReader(stream))) {
                   StringBuilder content = new StringBuilder();
                   String line;
                   while ((line = reader.readLine()) != null) {
                       content.append(line).append("\n");
                   }
                   return content.toString();
               }
           }).subscribeOn(Schedulers.io());
       }
   }
   ```

2. **缓存策略**
   - 最近文件内容缓存
   - 语法高亮结果缓存
   - 设置信息缓存

## 安全考虑

### 文件访问安全

1. **权限控制**
   - 最小权限原则
   - 运行时权限请求
   - 作用域存储适配

2. **输入验证**
   ```java
   public boolean validateFileUri(Uri uri) {
       String scheme = uri.getScheme();
       if (!"content".equals(scheme) && !"file".equals(scheme)) {
           return false;
       }
       
       String path = uri.getPath();
       if (path != null && path.contains("..")) {
           return false;
       }
       
       return true;
   }
   ```

### 数据保护

1. **敏感信息处理**
   - 不记录文件内容到日志
   - 加密存储用户偏好
   - 安全清理临时数据

2. **网络安全**
   - HTTPS强制使用
   - 证书固定
   - 网络安全配置

## 可访问性

### 无障碍支持

1. **屏幕阅读器支持**
   ```java
   // 在EditText或自定义View中设置无障碍属性
   editText.setContentDescription("代码编辑器内容");
   editText.setImportantForAccessibility(View.IMPORTANT_FOR_ACCESSIBILITY_YES);
   
   // 为行号设置描述
   lineNumberView.setContentDescription("第" + lineNumber + "行");
   ```

2. **键盘导航**
   - Tab键导航支持
   - 快捷键支持
   - 焦点管理

3. **视觉辅助**
   - 高对比度主题
   - 字体大小调节
   - 颜色盲友好配色

### 国际化

1. **多语言支持**
   - 中文（简体/繁体）
   - 英文
   - 动态语言切换

2. **本地化适配**
   - RTL布局支持
   - 日期时间格式
   - 数字格式

## 扩展性设计

### 插件架构预留

```java
public interface EditorPlugin {
    String getName();
    String getVersion();
    
    void onEditorCreated(EditorController editor);
    void onTextChanged(String text);
    void onSave(FileInfo file);
}

public class PluginManager {
    private final List<EditorPlugin> plugins = new ArrayList<>();
    
    public void registerPlugin(EditorPlugin plugin) {
        plugins.add(plugin);
    }
    
    public void notifyTextChanged(String text) {
        for (EditorPlugin plugin : plugins) {
            plugin.onTextChanged(text);
        }
    }
}
```

### 语音操作接口

```java
public interface VoiceCommandHandler {
    Observable<CommandResult> handleCommand(VoiceCommand command);
}

public abstract class VoiceCommand {
    public static class OpenFile extends VoiceCommand {}
    public static class SaveFile extends VoiceCommand {}
    public static class GoToLine extends VoiceCommand {
        private final int line;
        public GoToLine(int line) { this.line = line; }
        public int getLine() { return line; }
    }
    public static class Find extends VoiceCommand {
        private final String query;
        public Find(String query) { this.query = query; }
        public String getQuery() { return query; }
    }
}
```

## 编辑辅助功能

### 光标定位辅助

#### 放大镜功能
```java
public class CursorMagnifier {
    // 光标移动时的放大镜效果
    public void showMagnifier(float x, float y) {
        // 在光标周围显示放大的文本区域
        // 用户可以在放大镜中精确定位光标
        // 支持拖拽调整光标位置
    }
    
    // 放大镜配置
    private static final float MAGNIFICATION_SCALE = 1.5f;
    private static final int MAGNIFIER_SIZE_DP = 120;
}
```

#### 智能光标行为
- **磁吸效果**: 光标自动吸附到单词边界，提高编辑精度（仅编辑模式）
- **智能选择**: 双击进入编辑模式或选择单词（根据当前模式）
- **视觉反馈**: 光标移动时的流畅动画效果
- **模式感知**: 查看模式下隐藏光标，编辑模式下显示光标

#### 双模式键盘管理
```java
public class SmartKeyboardManager {
    // 智能键盘显示/隐藏逻辑
    public void handleScaleGesture(boolean isScaling) {
        if (isEditMode() && isScaling) {
            // 缩放时临时隐藏键盘
            rememberKeyboardState();
            hideKeyboard();
        } else if (isEditMode() && !isScaling && wasKeyboardVisible) {
            // 缩放结束后恢复键盘
            showKeyboard();
        }
    }
    
    public void handleModeChange(ViewMode newMode) {
        switch (newMode) {
            case VIEW_ONLY:
                hideKeyboard();
                clearFocus();
                break;
            case EDIT_MODE:
                requestFocus();
                // 键盘显示由用户操作决定
                break;
        }
    }
}

这个设计文档为LingTxt提供了完整的技术架构和实用的交互设计，专注于解决用户的实际需求，提供流畅的编辑体验。