# LingTxt（灵文本）- 专为技术人员打造的轻量级代码查看器

<div align="center">

![LingTxt Logo](docs/images/logo.png)

**小巧 · 专注 · 高效**

一个专为安卓平台设计的轻量级文本编辑器，让你在手机上轻松查看和编辑代码文件

[下载体验](#下载) | [功能特色](#功能特色) | [支持格式](#支持的文件格式) | [使用指南](#使用指南)

</div>

---

## 🎯 为什么选择 LingTxt？

### 📱 移动端代码查看的痛点

- **文件关联不完善**：系统默认无法打开各种代码文件
- **应用臃肿缓慢**：现有编辑器功能复杂，启动慢，占用资源多
- **触摸体验差**：意外键盘弹出，光标定位不准确
- **大文件卡顿**：打开大型代码文件时性能不佳
- **界面不友好**：未针对小屏幕优化，单手操作困难

### ✨ LingTxt 的解决方案

- **🚀 极速启动**：3秒内启动，安装包小于10MB，空开占内存<15M
- **🔗 全面关联**：支持120+种文件格式，一键打开各类代码文件
- **👁️ 智能双模式**：默认查看模式避免意外键盘，需要时一键切换编辑
- **📱 移动优化**：专为7-8英寸屏幕设计，底部操作区域，单手友好
- **⚡ 大文件流畅**：分块加载技术，10MB+文件依然流畅
- **🎨 语法高亮**：支持常见编程语言的语法高亮着色

---

## 🌟 功能特色

### 🔍 **智能查看模式**

- **默认只读状态**：打开文件后进入查看模式，避免意外键盘弹出
- **专注内容阅读**：清晰的语法高亮，行号显示，代码结构一目了然
- **双击进入编辑**：需要编辑时双击文本或点击底部编辑按钮即可

### ✏️ **高效编辑体验**

- **精准光标定位**：智能磁吸到单词边界，长按显示放大镜辅助
- **基础编辑功能**：支持输入、删除、选择、复制、粘贴、搜索替换
- **自动保存**：退出编辑模式时自动保存，无需担心数据丢失

### 🎯 **移动端优化**

- **单手友好设计**：主要操作按钮位于底部拇指可达区域
- **智能手势支持**：双指缩放调整字体大小，支持各种触摸操作
- **横竖屏适配**：自动调整布局，在不同方向下都有最佳体验

### 🚀 **性能卓越**

- **轻量级架构**：无后台进程，内存占用小（空开占内存<15M），不影响其他应用
- **大文件支持**：采用分块加载技术，流畅处理大型代码文件
- **快速启动**：优化启动流程，3秒内即可开始使用

### 🎨 **个性化定制**

- **主题切换**：支持浅色、深色主题，可跟随系统自动切换
- **字体调节**：自由调整字体大小和字体族，适应不同阅读习惯
- **语言支持**：中英文界面切换，满足不同用户需求

### 📁 **文件管理**

- **广泛格式支持**：自动关联120+种文件格式，涵盖所有主流编程语言
- **最近文件**：快速访问最近打开的文件，提高工作效率
- **编码智能检测**：自动识别文件编码，支持手动切换

---

## 📋 支持的文件格式

### 💻 编程语言（40+种）

```
Java, Python, JavaScript, TypeScript, C/C++, C#, Go, Rust, 
Kotlin, Swift, PHP, Ruby, Scala, Dart, Lua, Shell, PowerShell
```

### 🌐 Web技术

```
HTML, CSS, SCSS, LESS, Vue, React (JSX), Angular, XML, SVG
```

### 📊 数据格式

```
JSON, YAML, TOML, CSV, INI, Properties, SQL, GraphQL
```

### 📝 文档格式

```
Markdown, AsciiDoc, reStructuredText, Plain Text, Log files
```

### ⚙️ 配置文件

```
Dockerfile, Makefile, .gitignore, .editorconfig, Gradle, Maven
```

### 🔧 自定义扩展

- 支持添加自定义文件扩展名
- 灵活的文件类型管理系统
- 分类显示和统计功能

---

## 🚀 使用指南

### 📥 安装使用

1. **下载安装**：从官网下载安装包（待定）
2. **文件关联**：安装后自动关联支持的文件格式
3. **打开文件**：通过文件管理器、微信、浏览器等直接打开文本文件

### 🎮 基础操作

- **查看文件**：打开后默认进入查看模式，支持滚动、缩放
- **进入编辑**：双击文本区域或点击底部编辑按钮
- **退出编辑**：点击底部退出编辑按钮，自动保存文件
- **搜索文本**：点击搜索按钮，支持查找和替换
- **跳转行号**：点击跳转按钮，快速定位到指定行

### ⚙️ 个性化设置

- **调整字体**：双指缩放或在设置中调整字体大小
- **切换主题**：设置中选择浅色、深色或跟随系统
- **语言切换**：支持中英文界面切换
- **文件类型管理**：添加或删除自定义文件扩展名

---

## 🏆 核心优势

| 特性        | LingTxt | 传统编辑器     |
| --------- | ------- | --------- |
| **启动速度**  | < 3秒    | 5-10秒     |
| **安装包大小** | < 10MB  | 50-100MB+ |
| **内存占用**  | < 100MB | 200MB+    |
| **文件关联**  | 120+格式  | 有限支持      |
| **移动优化**  | 专门设计    | 桌面移植      |
| **双模式**   | 智能切换    | 单一模式      |
| **大文件**   | 流畅处理    | 容易卡顿      |
| **后台进程**  | 无       | 常驻后台      |

---

## 🎯 适用场景

### 👨‍💻 **开发者日常**

- 查看项目代码文件
- 快速编辑配置文件
- 阅读技术文档
- 检查日志文件

### 📱 **移动办公**

- 通过微信接收的代码文件
- 浏览器下载的技术文档
- 云盘同步的项目文件
- 邮件附件的配置文件

### 🎓 **学习研究**

- 阅读开源项目代码
- 学习编程语言语法
- 分析代码结构
- 做笔记和标注

---

## 📞 联系我们

- **问题反馈**：[GitHub Issues](https://github.com/lingtxt/issues)
- **功能建议**：[功能请求](https://github.com/lingtxt/discussions)
- **用户交流**：加微信机器人(fengincn)进群
- **邮箱联系**：<EMAIL>
- **作者**：凌封（公众号：凌封小子）

---

## 📄 开源协议

LingTxt 采用 [MIT License](LICENSE) 开源协议，欢迎贡献代码和建议。

---

<div align="center">

**让代码查看变得简单高效**

[立即下载体验](https://github.com/lingtxt/releases) | [查看源码](https://github.com/lingtxt)

---

*LingTxt - 专为移动端打造的代码查看利器*

</div>
