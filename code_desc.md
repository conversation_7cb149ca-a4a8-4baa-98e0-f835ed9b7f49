# LingTxt(灵文本) 项目代码结构说明

## 项目概述

LingTxt是一个专为安卓平台设计的轻量级文本编辑器，主要面向技术人员使用。项目采用MVVM架构模式，使用Java作为主要开发语言，集成了Dagger 2依赖注入框架，支持多种编程语言文件的语法高亮显示。

## 技术栈

- **开发语言**: Java (主要)
- **架构模式**: MVVM + Repository Pattern
- **依赖注入**: Dagger 2
- **异步处理**: RxJava 3
- **UI框架**: 传统View系统 + DataBinding
- **最小SDK**: API 24 (Android 7.0)
- **目标SDK**: API 34 (Android 14)

## 项目结构

### 根目录结构
```
lingtext/
├── .kiro/                    # 项目文档和规范
│   ├── steering/            # 全局规则和项目简介
│   └── specs/               # 需求、设计和任务文档
├── app/                     # Android应用主目录
├── docs/                    # 项目文档
├── build.gradle            # 根级构建配置
├── settings.gradle         # 项目设置
└── README_BUILD.md         # 构建说明
```

### 应用代码结构 (app/src/main/java/com/lingtxt/editor/)

#### 1. 应用入口
- `LingTxtApplication.java` - 应用程序入口类，配置Dagger依赖注入

#### 2. 基础架构 (base/)
- `BaseActivity.java` - Activity基类，提供通用功能
- `BaseFragment.java` - Fragment基类
- `BaseViewModel.java` - ViewModel基类

#### 3. 依赖注入 (di/)
- `AppComponent.java` - Dagger主组件
- `AppModule.java` - 应用级模块
- `DataModule.java` - 数据层模块
- `ViewModelFactory.java` - ViewModel工厂
- `ViewModelModule.java` - ViewModel模块

#### 4. 数据层 (data/)

##### 数据模型 (model/)
- `FileInfo.java` - 文件信息模型
- `FileContent.java` - 文件内容模型
- `EditorState.java` - 编辑器状态模型
- `AppSettings.java` - 应用设置模型
- `RecentFile.java` - 最近文件模型
- `Theme.java` - 主题枚举
- `AppLanguage.java` - 语言枚举

##### 数据库 (database/)
- SQLite数据库相关类，用于存储最近文件记录

##### 仓库 (repository/)
- Repository模式实现，提供数据访问抽象层

##### 偏好设置 (preferences/)
- SharedPreferences封装类

##### 异常处理 (exception/)
- 自定义异常类定义

#### 5. UI层 (ui/)

##### 主界面 (main/)
- `MainActivity.java` - 主Activity，处理文件关联和应用入口
- `MainViewModel.java` - 主界面ViewModel

##### 编辑器 (editor/)
- `CodeEditText.java` - 自定义文本编辑组件
- 编辑器相关的UI组件

##### 手势处理 (gesture/)
- 触摸手势识别和处理

##### 最近文件 (recent/)
- 最近文件列表界面

##### 活动 (activity/)
- 其他Activity类

##### 组件 (widget/)
- 自定义UI组件和工具类

#### 6. 语法高亮 (syntax/)
- `SyntaxHighlighter.java` - 语法高亮接口
- `SyntaxHighlightManager.java` - 语法高亮管理器
- `LanguageDetector.java` - 编程语言检测器

#### 7. 搜索功能 (search/)
- `SearchEngine.java` - 文本搜索引擎

#### 8. 工具类 (utils/)
- `FileUtils.java` - 文件操作工具
- `EncodingDetector.java` - 文件编码检测
- `LargeFileHandler.java` - 大文件处理
- `FileAccessHelper.java` - 文件访问助手（SAF支持）
- `FileInfoHelper.java` - 文件信息获取工具
- `PermissionManager.java` - 权限管理工具
- `UserFeedbackManager.java` - 用户反馈管理器
- `GlobalExceptionHandler.java` - 全局异常处理器
- `SettingsChangeManager.java` - 设置变更管理
- `BindingAdapters.java` - DataBinding适配器
- `ManifestGenerator.java` - 清单文件生成工具

#### 9. 配置 (config/)
- `SupportedFileTypes.java` - 支持的文件类型配置（120+种文件类型）

#### 10. 对话框 (ui/dialog/)
- `SupportedFileTypesDialog.java` - 文件类型管理对话框
- `FileInfoDialog.java` - 文件信息显示对话框

### 资源文件结构 (app/src/main/res/)

#### 布局文件 (layout/)
- `activity_main.xml` - 主界面布局
- `layout-land/` - 横屏布局
- `layout-sw600dp/` - 平板布局

#### 主题和样式 (values/)
- `themes.xml` - 应用主题
- `colors.xml` - 颜色定义
- `strings.xml` - 字符串资源
- `values-night/` - 深色主题

#### 图标资源 (drawable/, mipmap-*)
- 应用图标和UI图标

#### 菜单 (menu/)
- 菜单资源文件

#### XML配置 (xml/)
- 文件提供者配置等

## 核心功能实现状态

### ✅ 已完成 (任务1-13)
1. **项目基础架构** - Dagger 2依赖注入，MVVM架构
2. **核心数据模型** - 完整的数据模型和数据库结构
3. **文件操作核心功能** - 文件读取、保存、编码检测
4. **基础UI框架** - Material Design界面，响应式布局
5. **文本编辑器核心组件** - 自定义CodeEditText，双模式交互
6. **语法高亮系统** - 多语言语法高亮支持
7. **搜索和跳转功能** - 文本搜索、替换、行号跳转
8. **文件关联和Intent处理** - 系统文件关联，最近文件管理
9. **触摸手势和交互优化** - 双指缩放，智能光标定位，磁吸效果
10. **沉浸式阅读体验** - 双模式设计，智能键盘管理
11. **个性化设置系统** - 字体、主题、语言设置，完整的设置界面
12. **性能优化和大文件支持** - 大文件策略，分块加载，虚拟化处理
13. **错误处理和用户反馈** - 全局异常处理，用户反馈管理，崩溃恢复

### ✅ 新增完成功能
14. **文件类型管理系统** - 120+文件类型支持，分类管理，自定义扩展名
15. **权限管理系统** - Android 13+权限适配，SAF集成，权限检测
16. **文件信息展示** - 详细文件信息对话框，文件属性显示
17. **UI优化和图标系统** - Material Design 3图标，底部菜单重组

### 🚧 待完成 (任务18-20)
18. **代码折叠和高级编辑功能** - 代码折叠，智能编辑
19. **测试和质量保证** - 单元测试，UI测试
20. **应用打包和优化** - 代码混淆，APK优化

## 双模式交互设计

项目的核心特色是双模式交互设计：

### 查看模式 (VIEW_ONLY)
- 默认状态，只读模式
- 不显示光标和键盘
- 支持双指缩放、滚动、搜索
- 双击文本进入编辑模式

### 编辑模式 (EDIT_MODE)
- 显示光标，支持键盘输入
- 智能键盘管理（缩放时临时隐藏）
- 光标磁吸到单词边界
- 返回键退出编辑模式

## 构建和运行

### 环境要求
- Android Studio Arctic Fox (2020.3.1) 或更高版本
- Gradle 7.0+
- JDK 11 或更高版本
- Android SDK API 24-34

### 构建步骤
1. 在Android Studio中打开项目
2. 等待Gradle同步完成
3. Build → Make Project
4. Run → Run 'app'

### 当前功能状态
- ✅ 基础文件查看和编辑（双模式交互）
- ✅ 语法高亮显示（多语言支持）
- ✅ 文件关联和打开（120+文件类型）
- ✅ 响应式界面设计（Material Design 3）
- ✅ 大文件处理（多种策略）
- ✅ 错误处理和用户反馈（完整系统）
- ✅ 权限管理（Android 13+适配）
- ✅ 文件类型管理（用户可自定义）
- ✅ 文件信息展示（详细属性）
- ✅ 设置系统（完整的个性化配置）

### 核心特色功能

#### 1. 双模式交互设计
- **查看模式**：默认只读，无键盘干扰，专注内容查看
- **编辑模式**：智能键盘管理，光标磁吸，精确编辑
- **智能切换**：双击文本或点击按钮切换模式

#### 2. 大文件处理策略
- **智能分析**：根据文件大小自动选择处理策略
- **多种模式**：正常加载、预览加载、虚拟化加载、流式加载
- **用户选择**：提供不同选项让用户决定如何处理

#### 3. 文件类型管理
- **120+支持**：涵盖8大分类的文件类型
- **用户自定义**：可添加/删除自定义扩展名
- **分类展示**：按功能分类组织，便于管理

#### 4. 权限管理系统
- **Android 13+适配**：完整支持新权限模型
- **SAF集成**：使用Storage Access Framework
- **智能检测**：自动检测权限状态并引导用户

## 下一步开发重点

1. **添加高级编辑功能** - 代码折叠、智能缩进、自动补全
2. **完善测试覆盖** - 单元测试、集成测试、UI测试
3. **性能进一步优化** - 内存使用优化、启动速度提升
4. **可访问性增强** - 屏幕阅读器支持、键盘导航
5. **国际化完善** - 多语言支持、本地化适配

## 技术亮点

### 1. 文件关联系统
- **MIME类型优先**：主要依赖系统MIME类型识别，避免复杂的pathPattern配置
- **简洁高效**：通过少量intent-filter覆盖95%的文件类型
- **兼容性强**：适配不同Android版本和厂商的文件管理器

### 2. 大文件处理架构
- **策略模式**：根据文件大小自动选择最优处理策略
- **内存友好**：分块加载避免OOM，虚拟化渲染提升性能
- **用户体验**：提供进度指示和用户选择权

### 3. 错误处理体系
- **全局异常捕获**：统一处理未捕获异常，提供崩溃恢复
- **分类处理**：针对不同错误类型提供相应的用户反馈
- **优雅降级**：在错误情况下提供替代方案

### 4. 权限管理适配
- **Android 13+支持**：完整适配新权限模型
- **SAF集成**：使用现代化的文件访问方式
- **智能引导**：根据权限状态提供相应的用户指导

项目整体架构清晰，代码组织良好，实现了完整的文本编辑器功能，为后续功能扩展提供了坚实的基础。
