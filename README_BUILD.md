# LingTxt 构建说明

## 修复的问题

### ✅ Gradle仓库配置问题
- **问题**：`Build was configured to prefer settings repositories over project repositories`
- **解决方案**：从`build.gradle`中移除了`allprojects`仓库配置，仓库现在统一在`settings.gradle`中配置

### ✅ Dagger/Hilt配置不一致
- **问题**：代码使用Dagger 2，但配置了Hilt插件
- **解决方案**：移除了Hilt相关配置，保持纯Dagger 2实现

## 当前项目状态

### 构建要求
- **Android Studio**: Arctic Fox (2020.3.1) 或更高版本
- **Gradle**: 7.0+ 
- **JDK**: 11 或更高版本
- **Android SDK**: API 24-34

### 构建步骤

1. **在Android Studio中打开项目**
   ```
   File -> Open -> 选择项目根目录
   ```

2. **同步项目**
   ```
   Tools -> Android -> Sync Project with Gradle Files
   ```

3. **构建项目**
   ```
   Build -> Make Project
   或使用快捷键 Ctrl+F9
   ```

4. **运行应用**
   ```
   Run -> Run 'app'
   或使用快捷键 Shift+F10
   ```

### 如果遇到Gradle Wrapper问题

如果Android Studio提示缺少gradle-wrapper.jar，可以：

1. **重新生成Wrapper**
   ```bash
   gradle wrapper
   ```

2. **或者使用Android Studio的Gradle设置**
   - File -> Settings -> Build -> Gradle
   - 选择 "Use Gradle from: 'gradle-wrapper.properties' file"

### 预期的构建结果

构建成功后，你应该能够：
- ✅ 启动应用并看到欢迎界面
- ✅ 点击"打开文件"按钮选择文本文件
- ✅ 查看文本文件内容
- ✅ 看到行号显示
- ✅ 使用底部操作栏的基本功能

### 已知限制

以下功能是占位实现，会显示"待实现"提示：
- 搜索功能
- 跳转到行
- 设置界面
- 最近文件
- 快速操作菜单

## 故障排除

### 常见问题

1. **编译错误：找不到DaggerAppComponent**
   - 解决：Build -> Clean Project，然后重新构建

2. **DataBinding错误**
   - 解决：确保在app/build.gradle中启用了dataBinding

3. **资源文件错误**
   - 解决：检查所有drawable和string资源是否存在

4. **依赖版本冲突**
   - 解决：检查app/build.gradle中的依赖版本是否兼容

### 调试建议

1. **查看构建日志**
   - View -> Tool Windows -> Build

2. **检查Gradle同步**
   - File -> Sync Project with Gradle Files

3. **清理项目**
   - Build -> Clean Project

## 下一步开发

项目当前已完成基础架构，可以继续开发：
- 任务5：文本编辑器核心组件
- 任务6：语法高亮系统
- 任务7：搜索和跳转功能

每个任务都有详细的实现计划，可以按顺序进行开发。