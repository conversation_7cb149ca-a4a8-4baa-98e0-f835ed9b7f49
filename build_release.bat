@echo off
echo ========================================
echo LingTxt Android 应用打包脚本
echo ========================================

echo.
echo 1. 清理构建缓存...
call gradlew.bat clean

echo.
echo 2. 构建Release APK...
call gradlew.bat assembleRelease

echo.
echo 3. 构建Release AAB (Google Play)...
call gradlew.bat bundleRelease

echo.
echo 4. 检查构建结果...
if exist "app\build\outputs\apk\release\app-release.apk" (
    echo ✅ APK构建成功: app\build\outputs\apk\release\app-release.apk
) else (
    echo ❌ APK构建失败
)

if exist "app\build\outputs\bundle\release\app-release.aab" (
    echo ✅ AAB构建成功: app\build\outputs\bundle\release\app-release.aab
) else (
    echo ❌ AAB构建失败
)

echo.
echo 5. 显示APK信息...
if exist "app\build\outputs\apk\release\app-release.apk" (
    echo APK文件大小:
    dir "app\build\outputs\apk\release\app-release.apk" | findstr "app-release.apk"
)

echo.
echo ========================================
echo 构建完成！
echo ========================================
pause
