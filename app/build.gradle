plugins {
    id 'com.android.application'
}

android {
    namespace 'com.lingtxt.editor'
    compileSdk 34

    defaultConfig {
        applicationId "com.lingtxt.editor"
        minSdk 24
        targetSdk 34
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        // 启用矢量图支持
        vectorDrawables.useSupportLibrary = true
    }

    // 签名配置
    signingConfigs {
        release {
            // 注意：实际使用时需要创建keystore文件
            // storeFile file('release.keystore')
            // storePassword 'your_store_password'
            // keyAlias 'your_key_alias'
            // keyPassword 'your_key_password'

            // 临时配置，使用debug签名
            storeFile file('debug.keystore')
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
        }
    }

    buildTypes {
        release {
            minifyEnabled true
            shrinkResources true  // 启用资源压缩
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'

            // APK大小优化
            zipAlignEnabled true

            // 签名配置
            signingConfig signingConfigs.release
        }
        debug {
            minifyEnabled false
            shrinkResources false
            zipAlignEnabled false
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    buildFeatures {
        viewBinding true
        dataBinding true
    }

    // Fix for JDK compatibility
    configurations.all {
        resolutionStrategy {
            force 'org.jetbrains:annotations:23.0.0'
        }
    }

    packagingOptions {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
            excludes += '/META-INF/DEPENDENCIES'
            excludes += '/META-INF/LICENSE'
            excludes += '/META-INF/LICENSE.txt'
            excludes += '/META-INF/NOTICE'
            excludes += '/META-INF/NOTICE.txt'
        }
    }

    // 启动时间优化
    bundle {
        language {
            enableSplit = true
        }
        density {
            enableSplit = true
        }
        abi {
            enableSplit = true
        }
    }
}

dependencies {
    // Android Core
    implementation 'androidx.core:core:1.12.0'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.11.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.activity:activity:1.8.2'
    implementation 'androidx.fragment:fragment:1.6.2'

    // Architecture Components
    implementation 'androidx.lifecycle:lifecycle-viewmodel:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-livedata:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-common-java8:2.7.0'

    // Dagger 2 for Dependency Injection
    implementation 'com.google.dagger:dagger:2.48'
    annotationProcessor 'com.google.dagger:dagger-compiler:2.48'

    // RxJava for Async Operations
    implementation 'io.reactivex.rxjava3:rxjava:3.1.8'
    implementation 'io.reactivex.rxjava3:rxandroid:3.0.2'

    // File Operations
    implementation 'commons-io:commons-io:2.11.0'

    // RecyclerView
    implementation 'androidx.recyclerview:recyclerview:1.3.2'

    // Testing
    testImplementation 'junit:junit:4.13.2'
    testImplementation 'org.mockito:mockito-core:5.7.0'
    testImplementation 'org.robolectric:robolectric:4.11.1'
    testImplementation 'androidx.test:core:1.5.0'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
}