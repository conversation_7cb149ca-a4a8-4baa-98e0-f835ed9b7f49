package com.lingtxt.editor.utils;

/**
 * 文件操作异常类
 */
public class FileOperationException extends Exception {

    public enum ErrorType {
        FILE_NOT_FOUND,
        ACCESS_DENIED,
        FILE_TOO_LARGE,
        ENCODING_ERROR,
        IO_ERROR,
        PERMISSION_DENIED,
        UNSUPPORTED_FORMAT,
        NETWORK_ERROR,
        STORAGE_FULL
    }

    private final ErrorType errorType;
    private final String filePath;

    public FileOperationException(ErrorType errorType, String message, Throwable cause) {
        super(message, cause);
        this.errorType = errorType;
        this.filePath = null;
    }

    /**
     * 获取用户友好的错误信息
     */
    public String getUserFriendlyMessage() {
        switch (errorType) {
            case FILE_NOT_FOUND:
                return "文件未找到或已被删除";
            case ACCESS_DENIED:
                return "没有访问文件的权限";
            case FILE_TOO_LARGE:
                return "文件过大，无法打开";
            case ENCODING_ERROR:
                return "文件编码格式不支持";
            case IO_ERROR:
                return "文件读写错误";
            case PERMISSION_DENIED:
                return "权限不足，无法执行操作";
            case UNSUPPORTED_FORMAT:
                return "不支持的文件格式";
            case NETWORK_ERROR:
                return "网络连接错误";
            case STORAGE_FULL:
                return "存储空间不足";
            default:
                return "未知错误: " + getMessage();
        }
    }

    /**
     * 根据异常类型创建FileOperationException
     */
    public static FileOperationException fromThrowable(Throwable throwable) {
        if (throwable instanceof FileOperationException) {
            return (FileOperationException) throwable;
        }

        String message = throwable.getMessage();
        if (message == null) {
            message = throwable.getClass().getSimpleName();
        }

        // 根据异常类型和消息判断错误类型
        if (throwable instanceof java.io.FileNotFoundException) {
            return new FileOperationException(ErrorType.FILE_NOT_FOUND, message, throwable);
        } else if (throwable instanceof SecurityException) {
            return new FileOperationException(ErrorType.ACCESS_DENIED, message, throwable);
        } else if (throwable instanceof java.io.IOException) {
            if (message.contains("ENOSPC") || message.contains("No space left")) {
                return new FileOperationException(ErrorType.STORAGE_FULL, message, throwable);
            } else if (message.contains("Permission denied")) {
                return new FileOperationException(ErrorType.PERMISSION_DENIED, message, throwable);
            } else {
                return new FileOperationException(ErrorType.IO_ERROR, message, throwable);
            }
        } else if (throwable instanceof java.nio.charset.UnsupportedCharsetException ||
                   throwable instanceof java.nio.charset.MalformedInputException) {
            return new FileOperationException(ErrorType.ENCODING_ERROR, message, throwable);
        } else {
            return new FileOperationException(ErrorType.IO_ERROR, message, throwable);
        }
    }
}