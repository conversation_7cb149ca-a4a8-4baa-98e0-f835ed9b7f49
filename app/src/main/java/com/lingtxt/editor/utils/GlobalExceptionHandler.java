package com.lingtxt.editor.utils;

import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Build;
import android.util.Log;
import androidx.annotation.NonNull;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * 全局异常处理器
 * 处理未捕获的异常，记录崩溃日志，并提供恢复机制
 */
public class GlobalExceptionHandler implements Thread.UncaughtExceptionHandler {

    private static final String TAG = "GlobalExceptionHandler";
    private static final String CRASH_LOG_DIR = "crash_logs";
    private static final String CRASH_PREFERENCE = "crash_preference";
    private static final String KEY_LAST_CRASH_TIME = "last_crash_time";
    private static final String KEY_CRASH_COUNT = "crash_count";
    private static final String KEY_HAS_PENDING_CRASH = "has_pending_crash";
    private static final String KEY_LAST_CRASH_LOG = "last_crash_log";
    
    // 崩溃频率限制：5分钟内超过3次崩溃则进入安全模式
    private static final long CRASH_TIME_WINDOW = 5 * 60 * 1000; // 5分钟
    private static final int MAX_CRASH_COUNT = 3;
    
    private final Context context;
    private final Thread.UncaughtExceptionHandler defaultHandler;
    private final Class<?> restartActivityClass;
    
    public GlobalExceptionHandler(@NonNull Context context, @NonNull Class<?> restartActivityClass) {
        this.context = context.getApplicationContext();
        this.defaultHandler = Thread.getDefaultUncaughtExceptionHandler();
        this.restartActivityClass = restartActivityClass;
    }
    
    /**
     * 安装全局异常处理器
     */
    public static void install(@NonNull Context context, @NonNull Class<?> restartActivityClass) {
        GlobalExceptionHandler handler = new GlobalExceptionHandler(context, restartActivityClass);
        Thread.setDefaultUncaughtExceptionHandler(handler);
        Log.i(TAG, "Global exception handler installed");
    }
    
    @Override
    public void uncaughtException(@NonNull Thread thread, @NonNull Throwable throwable) {
        Log.e(TAG, "Uncaught exception in thread " + thread.getName(), throwable);
        
        try {
            // 记录崩溃信息
            String crashLog = generateCrashLog(throwable);
            saveCrashLog(crashLog);
            updateCrashStatistics();
            
            // 保存崩溃状态，用于下次启动时检测
            markCrashOccurred(crashLog);
            
            // 尝试自动保存用户数据
            saveUserDataOnCrash();
            
        } catch (Exception e) {
            Log.e(TAG, "Error handling crash", e);
        }
        
        // 重启应用或调用默认处理器
        if (shouldRestartApp()) {
            restartApplication();
        } else {
            // 如果崩溃过于频繁，调用系统默认处理器
            if (defaultHandler != null) {
                defaultHandler.uncaughtException(thread, throwable);
            } else {
                System.exit(1);
            }
        }
    }
    
    /**
     * 生成崩溃日志
     */
    private String generateCrashLog(Throwable throwable) {
        StringBuilder log = new StringBuilder();
        
        // 时间戳
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
        log.append("Crash Time: ").append(dateFormat.format(new Date())).append("\n");
        
        // 设备信息
        log.append("Device Info:\n");
        log.append("  Brand: ").append(Build.BRAND).append("\n");
        log.append("  Model: ").append(Build.MODEL).append("\n");
        log.append("  Android Version: ").append(Build.VERSION.RELEASE).append("\n");
        log.append("  SDK Version: ").append(Build.VERSION.SDK_INT).append("\n");
        log.append("  App Version: ").append(getAppVersion()).append("\n");
        log.append("\n");
        
        // 异常堆栈
        log.append("Exception:\n");
        StringWriter stringWriter = new StringWriter();
        PrintWriter printWriter = new PrintWriter(stringWriter);
        throwable.printStackTrace(printWriter);
        log.append(stringWriter.toString());
        
        return log.toString();
    }
    
    /**
     * 保存崩溃日志到文件
     */
    private void saveCrashLog(String crashLog) {
        try {
            File crashDir = new File(context.getFilesDir(), CRASH_LOG_DIR);
            if (!crashDir.exists()) {
                crashDir.mkdirs();
            }
            
            String fileName = "crash_" + System.currentTimeMillis() + ".log";
            File crashFile = new File(crashDir, fileName);
            
            FileWriter writer = new FileWriter(crashFile);
            writer.write(crashLog);
            writer.close();
            
            Log.i(TAG, "Crash log saved to: " + crashFile.getAbsolutePath());
            
            // 清理旧的崩溃日志（保留最近10个）
            cleanupOldCrashLogs(crashDir);
            
        } catch (IOException e) {
            Log.e(TAG, "Failed to save crash log", e);
        }
    }
    
    /**
     * 清理旧的崩溃日志
     */
    private void cleanupOldCrashLogs(File crashDir) {
        File[] logFiles = crashDir.listFiles((dir, name) -> name.startsWith("crash_") && name.endsWith(".log"));
        if (logFiles != null && logFiles.length > 10) {
            // 按修改时间排序，删除最旧的文件
            java.util.Arrays.sort(logFiles, (f1, f2) -> Long.compare(f1.lastModified(), f2.lastModified()));
            for (int i = 0; i < logFiles.length - 10; i++) {
                logFiles[i].delete();
            }
        }
    }
    
    /**
     * 更新崩溃统计信息
     */
    private void updateCrashStatistics() {
        SharedPreferences prefs = context.getSharedPreferences(CRASH_PREFERENCE, Context.MODE_PRIVATE);
        long currentTime = System.currentTimeMillis();
        long lastCrashTime = prefs.getLong(KEY_LAST_CRASH_TIME, 0);
        int crashCount = prefs.getInt(KEY_CRASH_COUNT, 0);
        
        // 如果距离上次崩溃超过时间窗口，重置计数
        if (currentTime - lastCrashTime > CRASH_TIME_WINDOW) {
            crashCount = 0;
        }
        
        crashCount++;
        
        prefs.edit()
            .putLong(KEY_LAST_CRASH_TIME, currentTime)
            .putInt(KEY_CRASH_COUNT, crashCount)
            .apply();
        
        Log.i(TAG, "Crash count updated: " + crashCount);
    }
    
    /**
     * 标记崩溃发生
     */
    private void markCrashOccurred(String crashLog) {
        SharedPreferences prefs = context.getSharedPreferences(CRASH_PREFERENCE, Context.MODE_PRIVATE);
        prefs.edit()
            .putBoolean(KEY_HAS_PENDING_CRASH, true)
            .putString(KEY_LAST_CRASH_LOG, crashLog)
            .apply();
    }
    
    /**
     * 判断是否应该重启应用
     */
    private boolean shouldRestartApp() {
        SharedPreferences prefs = context.getSharedPreferences(CRASH_PREFERENCE, Context.MODE_PRIVATE);
        int crashCount = prefs.getInt(KEY_CRASH_COUNT, 0);
        return crashCount < MAX_CRASH_COUNT;
    }
    
    /**
     * 重启应用
     */
    private void restartApplication() {
        try {
            Intent intent = new Intent(context, restartActivityClass);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
            intent.putExtra("crashed", true);
            context.startActivity(intent);
            
            // 延迟退出，给新Activity启动时间
            new Thread(() -> {
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                System.exit(0);
            }).start();
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to restart application", e);
            System.exit(1);
        }
    }
    
    /**
     * 崩溃时尝试保存用户数据
     */
    private void saveUserDataOnCrash() {
        try {
            Log.i(TAG, "Attempting to save user data on crash");

            // 发送广播通知所有组件保存数据
            Intent saveDataIntent = new Intent("com.lingtxt.editor.ACTION_SAVE_DATA_ON_CRASH");
            context.sendBroadcast(saveDataIntent);

            // 给组件一些时间来保存数据
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }

            // 保存应用状态到SharedPreferences
            saveAppStateOnCrash();

            Log.i(TAG, "User data save attempt completed");

        } catch (Exception e) {
            Log.e(TAG, "Failed to save user data on crash", e);
        }
    }

    /**
     * 保存应用状态到SharedPreferences
     */
    private void saveAppStateOnCrash() {
        try {
            SharedPreferences crashPrefs = context.getSharedPreferences(CRASH_PREFERENCE, Context.MODE_PRIVATE);
            SharedPreferences.Editor editor = crashPrefs.edit();

            // 记录崩溃时的时间戳
            editor.putLong("crash_timestamp", System.currentTimeMillis());

            // 记录崩溃时的应用状态
            editor.putString("crash_thread", Thread.currentThread().getName());

            // 保存内存使用情况
            Runtime runtime = Runtime.getRuntime();
            long maxMemory = runtime.maxMemory();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;

            editor.putLong("memory_max", maxMemory);
            editor.putLong("memory_used", usedMemory);
            editor.putFloat("memory_usage_percent", (float) usedMemory / maxMemory * 100);

            editor.apply();

        } catch (Exception e) {
            Log.e(TAG, "Failed to save app state on crash", e);
        }
    }
    
    /**
     * 获取应用版本
     */
    private String getAppVersion() {
        try {
            return context.getPackageManager()
                .getPackageInfo(context.getPackageName(), 0)
                .versionName;
        } catch (Exception e) {
            return "Unknown";
        }
    }
    
    /**
     * 检查是否有待处理的崩溃
     */
    public static boolean hasPendingCrash(@NonNull Context context) {
        SharedPreferences prefs = context.getSharedPreferences(CRASH_PREFERENCE, Context.MODE_PRIVATE);
        return prefs.getBoolean(KEY_HAS_PENDING_CRASH, false);
    }
    
    /**
     * 获取最后一次崩溃日志
     */
    public static String getLastCrashLog(@NonNull Context context) {
        SharedPreferences prefs = context.getSharedPreferences(CRASH_PREFERENCE, Context.MODE_PRIVATE);
        return prefs.getString(KEY_LAST_CRASH_LOG, "");
    }
    
    /**
     * 清除崩溃状态
     */
    public static void clearCrashState(@NonNull Context context) {
        SharedPreferences prefs = context.getSharedPreferences(CRASH_PREFERENCE, Context.MODE_PRIVATE);
        prefs.edit()
            .putBoolean(KEY_HAS_PENDING_CRASH, false)
            .putString(KEY_LAST_CRASH_LOG, "")
            .apply();
    }
    
    /**
     * 获取崩溃日志目录
     */
    public static File getCrashLogDirectory(@NonNull Context context) {
        return new File(context.getFilesDir(), CRASH_LOG_DIR);
    }
}
