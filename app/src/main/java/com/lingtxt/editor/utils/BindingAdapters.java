package com.lingtxt.editor.utils;

import android.util.TypedValue;
import android.widget.TextView;
import androidx.databinding.BindingAdapter;

/**
 * 数据绑定适配器
 */
public class BindingAdapters {

    /**
     * 设置TextView的字体大小（以sp为单位）
     */
    @BindingAdapter("textSizeSp")
    public static void setTextSizeSp(TextView textView, float sizeSp) {
        if (sizeSp > 0) {
            textView.setTextSize(TypedValue.COMPLEX_UNIT_SP, sizeSp);
        }
    }

    /**
     * 设置TextView的字体大小（以dp为单位）
     */
    @BindingAdapter("textSizeDp")
    public static void setTextSizeDp(TextView textView, float sizeDp) {
        if (sizeDp > 0) {
            textView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, sizeDp);
        }
    }

    /**
     * 设置TextView的字体大小（以px为单位）
     */
    @BindingAdapter("textSizePx")
    public static void setTextSizePx(TextView textView, float sizePx) {
        if (sizePx > 0) {
            textView.setTextSize(TypedValue.COMPLEX_UNIT_PX, sizePx);
        }
    }
}