package com.lingtxt.editor.utils;

import android.app.Activity;
import android.content.Context;
import android.view.View;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.annotation.StringRes;
import androidx.appcompat.app.AlertDialog;
import com.google.android.material.snackbar.Snackbar;
import com.google.android.material.dialog.MaterialAlertDialogBuilder;

/**
 * 统一的用户反馈管理器
 * 提供一致的Toast、Snackbar、Dialog显示方式
 */
public class UserFeedbackManager {

    private static UserFeedbackManager instance;
    
    public static UserFeedbackManager getInstance() {
        if (instance == null) {
            instance = new UserFeedbackManager();
        }
        return instance;
    }
    
    private UserFeedbackManager() {}
    
    // ==================== Toast 方法 ====================
    
    public void showToast(@NonNull Context context, @NonNull String message) {
        Toast.makeText(context, message, Toast.LENGTH_SHORT).show();
    }
    
    public void showToast(@NonNull Context context, @StringRes int messageRes) {
        Toast.makeText(context, messageRes, Toast.LENGTH_SHORT).show();
    }
    
    public void showLongToast(@NonNull Context context, @NonNull String message) {
        Toast.makeText(context, message, Toast.LENGTH_LONG).show();
    }
    
    // ==================== Snackbar 方法 ====================
    
    public void showSnackbar(@NonNull View view, @NonNull String message) {
        Snackbar.make(view, message, Snackbar.LENGTH_SHORT).show();
    }
    
    public void showLongSnackbar(@NonNull View view, @NonNull String message) {
        Snackbar.make(view, message, Snackbar.LENGTH_LONG).show();
    }
    
    public void showErrorSnackbar(@NonNull View view, @NonNull String message) {
        Snackbar snackbar = Snackbar.make(view, message, Snackbar.LENGTH_LONG);
        snackbar.setBackgroundTint(view.getContext().getColor(com.google.android.material.R.color.design_default_color_error));
        snackbar.setTextColor(view.getContext().getColor(android.R.color.white));
        snackbar.show();
    }
    
    public void showSuccessSnackbar(@NonNull View view, @NonNull String message) {
        Snackbar snackbar = Snackbar.make(view, message, Snackbar.LENGTH_SHORT);
        snackbar.setBackgroundTint(view.getContext().getColor(com.google.android.material.R.color.design_default_color_primary));
        snackbar.setTextColor(view.getContext().getColor(android.R.color.white));
        snackbar.show();
    }
    
    public void showWarningSnackbar(@NonNull View view, @NonNull String message) {
        Snackbar snackbar = Snackbar.make(view, message, Snackbar.LENGTH_LONG);
        snackbar.setBackgroundTint(view.getContext().getColor(android.R.color.holo_orange_dark));
        snackbar.setTextColor(view.getContext().getColor(android.R.color.white));
        snackbar.show();
    }
    
    public void showActionSnackbar(@NonNull View view, @NonNull String message, 
                                  @NonNull String actionText, @NonNull View.OnClickListener action) {
        Snackbar snackbar = Snackbar.make(view, message, Snackbar.LENGTH_LONG);
        snackbar.setAction(actionText, action);
        snackbar.show();
    }
    
    // ==================== Dialog 方法 ====================
    
    public void showInfoDialog(@NonNull Context context, @NonNull String title, @NonNull String message) {
        new MaterialAlertDialogBuilder(context)
            .setTitle(title)
            .setMessage(message)
            .setPositiveButton(android.R.string.ok, null)
            .show();
    }

    public void showErrorDialog(@NonNull Context context, @NonNull String title, @NonNull String message) {
        new MaterialAlertDialogBuilder(context)
            .setTitle(title)
            .setMessage(message)
            .setIcon(android.R.drawable.ic_dialog_alert)
            .setPositiveButton(android.R.string.ok, null)
            .show();
    }

    public void showConfirmDialog(@NonNull Context context, @NonNull String title, @NonNull String message,
                                 @NonNull Runnable onConfirm) {
        new MaterialAlertDialogBuilder(context)
            .setTitle(title)
            .setMessage(message)
            .setPositiveButton(android.R.string.ok, (dialog, which) -> onConfirm.run())
            .setNegativeButton(android.R.string.cancel, null)
            .show();
    }
    
    public AlertDialog showCrashRecoveryDialog(@NonNull Context context, @NonNull String crashLog,
                                       @NonNull Runnable onReportCrash, @NonNull Runnable onIgnore) {
        return new MaterialAlertDialogBuilder(context)
            .setTitle(context.getString(com.lingtxt.editor.R.string.crash_recovery_title))
            .setMessage(context.getString(com.lingtxt.editor.R.string.crash_recovery_message))
            .setPositiveButton(context.getString(com.lingtxt.editor.R.string.send_crash_report), (dialog, which) -> onReportCrash.run())
            .setNegativeButton(context.getString(com.lingtxt.editor.R.string.ignore_crash), (dialog, which) -> onIgnore.run())
            .setNeutralButton(context.getString(com.lingtxt.editor.R.string.view_crash_details), (dialog, which) -> showCrashDetailsDialog(context, crashLog))
            .setCancelable(false)
            .show();
    }

    private void showCrashDetailsDialog(@NonNull Context context, @NonNull String crashLog) {
        new MaterialAlertDialogBuilder(context)
            .setTitle(context.getString(com.lingtxt.editor.R.string.crash_details_title))
            .setMessage(crashLog)
            .setPositiveButton(android.R.string.ok, null)
            .show();
    }
    
    // ==================== 权限相关对话框 ====================
    
    public void showPermissionDeniedDialog(@NonNull Context context, @NonNull String permission,
                                          @NonNull Runnable onGoToSettings) {
        String message = getPermissionDeniedMessage(context, permission);
        new MaterialAlertDialogBuilder(context)
            .setTitle(context.getString(com.lingtxt.editor.R.string.permission_denied_title))
            .setMessage(message)
            .setPositiveButton(context.getString(com.lingtxt.editor.R.string.go_to_settings), (dialog, which) -> onGoToSettings.run())
            .setNegativeButton(android.R.string.cancel, null)
            .show();
    }

    public void showPermissionRationaleDialog(@NonNull Context context, @NonNull String permission,
                                             @NonNull Runnable onRequestPermission) {
        String message = getPermissionRationaleMessage(context, permission);
        new MaterialAlertDialogBuilder(context)
            .setTitle(context.getString(com.lingtxt.editor.R.string.permission_needed_title))
            .setMessage(message)
            .setPositiveButton(context.getString(com.lingtxt.editor.R.string.grant_permission), (dialog, which) -> onRequestPermission.run())
            .setNegativeButton(android.R.string.cancel, null)
            .show();
    }
    
    private String getPermissionDeniedMessage(@NonNull Context context, String permission) {
        switch (permission) {
            case android.Manifest.permission.READ_EXTERNAL_STORAGE:
            case android.Manifest.permission.WRITE_EXTERNAL_STORAGE:
                return context.getString(com.lingtxt.editor.R.string.storage_permission_denied);
            case android.Manifest.permission.RECORD_AUDIO:
                return context.getString(com.lingtxt.editor.R.string.audio_permission_denied);
            default:
                return context.getString(com.lingtxt.editor.R.string.permission_denied_some_features_unavailable);
        }
    }

    private String getPermissionRationaleMessage(@NonNull Context context, String permission) {
        switch (permission) {
            case android.Manifest.permission.READ_EXTERNAL_STORAGE:
            case android.Manifest.permission.WRITE_EXTERNAL_STORAGE:
                return context.getString(com.lingtxt.editor.R.string.storage_permission_rationale);
            case android.Manifest.permission.RECORD_AUDIO:
                return context.getString(com.lingtxt.editor.R.string.audio_permission_rationale);
            default:
                return context.getString(com.lingtxt.editor.R.string.permission_denied_some_features_unavailable);
        }
    }
    
    // ==================== 文件操作反馈 ====================

    public void showFileOperationSuccess(@NonNull View view, @NonNull String operation) {
        String message = view.getContext().getString(com.lingtxt.editor.R.string.operation_success, operation);
        showSuccessSnackbar(view, message);
    }

    public void showFileOperationError(@NonNull View view, @NonNull String operation, @NonNull String error) {
        String message = view.getContext().getString(com.lingtxt.editor.R.string.operation_failed, operation, error);
        showErrorSnackbar(view, message);
    }

    public void showFileLoadingProgress(@NonNull View view, @NonNull String fileName) {
        String message = view.getContext().getString(com.lingtxt.editor.R.string.loading_file, fileName);
        showSnackbar(view, message);
    }

    public void showFileSavingProgress(@NonNull View view, @NonNull String fileName) {
        String message = view.getContext().getString(com.lingtxt.editor.R.string.saving_file, fileName);
        showSnackbar(view, message);
    }
    
    // ==================== 性能优化反馈 ====================

    public void showPerformanceOptimization(@NonNull View view, @NonNull String optimization) {
        String message = view.getContext().getString(com.lingtxt.editor.R.string.performance_optimization_enabled, optimization);
        showWarningSnackbar(view, message);
    }

    public void showLargeFileWarning(@NonNull View view, @NonNull String fileSize) {
        String message = view.getContext().getString(com.lingtxt.editor.R.string.large_file_warning, fileSize);
        showWarningSnackbar(view, message);
    }
    
    // ==================== 搜索和编辑反馈 ====================

    public void showSearchResult(@NonNull View view, int totalCount, int currentIndex) {
        if (totalCount == 0) {
            String message = view.getContext().getString(com.lingtxt.editor.R.string.search_no_results);
            showSnackbar(view, message);
        } else {
            String message = view.getContext().getString(com.lingtxt.editor.R.string.search_results, totalCount, currentIndex);
            showSnackbar(view, message);
        }
    }

    public void showReplaceResult(@NonNull View view, int replacedCount) {
        if (replacedCount == 0) {
            String message = view.getContext().getString(com.lingtxt.editor.R.string.replace_no_results);
            showSnackbar(view, message);
        } else {
            String message = view.getContext().getString(com.lingtxt.editor.R.string.replace_results, replacedCount);
            showSuccessSnackbar(view, message);
        }
    }

    public void showGotoLineResult(@NonNull View view, int lineNumber) {
        String message = view.getContext().getString(com.lingtxt.editor.R.string.goto_line_result, lineNumber);
        showSnackbar(view, message);
    }
    
    // ==================== 设置变更反馈 ====================

    public void showSettingChanged(@NonNull Context context, @NonNull String settingName, @NonNull String newValue) {
        String message = context.getString(com.lingtxt.editor.R.string.setting_changed, settingName, newValue);
        showToast(context, message);
    }

    public void showFeatureToggled(@NonNull View view, @NonNull String featureName, boolean enabled) {
        int messageRes = enabled ? com.lingtxt.editor.R.string.feature_enabled : com.lingtxt.editor.R.string.feature_disabled;
        String message = view.getContext().getString(messageRes, featureName);
        showSnackbar(view, message);
    }
}
