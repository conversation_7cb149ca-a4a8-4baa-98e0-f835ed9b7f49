package com.lingtxt.editor.utils;

import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.provider.DocumentsContract;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.IOException;

/**
 * 文件保存助手，处理不同权限模式下的文件保存
 */
public class FileSaveHelper {
    
    private static final String TAG = "FileSaveHelper";
    private static final int REQUEST_CODE_SAVE_FILE = 2001;
    
    /**
     * 保存文件回调接口
     */
    public interface SaveCallback {
        void onSaveSuccess();
        void onSaveError(String error);
        void onPermissionRequired();
        default void onNeedSaveAs() {
            // 默认实现：显示另存为提示
        }
    }
    
    /**
     * 尝试保存文件（智能选择保存方式）
     */
    public static void saveFile(@NonNull Activity activity, 
                               @Nullable Uri fileUri, 
                               @NonNull String content,
                               @NonNull String fileName,
                               @NonNull SaveCallback callback) {
        
        if (fileUri != null) {
            // 有文件URI，检查是否可写
            if (canWriteToUri(activity, fileUri)) {
                saveToUri(activity, fileUri, content, callback);
            } else {
                // 文件只读，提供另存为选项
                showSaveAsDialog(activity, content, fileName, callback);
            }
        } else if (PermissionManager.hasStoragePermission(activity)) {
            // 有完整权限，使用传统文件保存
            saveToExternalStorage(activity, content, fileName, callback);
        } else {
            // 没有权限，使用SAF保存
            saveWithSAF(activity, content, fileName, callback);
        }
    }
    
    /**
     * 保存到指定URI
     */
    private static void saveToUri(@NonNull Activity activity,
                                 @NonNull Uri uri,
                                 @NonNull String content,
                                 @NonNull SaveCallback callback) {
        try {
            // 先检查是否有写入权限
            if (!canWriteToUri(activity, uri)) {
                android.util.Log.w(TAG, "No write permission for URI: " + uri);
                callback.onSaveError("原文件只读，无法直接保存。请使用\"另存为\"功能。");
                return;
            }

            OutputStream outputStream = activity.getContentResolver().openOutputStream(uri);
            if (outputStream != null) {
                OutputStreamWriter writer = new OutputStreamWriter(outputStream, "UTF-8");
                writer.write(content);
                writer.close();
                outputStream.close();

                android.util.Log.d(TAG, "File saved to URI: " + uri);
                callback.onSaveSuccess();
            } else {
                callback.onSaveError("无法打开文件进行写入");
            }
        } catch (IOException e) {
            android.util.Log.e(TAG, "Error saving to URI", e);
            callback.onSaveError("保存失败: " + e.getMessage());
        } catch (SecurityException e) {
            android.util.Log.e(TAG, "Permission denied for URI", e);
            callback.onSaveError("原文件只读，无法直接保存。请使用\"另存为\"功能。");
        }
    }

    /**
     * 检查URI是否可写
     */
    private static boolean canWriteToUri(@NonNull Activity activity, @NonNull Uri uri) {
        try {
            // 尝试以写入模式打开
            OutputStream outputStream = activity.getContentResolver().openOutputStream(uri, "wa");
            if (outputStream != null) {
                outputStream.close();
                return true;
            }
        } catch (Exception e) {
            android.util.Log.d(TAG, "URI not writable: " + uri);
        }
        return false;
    }
    
    /**
     * 保存到外部存储（需要完整权限）
     */
    private static void saveToExternalStorage(@NonNull Activity activity,
                                            @NonNull String content,
                                            @NonNull String fileName,
                                            @NonNull SaveCallback callback) {
        try {
            java.io.File downloadsDir = android.os.Environment.getExternalStoragePublicDirectory(
                android.os.Environment.DIRECTORY_DOWNLOADS);
            java.io.File file = new java.io.File(downloadsDir, fileName);
            
            java.io.FileWriter writer = new java.io.FileWriter(file);
            writer.write(content);
            writer.close();
            
            android.util.Log.d(TAG, "File saved to: " + file.getAbsolutePath());
            callback.onSaveSuccess();
        } catch (IOException e) {
            android.util.Log.e(TAG, "Error saving to external storage", e);
            callback.onSaveError("保存失败: " + e.getMessage());
        } catch (SecurityException e) {
            android.util.Log.e(TAG, "Permission denied for external storage", e);
            callback.onPermissionRequired();
        }
    }
    
    /**
     * 使用SAF保存文件
     */
    private static void saveWithSAF(@NonNull Activity activity,
                                   @NonNull String content,
                                   @NonNull String fileName,
                                   @NonNull SaveCallback callback) {
        Intent intent = new Intent(Intent.ACTION_CREATE_DOCUMENT);
        intent.addCategory(Intent.CATEGORY_OPENABLE);
        intent.setType("text/plain");
        intent.putExtra(Intent.EXTRA_TITLE, fileName);
        
        try {
            activity.startActivityForResult(intent, REQUEST_CODE_SAVE_FILE);
            // 保存内容到临时变量，在onActivityResult中处理
            PendingSaveData.content = content;
            PendingSaveData.callback = callback;
        } catch (Exception e) {
            callback.onSaveError("无法打开文件选择器: " + e.getMessage());
        }
    }
    
    /**
     * 处理SAF保存结果
     */
    public static void handleSaveResult(int requestCode, int resultCode, Intent data, Activity activity) {
        if (requestCode == REQUEST_CODE_SAVE_FILE && resultCode == Activity.RESULT_OK) {
            if (data != null && data.getData() != null) {
                Uri uri = data.getData();
                if (PendingSaveData.content != null && PendingSaveData.callback != null) {
                    saveToUri(activity, uri, PendingSaveData.content, PendingSaveData.callback);
                    PendingSaveData.clear();
                }
            }
        } else if (requestCode == REQUEST_CODE_SAVE_FILE) {
            if (PendingSaveData.callback != null) {
                PendingSaveData.callback.onSaveError("用户取消保存");
                PendingSaveData.clear();
            }
        }
    }
    
    /**
     * 临时保存数据类
     */
    private static class PendingSaveData {
        static String content;
        static SaveCallback callback;
        
        static void clear() {
            content = null;
            callback = null;
        }
    }
    
    /**
     * 显示另存为对话框
     */
    private static void showSaveAsDialog(@NonNull Activity activity,
                                       @NonNull String content,
                                       @NonNull String fileName,
                                       @NonNull SaveCallback callback) {
        UserFeedbackManager.getInstance().showConfirmDialog(
            activity,
            "文件只读",
            "原文件来自其他应用，无法直接修改。\n\n是否另存为新文件？",
            () -> saveWithSAF(activity, content, fileName, callback)
        );
    }

    /**
     * 直接另存为（不显示对话框）
     */
    public static void saveAsFile(@NonNull Activity activity,
                                 @NonNull String content,
                                 @NonNull String fileName,
                                 @NonNull SaveCallback callback) {
        saveWithSAF(activity, content, fileName, callback);
    }

    /**
     * 获取保存请求码
     */
    public static int getSaveRequestCode() {
        return REQUEST_CODE_SAVE_FILE;
    }
}
