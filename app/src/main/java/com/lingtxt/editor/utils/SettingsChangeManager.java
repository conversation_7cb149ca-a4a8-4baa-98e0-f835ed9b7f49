package com.lingtxt.editor.utils;

import android.app.Activity;
import android.content.Context;
import android.content.res.Configuration;
import androidx.appcompat.app.AppCompatDelegate;
import com.lingtxt.editor.data.model.AppLanguage;
import com.lingtxt.editor.data.model.Theme;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * 设置变更管理器
 * 负责统一处理主题和语言变更，确保即时生效且不冲突
 */
public class SettingsChangeManager {

    private static SettingsChangeManager instance;
    private final List<SettingsChangeListener> listeners = new ArrayList<>();

    // 防止重复操作的标志
    private boolean isApplyingTheme = false;
    private boolean isApplyingLanguage = false;
    
    public interface SettingsChangeListener {
        void onThemeChanged(Theme theme);
        void onLanguageChanged(AppLanguage language);
        void onFontSizeChanged(float fontSize);
        void onFontFamilyChanged(String fontFamily);
        void onOtherSettingChanged(String key, Object value);
    }
    
    private SettingsChangeManager() {}
    
    public static synchronized SettingsChangeManager getInstance() {
        if (instance == null) {
            instance = new SettingsChangeManager();
        }
        return instance;
    }
    
    /**
     * 注册设置变更监听器
     */
    public void registerListener(SettingsChangeListener listener) {
        if (!listeners.contains(listener)) {
            listeners.add(listener);
        }
    }
    
    /**
     * 注销设置变更监听器
     */
    public void unregisterListener(SettingsChangeListener listener) {
        listeners.remove(listener);
    }
    
    /**
     * 应用主题变更
     */
    public void applyThemeChange(Theme theme) {
        if (isApplyingTheme) {
            android.util.Log.d("SettingsChangeManager", "Theme change already in progress, skipping");
            return;
        }

        isApplyingTheme = true;

        try {
            int nightMode;
            switch (theme) {
                case LIGHT:
                    nightMode = AppCompatDelegate.MODE_NIGHT_NO;
                    break;
                case DARK:
                    nightMode = AppCompatDelegate.MODE_NIGHT_YES;
                    break;
                case SYSTEM:
                default:
                    nightMode = AppCompatDelegate.MODE_NIGHT_FOLLOW_SYSTEM;
                    break;
            }

            // 设置全局主题模式
            AppCompatDelegate.setDefaultNightMode(nightMode);

            // 延迟通知监听器，确保主题应用完成
            android.os.Handler mainHandler = new android.os.Handler(android.os.Looper.getMainLooper());
            mainHandler.postDelayed(() -> {
                // 通知所有监听器
                for (SettingsChangeListener listener : listeners) {
                    try {
                        listener.onThemeChanged(theme);
                    } catch (Exception e) {
                        android.util.Log.e("SettingsChangeManager", "Error notifying theme change", e);
                    }
                }
                isApplyingTheme = false;
            }, 50); // 延迟50ms确保主题应用完成

        } catch (Exception e) {
            android.util.Log.e("SettingsChangeManager", "Error applying theme change", e);
            isApplyingTheme = false;
        }
    }
    
    /**
     * 应用语言变更
     */
    public void applyLanguageChange(Context context, AppLanguage language) {
        if (isApplyingLanguage) {
            android.util.Log.d("SettingsChangeManager", "Language change already in progress, skipping");
            return;
        }

        isApplyingLanguage = true;
        Locale locale;
        switch (language) {
            case CHINESE:
                locale = Locale.SIMPLIFIED_CHINESE;
                break;
            case ENGLISH:
                locale = Locale.ENGLISH;
                break;
            case SYSTEM:
            default:
                locale = Locale.getDefault();
                break;
        }

        android.util.Log.d("SettingsChangeManager", "Applying language change to: " + language + ", locale: " + locale);

        // 1. 首先更新Application级别的语言配置
        if (context != null) {
            Context appContext = context.getApplicationContext();
            Configuration appConfig = new Configuration(appContext.getResources().getConfiguration());
            appConfig.setLocale(locale);
            appContext.getResources().updateConfiguration(appConfig, appContext.getResources().getDisplayMetrics());
            android.util.Log.d("SettingsChangeManager", "Updated application context language");
        }

        // 2. 更新当前Activity的语言配置
        if (context instanceof Activity) {
            Activity activity = (Activity) context;
            Configuration activityConfig = new Configuration(activity.getResources().getConfiguration());
            activityConfig.setLocale(locale);
            activity.getResources().updateConfiguration(activityConfig, activity.getResources().getDisplayMetrics());
            android.util.Log.d("SettingsChangeManager", "Updated activity context language");
        }

        // 3. 延迟通知监听器，确保配置已完全应用
        android.os.Handler mainHandler = new android.os.Handler(android.os.Looper.getMainLooper());
        mainHandler.postDelayed(() -> {
            android.util.Log.d("SettingsChangeManager", "Notifying language change listeners");
            for (SettingsChangeListener listener : listeners) {
                try {
                    listener.onLanguageChanged(language);
                } catch (Exception e) {
                    android.util.Log.e("SettingsChangeManager", "Error notifying language change", e);
                }
            }
            isApplyingLanguage = false;
        }, 100); // 延迟100ms确保配置应用完成
    }
    
    /**
     * 应用字体大小变更
     */
    public void applyFontSizeChange(float fontSize) {
        for (SettingsChangeListener listener : listeners) {
            try {
                listener.onFontSizeChanged(fontSize);
            } catch (Exception e) {
                android.util.Log.e("SettingsChangeManager", "Error notifying font size change", e);
            }
        }
    }
    
    /**
     * 应用字体族变更
     */
    public void applyFontFamilyChange(String fontFamily) {
        for (SettingsChangeListener listener : listeners) {
            try {
                listener.onFontFamilyChanged(fontFamily);
            } catch (Exception e) {
                android.util.Log.e("SettingsChangeManager", "Error notifying font family change", e);
            }
        }
    }
    
    /**
     * 应用其他设置变更
     */
    public void applyOtherSettingChange(String key, Object value) {
        for (SettingsChangeListener listener : listeners) {
            try {
                listener.onOtherSettingChanged(key, value);
            } catch (Exception e) {
                android.util.Log.e("SettingsChangeManager", "Error notifying other setting change", e);
            }
        }
    }
    
    /**
     * 检查是否正在应用变更
     */
    public boolean isApplyingChanges() {
        return isApplyingTheme || isApplyingLanguage;
    }

    /**
     * 清理所有监听器
     */
    public void clearListeners() {
        listeners.clear();
    }
}
