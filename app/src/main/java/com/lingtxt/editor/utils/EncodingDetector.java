package com.lingtxt.editor.utils;

import android.content.Context;
import android.net.Uri;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

/**
 * 文件编码检测工具类
 */
public class EncodingDetector {

    private static final int BUFFER_SIZE = 8192;
    private static final byte[] UTF8_BOM = {(byte) 0xEF, (byte) 0xBB, (byte) 0xBF};
    private static final byte[] UTF16_BE_BOM = {(byte) 0xFE, (byte) 0xFF};
    private static final byte[] UTF16_LE_BOM = {(byte) 0xFF, (byte) 0xFE};
    private static final byte[] UTF32_BE_BOM = {(byte) 0x00, (byte) 0x00, (byte) 0xFE, (byte) 0xFF};
    private static final byte[] UTF32_LE_BOM = {(byte) 0xFF, (byte) 0xFE, (byte) 0x00, (byte) 0x00};

    /**
     * 检测文件编码
     */
    @NonNull
    public static String detectEncoding(@NonNull Context context, @NonNull Uri uri) {
        try (InputStream inputStream = context.getContentResolver().openInputStream(uri);
             BufferedInputStream bufferedStream = new BufferedInputStream(inputStream, BUFFER_SIZE)) {
            
            // 读取文件头部字节用于检测
            byte[] buffer = new byte[BUFFER_SIZE];
            bufferedStream.mark(BUFFER_SIZE);
            int bytesRead = bufferedStream.read(buffer);
            bufferedStream.reset();
            
            if (bytesRead <= 0) {
                return StandardCharsets.UTF_8.name();
            }
            
            // 检测BOM
            String bomEncoding = detectBOM(buffer, bytesRead);
            if (bomEncoding != null) {
                return bomEncoding;
            }
            
            // 检测UTF-8
            if (isValidUTF8(buffer, bytesRead)) {
                return StandardCharsets.UTF_8.name();
            }
            
            // 检测ASCII
            if (isASCII(buffer, bytesRead)) {
                return StandardCharsets.US_ASCII.name();
            }
            
            // 检测UTF-16
            if (isValidUTF16(buffer, bytesRead)) {
                return StandardCharsets.UTF_16.name();
            }
            
            // 默认返回UTF-8
            return StandardCharsets.UTF_8.name();
            
        } catch (IOException e) {
            // 出错时返回默认编码
            return StandardCharsets.UTF_8.name();
        }
    }

    /**
     * 检测BOM（Byte Order Mark）
     */
    @Nullable
    private static String detectBOM(@NonNull byte[] buffer, int length) {
        if (length >= 4 && startsWith(buffer, UTF32_BE_BOM)) {
            return "UTF-32BE";
        }
        if (length >= 4 && startsWith(buffer, UTF32_LE_BOM)) {
            return "UTF-32LE";
        }
        if (length >= 3 && startsWith(buffer, UTF8_BOM)) {
            return StandardCharsets.UTF_8.name();
        }
        if (length >= 2 && startsWith(buffer, UTF16_BE_BOM)) {
            return StandardCharsets.UTF_16BE.name();
        }
        if (length >= 2 && startsWith(buffer, UTF16_LE_BOM)) {
            return StandardCharsets.UTF_16LE.name();
        }
        return null;
    }

    /**
     * 检查字节数组是否以指定的字节序列开头
     */
    private static boolean startsWith(@NonNull byte[] buffer, @NonNull byte[] prefix) {
        if (buffer.length < prefix.length) {
            return false;
        }
        for (int i = 0; i < prefix.length; i++) {
            if (buffer[i] != prefix[i]) {
                return false;
            }
        }
        return true;
    }

    /**
     * 检测是否为有效的UTF-8编码
     */
    private static boolean isValidUTF8(@NonNull byte[] buffer, int length) {
        int i = 0;
        while (i < length) {
            byte b = buffer[i];
            
            // ASCII字符 (0xxxxxxx)
            if ((b & 0x80) == 0) {
                i++;
                continue;
            }
            
            // 多字节UTF-8字符
            int bytesToRead = 0;
            if ((b & 0xE0) == 0xC0) {
                // 2字节字符 (110xxxxx 10xxxxxx)
                bytesToRead = 1;
            } else if ((b & 0xF0) == 0xE0) {
                // 3字节字符 (1110xxxx 10xxxxxx 10xxxxxx)
                bytesToRead = 2;
            } else if ((b & 0xF8) == 0xF0) {
                // 4字节字符 (11110xxx 10xxxxxx 10xxxxxx 10xxxxxx)
                bytesToRead = 3;
            } else {
                // 无效的UTF-8起始字节
                return false;
            }
            
            // 检查后续字节
            if (i + bytesToRead >= length) {
                // 字节不足，可能是文件末尾的不完整字符
                break;
            }
            
            for (int j = 1; j <= bytesToRead; j++) {
                if ((buffer[i + j] & 0xC0) != 0x80) {
                    // 后续字节不符合10xxxxxx格式
                    return false;
                }
            }
            
            i += bytesToRead + 1;
        }
        
        return true;
    }

    /**
     * 检测是否为ASCII编码
     */
    private static boolean isASCII(@NonNull byte[] buffer, int length) {
        for (int i = 0; i < length; i++) {
            if ((buffer[i] & 0x80) != 0) {
                return false;
            }
        }
        return true;
    }

    /**
     * 检测是否为有效的UTF-16编码
     */
    private static boolean isValidUTF16(@NonNull byte[] buffer, int length) {
        // UTF-16需要偶数个字节
        if (length % 2 != 0) {
            return false;
        }
        
        // 简单检测：查看是否有过多的null字节（可能表示UTF-16）
        int nullCount = 0;
        for (int i = 0; i < length; i++) {
            if (buffer[i] == 0) {
                nullCount++;
            }
        }
        
        // 如果null字节占比较高，可能是UTF-16
        return nullCount > length * 0.1;
    }

    /**
     * 验证指定编码是否可用
     */
    public static boolean isEncodingSupported(@NonNull String encoding) {
        try {
            return Charset.isSupported(encoding);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取编码的显示名称
     */
    @NonNull
    public static String getEncodingDisplayName(@NonNull String encoding) {
        switch (encoding.toUpperCase()) {
            case "UTF-8":
                return "UTF-8";
            case "UTF-16":
                return "UTF-16";
            case "UTF-16BE":
                return "UTF-16 BE";
            case "UTF-16LE":
                return "UTF-16 LE";
            case "UTF-32":
                return "UTF-32";
            case "UTF-32BE":
                return "UTF-32 BE";
            case "UTF-32LE":
                return "UTF-32 LE";
            case "US-ASCII":
                return "ASCII";
            case "ISO-8859-1":
                return "Latin-1";
            case "GBK":
                return "GBK";
            case "GB2312":
                return "GB2312";
            case "BIG5":
                return "Big5";
            default:
                return encoding;
        }
    }
}