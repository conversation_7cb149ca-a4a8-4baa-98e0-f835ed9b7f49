package com.lingtxt.editor.utils;

import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.provider.DocumentsContract;
import android.provider.OpenableColumns;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * 文件信息获取工具类
 */
public class FileInfoHelper {
    
    private static final String TAG = "FileInfoHelper";
    
    /**
     * 文件信息数据类
     */
    public static class FileInfo {
        public String fileName = "未知";
        public String filePath = "未知";
        public String fileSize = "未知";
        public String createTime = "未知";
        public String modifyTime = "未知";
        public String permissions = "未知";
        public String encoding = "未知";
        public String mimeType = "未知";
    }
    
    /**
     * 获取文件详细信息
     */
    public static FileInfo getFileInfo(@NonNull Context context, @Nullable Uri uri) {
        FileInfo info = new FileInfo();
        
        if (uri == null) {
            return info;
        }
        
        try {
            // 获取基本信息
            getBasicInfo(context, uri, info);
            
            // 获取时间信息
            getTimeInfo(context, uri, info);
            
            // 获取权限信息
            getPermissionInfo(context, uri, info);
            
            // 获取编码信息
            getEncodingInfo(context, uri, info);
            
        } catch (Exception e) {
            android.util.Log.e(TAG, "Error getting file info", e);
        }
        
        return info;
    }
    
    /**
     * 获取基本信息（文件名、路径、大小、MIME类型）
     */
    private static void getBasicInfo(@NonNull Context context, @NonNull Uri uri, @NonNull FileInfo info) {
        try {
            Cursor cursor = context.getContentResolver().query(uri, null, null, null, null);
            if (cursor != null) {
                if (cursor.moveToFirst()) {
                    // 文件名
                    int nameIndex = cursor.getColumnIndex(OpenableColumns.DISPLAY_NAME);
                    if (nameIndex >= 0) {
                        info.fileName = cursor.getString(nameIndex);
                    }
                    
                    // 文件大小
                    int sizeIndex = cursor.getColumnIndex(OpenableColumns.SIZE);
                    if (sizeIndex >= 0) {
                        long size = cursor.getLong(sizeIndex);
                        info.fileSize = FileUtils.formatFileSize(size);
                    }
                }
                cursor.close();
            }
            
            // 文件路径
            info.filePath = getDisplayPath(uri);
            
            // MIME类型
            info.mimeType = context.getContentResolver().getType(uri);
            if (info.mimeType == null) {
                info.mimeType = "未知";
            }
            
        } catch (Exception e) {
            android.util.Log.e(TAG, "Error getting basic info", e);
        }
    }
    
    /**
     * 获取时间信息
     */
    private static void getTimeInfo(@NonNull Context context, @NonNull Uri uri, @NonNull FileInfo info) {
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
            
            if (DocumentsContract.isDocumentUri(context, uri)) {
                // Document URI
                Cursor cursor = context.getContentResolver().query(
                    uri,
                    new String[]{
                        DocumentsContract.Document.COLUMN_LAST_MODIFIED
                    },
                    null, null, null
                );
                
                if (cursor != null) {
                    if (cursor.moveToFirst()) {
                        int modifiedIndex = cursor.getColumnIndex(DocumentsContract.Document.COLUMN_LAST_MODIFIED);
                        if (modifiedIndex >= 0) {
                            long lastModified = cursor.getLong(modifiedIndex);
                            if (lastModified > 0) {
                                info.modifyTime = dateFormat.format(new Date(lastModified));
                                // 创建时间通常等于修改时间（如果没有其他信息）
                                info.createTime = info.modifyTime;
                            }
                        }
                    }
                    cursor.close();
                }
            } else if ("file".equals(uri.getScheme())) {
                // File URI
                File file = new File(uri.getPath());
                if (file.exists()) {
                    long lastModified = file.lastModified();
                    if (lastModified > 0) {
                        info.modifyTime = dateFormat.format(new Date(lastModified));
                        info.createTime = info.modifyTime; // 文件系统通常不提供创建时间
                    }
                }
            }
            
        } catch (Exception e) {
            android.util.Log.e(TAG, "Error getting time info", e);
        }
    }
    
    /**
     * 获取权限信息
     */
    private static void getPermissionInfo(@NonNull Context context, @NonNull Uri uri, @NonNull FileInfo info) {
        try {
            boolean canRead = false;
            boolean canWrite = false;
            
            // 检查读取权限
            try {
                context.getContentResolver().openInputStream(uri);
                canRead = true;
            } catch (Exception e) {
                // 无读取权限
            }
            
            // 检查写入权限
            try {
                context.getContentResolver().openOutputStream(uri, "wa");
                canWrite = true;
            } catch (Exception e) {
                // 无写入权限
            }
            
            if (canRead && canWrite) {
                info.permissions = "可读写";
            } else if (canRead) {
                info.permissions = "只读";
            } else {
                info.permissions = "无权限";
            }
            
        } catch (Exception e) {
            android.util.Log.e(TAG, "Error getting permission info", e);
            info.permissions = "未知";
        }
    }
    
    /**
     * 获取编码信息
     */
    private static void getEncodingInfo(@NonNull Context context, @NonNull Uri uri, @NonNull FileInfo info) {
        try {
            // 使用现有的编码检测工具
            String encoding = EncodingDetector.detectEncoding(context, uri);
            info.encoding = encoding != null ? encoding : "未知";
        } catch (Exception e) {
            android.util.Log.e(TAG, "Error getting encoding info", e);
            info.encoding = "未知";
        }
    }
    
    /**
     * 获取显示用的路径
     */
    private static String getDisplayPath(@NonNull Uri uri) {
        String scheme = uri.getScheme();
        
        if ("file".equals(scheme)) {
            return uri.getPath();
        } else if ("content".equals(scheme)) {
            String authority = uri.getAuthority();
            if (authority != null) {
                if (authority.contains("com.android.externalstorage")) {
                    return "外部存储";
                } else if (authority.contains("downloads")) {
                    return "下载文件夹";
                } else if (authority.contains("media")) {
                    return "媒体文件夹";
                } else {
                    return authority + " 提供的文件";
                }
            }
            return "内容提供者文件";
        } else {
            return uri.toString();
        }
    }
}
