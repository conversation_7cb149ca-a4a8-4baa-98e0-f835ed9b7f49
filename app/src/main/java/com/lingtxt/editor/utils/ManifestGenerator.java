package com.lingtxt.editor.utils;

import com.lingtxt.editor.config.SupportedFileTypes;

/**
 * AndroidManifest配置生成工具
 * 注意：这个类主要用于开发时生成配置，实际的AndroidManifest.xml需要手动更新
 */
public class ManifestGenerator {

    /**
     * 生成文件关联的intent-filter XML配置
     * 开发者可以复制这个输出到AndroidManifest.xml中
     */
    public static String generateFileAssociationXml() {
        StringBuilder xml = new StringBuilder();
        
        xml.append("<!-- 文件关联 - 特定扩展名 -->\n");
        xml.append("<intent-filter>\n");
        xml.append("    <action android:name=\"android.intent.action.VIEW\" />\n");
        xml.append("    <category android:name=\"android.intent.category.DEFAULT\" />\n");
        xml.append("    <category android:name=\"android.intent.category.BROWSABLE\" />\n");
        xml.append("    <data android:scheme=\"file\" />\n");
        
        String[] extensions = SupportedFileTypes.getPrimaryExtensions();
        for (String ext : extensions) {
            xml.append("    <data android:pathPattern=\".*\\\\.").append(ext).append("\" />\n");
        }
        
        xml.append("</intent-filter>");
        
        return xml.toString();
    }

    /**
     * 生成支持的文件类型列表（用于文档）
     */
    public static String generateSupportedFilesList() {
        StringBuilder list = new StringBuilder();
        list.append("支持的文件类型：\n\n");
        
        String[] extensions = SupportedFileTypes.getPrimaryExtensions();
        for (String ext : extensions) {
            String displayName = SupportedFileTypes.getFileTypeDisplayName("test." + ext);
            list.append("- .").append(ext).append(" (").append(displayName).append(")\n");
        }
        
        return list.toString();
    }
}