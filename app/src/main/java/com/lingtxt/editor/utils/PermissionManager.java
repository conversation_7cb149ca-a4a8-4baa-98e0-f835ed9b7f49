package com.lingtxt.editor.utils;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.Settings;
import android.util.Log;
import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import java.util.ArrayList;
import java.util.List;

/**
 * 权限管理器
 * 处理动态权限请求、检查和权限被拒绝后的处理
 */
public class PermissionManager {

    private static final String TAG = "PermissionManager";

    public static final int REQUEST_CODE_STORAGE_PERMISSION = 1001;
    public static final int REQUEST_CODE_AUDIO_PERMISSION = 1002;
    public static final int REQUEST_CODE_ALL_PERMISSIONS = 1003;
    
    // 存储权限 - Android 12 及以下
    public static final String[] LEGACY_STORAGE_PERMISSIONS = {
        Manifest.permission.READ_EXTERNAL_STORAGE,
        Manifest.permission.WRITE_EXTERNAL_STORAGE
    };

    // 媒体权限 - Android 13+
    public static final String[] MEDIA_PERMISSIONS = {
        Manifest.permission.READ_MEDIA_IMAGES,
        Manifest.permission.READ_MEDIA_VIDEO,
        Manifest.permission.READ_MEDIA_AUDIO
    };
    
    // 音频权限（为语音功能预留）
    public static final String[] AUDIO_PERMISSIONS = {
        Manifest.permission.RECORD_AUDIO
    };
    
    /**
     * 权限检查结果回调
     */
    public interface PermissionCallback {
        void onPermissionGranted();
        void onPermissionDenied(String[] deniedPermissions);
        void onPermissionPermanentlyDenied(String[] permanentlyDeniedPermissions);
    }
    
    /**
     * 检查是否有存储权限
     */
    public static boolean hasStoragePermission(@NonNull Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11+ 优先检查 MANAGE_EXTERNAL_STORAGE 权限
            if (Environment.isExternalStorageManager()) {
                return true;
            }

            // 如果没有管理所有文件权限，检查媒体权限（Android 13+）
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                // 至少需要一个媒体权限
                for (String permission : MEDIA_PERMISSIONS) {
                    if (ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED) {
                        return true;
                    }
                }
                return false;
            } else {
                // Android 11-12 检查传统存储权限
                return ContextCompat.checkSelfPermission(context, Manifest.permission.READ_EXTERNAL_STORAGE)
                    == PackageManager.PERMISSION_GRANTED;
            }
        } else {
            // Android 10 及以下检查传统存储权限
            for (String permission : LEGACY_STORAGE_PERMISSIONS) {
                if (ContextCompat.checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED) {
                    return false;
                }
            }
            return true;
        }
    }
    
    /**
     * 检查是否有音频权限
     */
    public static boolean hasAudioPermission(@NonNull Context context) {
        for (String permission : AUDIO_PERMISSIONS) {
            if (ContextCompat.checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * 请求存储权限
     */
    public static void requestStoragePermission(@NonNull Activity activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11+ 优先请求管理所有文件权限
            if (!Environment.isExternalStorageManager()) {
                requestManageExternalStoragePermission(activity);
                return;
            }
        }

        // 请求常规权限
        List<String> permissionsToRequest = new ArrayList<>();

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ 请求媒体权限
            for (String permission : MEDIA_PERMISSIONS) {
                if (ContextCompat.checkSelfPermission(activity, permission) != PackageManager.PERMISSION_GRANTED) {
                    permissionsToRequest.add(permission);
                }
            }
        } else {
            // Android 12 及以下请求传统存储权限
            for (String permission : LEGACY_STORAGE_PERMISSIONS) {
                if (ContextCompat.checkSelfPermission(activity, permission) != PackageManager.PERMISSION_GRANTED) {
                    permissionsToRequest.add(permission);
                }
            }
        }

        if (!permissionsToRequest.isEmpty()) {
            ActivityCompat.requestPermissions(activity,
                permissionsToRequest.toArray(new String[0]),
                REQUEST_CODE_STORAGE_PERMISSION);
        }
    }

    /**
     * 请求管理外部存储权限 (Android 11+)
     */
    public static void requestManageExternalStoragePermission(@NonNull Activity activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            try {
                Intent intent = new Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION);
                Uri uri = Uri.fromParts("package", activity.getPackageName(), null);
                intent.setData(uri);
                activity.startActivity(intent);
            } catch (Exception e) {
                // 如果无法打开特定设置页面，打开通用设置
                Intent intent = new Intent(Settings.ACTION_MANAGE_ALL_FILES_ACCESS_PERMISSION);
                activity.startActivity(intent);
            }
        }
    }
    
    /**
     * 请求音频权限
     */
    public static void requestAudioPermission(@NonNull Activity activity) {
        List<String> permissionsToRequest = new ArrayList<>();
        for (String permission : AUDIO_PERMISSIONS) {
            if (ContextCompat.checkSelfPermission(activity, permission) != PackageManager.PERMISSION_GRANTED) {
                permissionsToRequest.add(permission);
            }
        }
        
        if (!permissionsToRequest.isEmpty()) {
            ActivityCompat.requestPermissions(activity, 
                permissionsToRequest.toArray(new String[0]), 
                REQUEST_CODE_AUDIO_PERMISSION);
        }
    }
    
    /**
     * 检查权限请求结果
     */
    public static void handlePermissionResult(@NonNull Activity activity, int requestCode, 
                                            @NonNull String[] permissions, @NonNull int[] grantResults,
                                            @NonNull PermissionCallback callback) {
        
        List<String> deniedPermissions = new ArrayList<>();
        List<String> permanentlyDeniedPermissions = new ArrayList<>();
        
        for (int i = 0; i < permissions.length; i++) {
            if (grantResults[i] != PackageManager.PERMISSION_GRANTED) {
                if (ActivityCompat.shouldShowRequestPermissionRationale(activity, permissions[i])) {
                    deniedPermissions.add(permissions[i]);
                } else {
                    permanentlyDeniedPermissions.add(permissions[i]);
                }
            }
        }
        
        if (deniedPermissions.isEmpty() && permanentlyDeniedPermissions.isEmpty()) {
            callback.onPermissionGranted();
        } else if (!permanentlyDeniedPermissions.isEmpty()) {
            callback.onPermissionPermanentlyDenied(permanentlyDeniedPermissions.toArray(new String[0]));
        } else {
            callback.onPermissionDenied(deniedPermissions.toArray(new String[0]));
        }
    }
    
    /**
     * 检查是否应该显示权限说明
     */
    public static boolean shouldShowPermissionRationale(@NonNull Activity activity, @NonNull String permission) {
        return ActivityCompat.shouldShowRequestPermissionRationale(activity, permission);
    }
    
    /**
     * 打开应用设置页面
     */
    public static void openAppSettings(@NonNull Context context) {
        Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
        Uri uri = Uri.fromParts("package", context.getPackageName(), null);
        intent.setData(uri);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);
    }
    
    /**
     * 检查并请求必要权限（文本编辑器需要写入权限）
     */
    public static void checkAndRequestEssentialPermissions(@NonNull Activity activity,
                                                          @NonNull PermissionCallback callback) {
        // 文本编辑器需要写入权限，优先检查完整权限
        if (hasStoragePermission(activity)) {
            callback.onPermissionGranted();
        } else if (hasBasicStoragePermission(activity)) {
            // 有基础权限但没有写入权限，提示升级权限
            UserFeedbackManager.getInstance().showConfirmDialog(
                activity,
                "需要文件写入权限",
                "当前只有读取权限，无法保存文件。\n\n文本编辑器需要写入权限才能保存修改。是否授权完整文件访问权限？",
                () -> requestStoragePermission(activity)
            );
        } else {
            // 没有任何权限，显示权限说明
            UserFeedbackManager.getInstance().showPermissionRationaleDialog(
                activity,
                Manifest.permission.READ_EXTERNAL_STORAGE,
                () -> requestStoragePermission(activity)
            );
        }
    }

    /**
     * 检查基础存储权限（不包括管理所有文件）
     */
    public static boolean hasBasicStoragePermission(@NonNull Context context) {
        Log.d(TAG, "Checking basic storage permission, Android version: " + Build.VERSION.SDK_INT);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ 优先检查媒体权限
            boolean hasMediaPermission = false;
            for (String permission : MEDIA_PERMISSIONS) {
                try {
                    int result = ContextCompat.checkSelfPermission(context, permission);
                    Log.d(TAG, "Permission " + permission + " result: " + (result == PackageManager.PERMISSION_GRANTED ? "GRANTED" : "DENIED"));
                    if (result == PackageManager.PERMISSION_GRANTED) {
                        hasMediaPermission = true;
                        Log.d(TAG, "Basic storage permission granted via: " + permission);
                        break;
                    }
                } catch (Exception e) {
                    Log.w(TAG, "Permission not available: " + permission);
                }
            }

            // 如果没有媒体权限，检查传统权限作为回退
            if (!hasMediaPermission) {
                boolean legacyGranted = ContextCompat.checkSelfPermission(context, Manifest.permission.READ_EXTERNAL_STORAGE)
                    == PackageManager.PERMISSION_GRANTED;
                Log.d(TAG, "Fallback READ external storage permission: " + (legacyGranted ? "GRANTED" : "DENIED"));
                return legacyGranted;
            }

            return hasMediaPermission;
        } else {
            // Android 12 及以下检查传统权限
            boolean granted = ContextCompat.checkSelfPermission(context, Manifest.permission.READ_EXTERNAL_STORAGE)
                == PackageManager.PERMISSION_GRANTED;
            Log.d(TAG, "READ_EXTERNAL_STORAGE permission: " + (granted ? "GRANTED" : "DENIED"));
            return granted;
        }
    }

    /**
     * 请求基础存储权限（不包括管理所有文件）
     */
    public static void requestBasicStoragePermission(@NonNull Activity activity) {
        List<String> permissionsToRequest = new ArrayList<>();

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ 请求媒体权限，但只请求存在的权限
            for (String permission : MEDIA_PERMISSIONS) {
                try {
                    // 检查权限是否存在于系统中
                    if (ContextCompat.checkSelfPermission(activity, permission) != PackageManager.PERMISSION_GRANTED) {
                        permissionsToRequest.add(permission);
                    }
                } catch (Exception e) {
                    Log.w(TAG, "Permission not available: " + permission);
                }
            }

            // 如果没有可用的媒体权限，回退到传统权限
            if (permissionsToRequest.isEmpty()) {
                Log.d(TAG, "No media permissions available, falling back to READ external storage");
                if (ContextCompat.checkSelfPermission(activity, Manifest.permission.READ_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
                    permissionsToRequest.add(Manifest.permission.READ_EXTERNAL_STORAGE);
                }
            }
        } else {
            // Android 12 及以下请求传统存储权限
            if (ContextCompat.checkSelfPermission(activity, Manifest.permission.READ_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
                permissionsToRequest.add(Manifest.permission.READ_EXTERNAL_STORAGE);
            }
        }

        if (!permissionsToRequest.isEmpty()) {
            Log.d(TAG, "Requesting permissions: " + permissionsToRequest);
            ActivityCompat.requestPermissions(activity,
                permissionsToRequest.toArray(new String[0]),
                REQUEST_CODE_STORAGE_PERMISSION);
        } else {
            Log.d(TAG, "All required permissions already granted");
        }
    }
    
    /**
     * 处理权限被永久拒绝的情况
     */
    public static void handlePermanentlyDeniedPermissions(@NonNull Activity activity, 
                                                         @NonNull String[] permissions) {
        for (String permission : permissions) {
            UserFeedbackManager.getInstance().showPermissionDeniedDialog(
                activity, 
                permission,
                () -> openAppSettings(activity)
            );
            break; // 只显示第一个权限的对话框
        }
    }
    
    /**
     * 获取权限的友好名称
     */
    public static String getPermissionName(@NonNull String permission) {
        switch (permission) {
            case Manifest.permission.READ_EXTERNAL_STORAGE:
                return "文件读取权限";
            case Manifest.permission.WRITE_EXTERNAL_STORAGE:
                return "文件写入权限";
            case Manifest.permission.RECORD_AUDIO:
                return "录音权限";
            default:
                return "未知权限";
        }
    }
    
    /**
     * 检查是否为关键权限
     */
    public static boolean isCriticalPermission(@NonNull String permission) {
        return permission.equals(Manifest.permission.READ_EXTERNAL_STORAGE) ||
               permission.equals(Manifest.permission.WRITE_EXTERNAL_STORAGE);
    }
    
    /**
     * 获取所有需要的权限
     */
    public static String[] getAllRequiredPermissions() {
        List<String> allPermissions = new ArrayList<>();

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ 添加媒体权限
            for (String permission : MEDIA_PERMISSIONS) {
                allPermissions.add(permission);
            }
        } else {
            // Android 12 及以下添加传统存储权限
            for (String permission : LEGACY_STORAGE_PERMISSIONS) {
                allPermissions.add(permission);
            }
        }

        // 音频权限是可选的，不添加到必需权限中

        return allPermissions.toArray(new String[0]);
    }
    
    /**
     * 检查所有必需权限是否已授权
     */
    public static boolean hasAllRequiredPermissions(@NonNull Context context) {
        String[] requiredPermissions = getAllRequiredPermissions();
        for (String permission : requiredPermissions) {
            if (ContextCompat.checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED) {
                return false;
            }
        }
        return true;
    }
}
