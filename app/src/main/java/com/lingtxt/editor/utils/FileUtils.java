package com.lingtxt.editor.utils;

import android.content.Context;
import android.net.Uri;
import android.webkit.MimeTypeMap;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import java.text.DecimalFormat;
import java.util.Locale;

/**
 * 文件操作工具类
 */
public class FileUtils {

    private static final String[] SIZE_UNITS = {"B", "KB", "MB", "GB", "TB"};
    private static final DecimalFormat SIZE_FORMAT = new DecimalFormat("#,##0.#");

    /**
     * 格式化文件大小
     */
    @NonNull
    public static String formatFileSize(long bytes) {
        if (bytes <= 0) return "0 B";
        
        int unitIndex = 0;
        double size = bytes;
        
        while (size >= 1024 && unitIndex < SIZE_UNITS.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return SIZE_FORMAT.format(size) + " " + SIZE_UNITS[unitIndex];
    }

    /**
     * 从URI获取文件名
     */
    @NonNull
    public static String getFileNameFromUri(@NonNull Uri uri) {
        String path = uri.getPath();
        if (path != null) {
            int lastSlash = path.lastIndexOf('/');
            if (lastSlash != -1 && lastSlash < path.length() - 1) {
                return path.substring(lastSlash + 1);
            }
        }
        
        // 如果无法从路径获取，尝试从URI的最后一段获取
        String lastSegment = uri.getLastPathSegment();
        if (lastSegment != null && !lastSegment.isEmpty()) {
            return lastSegment;
        }
        
        return "未知文件";
    }

    /**
     * 获取文件扩展名
     */
    @Nullable
    public static String getFileExtension(@NonNull String fileName) {
        int lastDot = fileName.lastIndexOf('.');
        if (lastDot > 0 && lastDot < fileName.length() - 1) {
            return fileName.substring(lastDot + 1).toLowerCase(Locale.ROOT);
        }
        return null;
    }

    /**
     * 根据文件扩展名获取MIME类型
     */
    @Nullable
    public static String getMimeTypeFromExtension(@NonNull String extension) {
        return MimeTypeMap.getSingleton().getMimeTypeFromExtension(extension.toLowerCase(Locale.ROOT));
    }

    /**
     * 判断是否为文本文件
     */
    public static boolean isTextFile(@NonNull String fileName, @Nullable String mimeType) {
        // 首先检查MIME类型
        if (mimeType != null && mimeType.startsWith("text/")) {
            return true;
        }
        
        // 使用统一的文件类型配置
        return com.lingtxt.editor.config.SupportedFileTypes.isSupportedFile(fileName);
    }

    /**
     * 判断扩展名是否为文本文件
     */
    public static boolean isTextExtension(@NonNull String extension) {
        return com.lingtxt.editor.config.SupportedFileTypes.isSupportedExtension(extension);
    }

    /**
     * 获取文件类型的显示名称
     */
    @NonNull
    public static String getFileTypeDisplayName(@NonNull String fileName) {
        return com.lingtxt.editor.config.SupportedFileTypes.getFileTypeDisplayName(fileName);
    }

    /**
     * 检查文件名是否有效
     */
    public static boolean isValidFileName(@NonNull String fileName) {
        if (fileName.trim().isEmpty()) {
            return false;
        }
        
        // 检查非法字符
        String invalidChars = "<>:\"/\\|?*";
        for (char c : invalidChars.toCharArray()) {
            if (fileName.indexOf(c) != -1) {
                return false;
            }
        }
        
        // 检查保留名称（Windows）
        String[] reservedNames = {
            "CON", "PRN", "AUX", "NUL",
            "COM1", "COM2", "COM3", "COM4", "COM5", "COM6", "COM7", "COM8", "COM9",
            "LPT1", "LPT2", "LPT3", "LPT4", "LPT5", "LPT6", "LPT7", "LPT8", "LPT9"
        };
        
        String nameWithoutExt = fileName;
        int lastDot = fileName.lastIndexOf('.');
        if (lastDot > 0) {
            nameWithoutExt = fileName.substring(0, lastDot);
        }
        
        for (String reserved : reservedNames) {
            if (reserved.equalsIgnoreCase(nameWithoutExt)) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 清理文件名，移除非法字符
     */
    @NonNull
    public static String sanitizeFileName(@NonNull String fileName) {
        if (fileName.trim().isEmpty()) {
            return "untitled";
        }
        
        // 替换非法字符
        String sanitized = fileName.replaceAll("[<>:\"/\\\\|?*]", "_");
        
        // 移除开头和结尾的空格和点
        sanitized = sanitized.trim().replaceAll("^[.\\s]+|[.\\s]+$", "");
        
        if (sanitized.isEmpty()) {
            return "untitled";
        }
        
        return sanitized;
    }

    /**
     * 格式化时间戳
     */
    @NonNull
    public static String formatTimestamp(long timestamp) {
        if (timestamp <= 0) {
            return "未知";
        }
        
        java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat(
            "yyyy-MM-dd HH:mm:ss", Locale.getDefault());
        return sdf.format(new java.util.Date(timestamp));
    }

    /**
     * 获取相对时间描述
     */
    @NonNull
    public static String getRelativeTimeDescription(long timestamp) {
        if (timestamp <= 0) {
            return "未知时间";
        }
        
        long now = System.currentTimeMillis();
        long diff = now - timestamp;
        
        if (diff < 60 * 1000) { // 1分钟内
            return "刚刚";
        } else if (diff < 60 * 60 * 1000) { // 1小时内
            return (diff / (60 * 1000)) + "分钟前";
        } else if (diff < 24 * 60 * 60 * 1000) { // 1天内
            return (diff / (60 * 60 * 1000)) + "小时前";
        } else if (diff < 7 * 24 * 60 * 60 * 1000) { // 1周内
            return (diff / (24 * 60 * 60 * 1000)) + "天前";
        } else {
            return formatTimestamp(timestamp);
        }
    }
}