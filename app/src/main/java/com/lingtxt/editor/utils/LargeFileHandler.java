package com.lingtxt.editor.utils;

import android.content.Context;
import android.net.Uri;
import androidx.annotation.NonNull;

import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.Single;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

/**
 * 大文件处理工具类
 */
public class LargeFileHandler {

    private static final int DEFAULT_CHUNK_SIZE = 8192; // 8KB
    private static final int DEFAULT_LINE_BUFFER_SIZE = 1000; // 1000行
    private static final long MAX_PREVIEW_SIZE = 1024 * 1024; // 1MB预览

    /**
     * 文件块数据
     */
    public static class FileChunk {
        private final String content;
        private final int chunkIndex;
        private final boolean isLastChunk;
        private final long totalSize;

        public FileChunk(String content, int chunkIndex, boolean isLastChunk, long totalSize) {
            this.content = content;
            this.chunkIndex = chunkIndex;
            this.isLastChunk = isLastChunk;
            this.totalSize = totalSize;
        }

        public String getContent() { return content; }
        public int getChunkIndex() { return chunkIndex; }
        public boolean isLastChunk() { return isLastChunk; }
        public long getTotalSize() { return totalSize; }
    }

    /**
     * 行数据
     */
    public static class LineData {
        private final String content;
        private final int lineNumber;
        private final long byteOffset;

        public LineData(String content, int lineNumber, long byteOffset) {
            this.content = content;
            this.lineNumber = lineNumber;
            this.byteOffset = byteOffset;
        }

        public String getContent() { return content; }
        public int getLineNumber() { return lineNumber; }
        public long getByteOffset() { return byteOffset; }
    }

    /**
     * 分块读取文件
     */
    public static Observable<FileChunk> readFileInChunks(@NonNull Context context, 
                                                        @NonNull Uri uri, 
                                                        @NonNull String encoding,
                                                        int chunkSize) {
        return Observable.create(emitter -> {
            try (InputStream inputStream = context.getContentResolver().openInputStream(uri);
                 BufferedReader reader = new BufferedReader(
                     new InputStreamReader(inputStream, encoding))) {
                
                char[] buffer = new char[chunkSize];
                int charsRead;
                int chunkIndex = 0;
                long totalBytesRead = 0;
                
                while ((charsRead = reader.read(buffer)) != -1 && !emitter.isDisposed()) {
                    String chunkContent = new String(buffer, 0, charsRead);
                    totalBytesRead += charsRead;
                    
                    // 检查是否是最后一个块
                    reader.mark(1);
                    boolean isLastChunk = reader.read() == -1;
                    reader.reset();
                    
                    FileChunk chunk = new FileChunk(chunkContent, chunkIndex++, isLastChunk, totalBytesRead);
                    emitter.onNext(chunk);
                    
                    if (isLastChunk) {
                        break;
                    }
                }
                
                if (!emitter.isDisposed()) {
                    emitter.onComplete();
                }
            } catch (Exception e) {
                if (!emitter.isDisposed()) {
                    emitter.onError(FileOperationException.fromThrowable(e));
                }
            }
        });
    }

    /**
     * 按行读取文件
     */
    public static Observable<LineData> readFileByLines(@NonNull Context context, 
                                                      @NonNull Uri uri, 
                                                      @NonNull String encoding,
                                                      int maxLines) {
        return Observable.create(emitter -> {
            try (InputStream inputStream = context.getContentResolver().openInputStream(uri);
                 BufferedReader reader = new BufferedReader(
                     new InputStreamReader(inputStream, encoding))) {
                
                String line;
                int lineNumber = 1;
                long byteOffset = 0;
                int linesRead = 0;
                
                while ((line = reader.readLine()) != null && !emitter.isDisposed()) {
                    LineData lineData = new LineData(line, lineNumber, byteOffset);
                    emitter.onNext(lineData);
                    
                    lineNumber++;
                    byteOffset += line.getBytes(encoding).length + 1; // +1 for newline
                    linesRead++;
                    
                    if (maxLines > 0 && linesRead >= maxLines) {
                        break;
                    }
                }
                
                if (!emitter.isDisposed()) {
                    emitter.onComplete();
                }
            } catch (Exception e) {
                if (!emitter.isDisposed()) {
                    emitter.onError(FileOperationException.fromThrowable(e));
                }
            }
        });
    }

    /**
     * 获取文件预览（前几行或前几KB）
     */
    public static Single<String> getFilePreview(@NonNull Context context, 
                                               @NonNull Uri uri, 
                                               @NonNull String encoding,
                                               int maxLines) {
        return Single.fromCallable(() -> {
            StringBuilder preview = new StringBuilder();
            
            try (InputStream inputStream = context.getContentResolver().openInputStream(uri);
                 BufferedReader reader = new BufferedReader(
                     new InputStreamReader(inputStream, encoding))) {
                
                String line;
                int linesRead = 0;
                long bytesRead = 0;
                
                while ((line = reader.readLine()) != null && 
                       linesRead < maxLines && 
                       bytesRead < MAX_PREVIEW_SIZE) {
                    
                    preview.append(line).append("\n");
                    linesRead++;
                    bytesRead += line.getBytes(encoding).length + 1;
                }
                
                if (linesRead >= maxLines || bytesRead >= MAX_PREVIEW_SIZE) {
                    preview.append("\n... (文件内容过长，仅显示前 ").append(linesRead).append(" 行)");
                }
                
                return preview.toString();
            }
        });
    }

    /**
     * 计算文件行数
     */
    public static Single<Integer> countFileLines(@NonNull Context context, 
                                                @NonNull Uri uri, 
                                                @NonNull String encoding) {
        return Single.fromCallable(() -> {
            int lineCount = 0;
            
            try (InputStream inputStream = context.getContentResolver().openInputStream(uri);
                 BufferedReader reader = new BufferedReader(
                     new InputStreamReader(inputStream, encoding))) {
                
                while (reader.readLine() != null) {
                    lineCount++;
                }
            }
            
            return lineCount;
        });
    }

    /**
     * 读取指定范围的行
     */
    public static Single<List<String>> readLinesInRange(@NonNull Context context, 
                                                       @NonNull Uri uri, 
                                                       @NonNull String encoding,
                                                       int startLine, 
                                                       int endLine) {
        return Single.fromCallable(() -> {
            List<String> lines = new ArrayList<>();
            
            if (startLine < 1 || endLine < startLine) {
                throw new IllegalArgumentException("无效的行号范围");
            }
            
            try (InputStream inputStream = context.getContentResolver().openInputStream(uri);
                 BufferedReader reader = new BufferedReader(
                     new InputStreamReader(inputStream, encoding))) {
                
                String line;
                int currentLine = 1;
                
                // 跳过开始行之前的内容
                while (currentLine < startLine && (line = reader.readLine()) != null) {
                    currentLine++;
                }
                
                // 读取指定范围的行
                while (currentLine <= endLine && (line = reader.readLine()) != null) {
                    lines.add(line);
                    currentLine++;
                }
            }
            
            return lines;
        });
    }

    /**
     * 检查文件是否过大
     */
    public static Single<Boolean> isFileTooLarge(@NonNull Context context, 
                                                @NonNull Uri uri, 
                                                long maxSize) {
        return Single.fromCallable(() -> {
            try (InputStream inputStream = context.getContentResolver().openInputStream(uri)) {
                long size = 0;
                byte[] buffer = new byte[DEFAULT_CHUNK_SIZE];
                int bytesRead;
                
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    size += bytesRead;
                    if (size > maxSize) {
                        return true;
                    }
                }
                
                return false;
            }
        });
    }

    /**
     * 获取文件大小的估算值
     */
    public static Single<Long> estimateFileSize(@NonNull Context context, @NonNull Uri uri) {
        return Single.fromCallable(() -> {
            try (InputStream inputStream = context.getContentResolver().openInputStream(uri)) {
                long size = 0;
                byte[] buffer = new byte[DEFAULT_CHUNK_SIZE];
                int bytesRead;
                
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    size += bytesRead;
                }
                
                return size;
            }
        });
    }
}