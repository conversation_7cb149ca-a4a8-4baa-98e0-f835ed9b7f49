package com.lingtxt.editor.utils;

import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;

import androidx.annotation.NonNull;
import androidx.documentfile.provider.DocumentFile;

import com.lingtxt.editor.config.SupportedFileTypes;

/**
 * 文件访问助手
 * 提供多种文件访问方式，优先使用SAF，降低权限要求
 */
public class FileAccessHelper {

    public static final int REQUEST_CODE_OPEN_FILE = 2001;
    public static final int REQUEST_CODE_SAVE_FILE = 2002;
    public static final int REQUEST_CODE_OPEN_DIRECTORY = 2003;

    /**
     * 文件访问回调
     */
    public interface FileAccessCallback {
        void onFileSelected(Uri uri);
        void onDirectorySelected(Uri uri);
        void onAccessDenied();
        void onError(String error);
    }

    /**
     * 使用SAF打开文件（推荐方式，无需权限）
     */
    public static void openFileWithSAF(@NonNull Activity activity) {
        Intent intent = new Intent(Intent.ACTION_OPEN_DOCUMENT);
        intent.addCategory(Intent.CATEGORY_OPENABLE);

        // 临时测试：允许所有文件类型
        intent.setType("*/*");

        // 也添加我们支持的 MIME 类型作为提示
        String[] mimeTypes = SupportedFileTypes.getSupportedMimeTypes();
        intent.putExtra(Intent.EXTRA_MIME_TYPES, mimeTypes);
        intent.putExtra(Intent.EXTRA_TITLE, "选择文本文件");

        try {
            activity.startActivityForResult(intent, REQUEST_CODE_OPEN_FILE);
        } catch (Exception e) {
            // 如果SAF不可用，提示用户
            UserFeedbackManager.getInstance().showErrorDialog(
                activity,
                "文件选择器不可用",
                "系统文件选择器不可用，请检查系统设置或使用其他方式打开文件。"
            );
        }
    }

    /**
     * 使用SAF保存文件
     */
    public static void saveFileWithSAF(@NonNull Activity activity, @NonNull String fileName) {
        Intent intent = new Intent(Intent.ACTION_CREATE_DOCUMENT);
        intent.addCategory(Intent.CATEGORY_OPENABLE);
        intent.setType("text/plain");
        intent.putExtra(Intent.EXTRA_TITLE, fileName);
        
        try {
            activity.startActivityForResult(intent, REQUEST_CODE_SAVE_FILE);
        } catch (Exception e) {
            UserFeedbackManager.getInstance().showErrorDialog(
                activity,
                "文件保存器不可用",
                "系统文件保存器不可用，请检查系统设置。"
            );
        }
    }

    /**
     * 使用SAF选择目录
     */
    public static void selectDirectoryWithSAF(@NonNull Activity activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            Intent intent = new Intent(Intent.ACTION_OPEN_DOCUMENT_TREE);
            intent.putExtra(Intent.EXTRA_TITLE, "选择文件夹");
            
            try {
                activity.startActivityForResult(intent, REQUEST_CODE_OPEN_DIRECTORY);
            } catch (Exception e) {
                UserFeedbackManager.getInstance().showErrorDialog(
                    activity,
                    "文件夹选择器不可用",
                    "系统文件夹选择器不可用，请检查系统设置。"
                );
            }
        }
    }

    /**
     * 检查是否需要高级文件权限
     */
    public static boolean needsAdvancedFileAccess(@NonNull Activity activity) {
        // 如果用户已经有基础权限，但想访问更多位置
        return PermissionManager.hasBasicStoragePermission(activity) && 
               !PermissionManager.hasStoragePermission(activity);
    }

    /**
     * 显示高级文件访问选项对话框
     */
    public static void showAdvancedAccessDialog(@NonNull Activity activity, @NonNull Runnable onRequestAdvanced) {
        UserFeedbackManager.getInstance().showConfirmDialog(
            activity,
            "需要更多文件访问权限",
            "当前只能访问媒体文件夹中的文件。\n\n如需访问其他位置的文件，可以：\n" +
            "1. 使用文件选择器（推荐）\n" +
            "2. 授权访问所有文件\n\n" +
            "是否授权访问所有文件？",
            onRequestAdvanced
        );
    }

    /**
     * 请求高级文件访问权限
     */
    public static void requestAdvancedFileAccess(@NonNull Activity activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            PermissionManager.requestManageExternalStoragePermission(activity);
        } else {
            // 对于较低版本，基础权限已经足够
            UserFeedbackManager.getInstance().showToast(activity, "当前系统版本已有足够权限");
        }
    }

    /**
     * 获取推荐的文件访问方式说明
     */
    public static String getRecommendedAccessMethod() {
        return "推荐使用文件选择器打开文件：\n" +
               "• 无需额外权限\n" +
               "• 更安全可靠\n" +
               "• 支持所有位置的文件\n" +
               "• 系统原生体验";
    }

    /**
     * 检查URI是否可访问
     */
    public static boolean isUriAccessible(@NonNull Activity activity, @NonNull Uri uri) {
        try {
            DocumentFile documentFile = DocumentFile.fromSingleUri(activity, uri);
            return documentFile != null && documentFile.exists() && documentFile.canRead();
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取文件访问建议
     */
    public static String getFileAccessSuggestion(@NonNull Activity activity) {
        if (PermissionManager.hasStoragePermission(activity)) {
            return "✅ 已有完整文件访问权限";
        } else if (PermissionManager.hasBasicStoragePermission(activity)) {
            return "⚠️ 当前可访问媒体文件，建议使用文件选择器访问其他位置";
        } else {
            return "❌ 需要基础文件访问权限，或使用文件选择器";
        }
    }

    /**
     * 处理文件访问结果
     */
    public static void handleFileAccessResult(int requestCode, int resultCode, Intent data, 
                                            @NonNull FileAccessCallback callback) {
        if (resultCode != Activity.RESULT_OK || data == null) {
            callback.onAccessDenied();
            return;
        }

        Uri uri = data.getData();
        if (uri == null) {
            callback.onError("未获取到文件路径");
            return;
        }

        switch (requestCode) {
            case REQUEST_CODE_OPEN_FILE:
            case REQUEST_CODE_SAVE_FILE:
                callback.onFileSelected(uri);
                break;
            case REQUEST_CODE_OPEN_DIRECTORY:
                callback.onDirectorySelected(uri);
                break;
            default:
                callback.onError("未知的请求类型");
                break;
        }
    }

    /**
     * 获取常见文档目录
     */
    public static String[] getCommonDocumentPaths() {
        return new String[] {
            Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS).getAbsolutePath(),
            Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).getAbsolutePath(),
            Environment.getExternalStorageDirectory().getAbsolutePath() + "/Documents",
            Environment.getExternalStorageDirectory().getAbsolutePath() + "/Download"
        };
    }
}
