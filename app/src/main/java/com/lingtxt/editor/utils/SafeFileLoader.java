package com.lingtxt.editor.utils;

import android.content.Context;
import android.net.Uri;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.Single;
import io.reactivex.rxjava3.schedulers.Schedulers;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 安全的文件加载工具类
 * 提供超时保护、进度反馈和错误处理
 */
public class SafeFileLoader {
    
    private static final int DEFAULT_TIMEOUT_SECONDS = 30; // 30秒超时
    private static final int CHUNK_SIZE = 8192; // 8KB chunks
    private static final long MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB limit
    private static final int MAX_PREVIEW_LINES = 100; // 预览最多100行
    
    /**
     * 文件加载回调
     */
    public interface LoadCallback {
        void onLoadStart();
        void onProgress(int progress); // 0-100
        void onLoadSuccess(String content, String encoding);
        void onLoadError(String error);
        void onTimeout();
    }
    
    /**
     * 文件预检查结果
     */
    public static class FileCheckResult {
        public final boolean canLoad;
        public final String reason;
        public final long fileSize;
        public final String suggestedAction;
        
        public FileCheckResult(boolean canLoad, String reason, long fileSize, String suggestedAction) {
            this.canLoad = canLoad;
            this.reason = reason;
            this.fileSize = fileSize;
            this.suggestedAction = suggestedAction;
        }
    }
    
    /**
     * 安全加载文件（带超时和进度反馈）
     */
    public static void loadFileSafely(@NonNull Context context, 
                                     @NonNull Uri uri, 
                                     @NonNull LoadCallback callback) {
        loadFileSafely(context, uri, callback, DEFAULT_TIMEOUT_SECONDS);
    }
    
    /**
     * 安全加载文件（自定义超时时间）
     */
    public static void loadFileSafely(@NonNull Context context, 
                                     @NonNull Uri uri, 
                                     @NonNull LoadCallback callback,
                                     int timeoutSeconds) {
        
        callback.onLoadStart();
        
        // 先进行文件预检查
        checkFileBeforeLoad(context, uri)
            .subscribeOn(Schedulers.io())
            .timeout(5, TimeUnit.SECONDS) // 预检查5秒超时
            .subscribe(
                checkResult -> {
                    if (!checkResult.canLoad) {
                        callback.onLoadError(checkResult.reason + "\n\n建议：" + checkResult.suggestedAction);
                        return;
                    }
                    
                    // 预检查通过，开始加载文件
                    loadFileWithProgress(context, uri, callback, timeoutSeconds);
                },
                error -> callback.onLoadError("文件预检查失败：" + error.getMessage())
            );
    }
    
    /**
     * 文件预检查
     */
    private static Single<FileCheckResult> checkFileBeforeLoad(@NonNull Context context, @NonNull Uri uri) {
        return Single.fromCallable(() -> {
            try {
                // 检查文件是否可访问
                try (InputStream inputStream = context.getContentResolver().openInputStream(uri)) {
                    if (inputStream == null) {
                        return new FileCheckResult(false, "无法访问文件", 0, "请检查文件是否存在或权限是否足够");
                    }
                }
                
                // 估算文件大小
                long fileSize = estimateFileSize(context, uri);
                if (fileSize > MAX_FILE_SIZE) {
                    return new FileCheckResult(false, 
                        "文件过大（" + FileUtils.formatFileSize(fileSize) + "）", 
                        fileSize, 
                        "请选择小于 " + FileUtils.formatFileSize(MAX_FILE_SIZE) + " 的文件");
                }
                
                // 检查是否可能是二进制文件
                if (isProbablyBinaryFile(context, uri)) {
                    return new FileCheckResult(false, 
                        "文件可能包含二进制内容", 
                        fileSize, 
                        "请选择文本文件，如 .txt、.md、.json 等");
                }
                
                return new FileCheckResult(true, "文件检查通过", fileSize, "");
                
            } catch (Exception e) {
                return new FileCheckResult(false, "文件检查失败：" + e.getMessage(), 0, "请重新选择文件");
            }
        });
    }
    
    /**
     * 带进度的文件加载
     */
    private static void loadFileWithProgress(@NonNull Context context, 
                                           @NonNull Uri uri, 
                                           @NonNull LoadCallback callback,
                                           int timeoutSeconds) {
        
        AtomicBoolean isCompleted = new AtomicBoolean(false);
        
        Observable.fromCallable(() -> {
            String encoding = EncodingDetector.detectEncoding(context, uri);
            StringBuilder content = new StringBuilder();
            
            try (InputStream inputStream = context.getContentResolver().openInputStream(uri);
                 BufferedReader reader = new BufferedReader(
                     new InputStreamReader(inputStream, encoding))) {
                
                char[] buffer = new char[CHUNK_SIZE];
                int charsRead;
                long totalChars = 0;
                long estimatedSize = estimateFileSize(context, uri);
                
                while ((charsRead = reader.read(buffer)) != -1) {
                    // 检查是否已超时或取消
                    if (isCompleted.get()) {
                        break;
                    }
                    
                    content.append(buffer, 0, charsRead);
                    totalChars += charsRead;
                    
                    // 更新进度
                    if (estimatedSize > 0) {
                        int progress = (int) Math.min(100, (totalChars * 100) / estimatedSize);
                        callback.onProgress(progress);
                    }
                    
                    // 检查内容是否包含大量非文本字符
                    if (totalChars > 1024 && containsTooManyBinaryChars(content.toString())) {
                        throw new RuntimeException("文件包含大量二进制字符，可能不是文本文件");
                    }
                }
                
                return new String[]{content.toString(), encoding};
            }
        })
        .subscribeOn(Schedulers.io())
        .timeout(timeoutSeconds, TimeUnit.SECONDS)
        .subscribe(
            result -> {
                if (!isCompleted.getAndSet(true)) {
                    callback.onProgress(100);
                    callback.onLoadSuccess(result[0], result[1]);
                }
            },
            error -> {
                if (!isCompleted.getAndSet(true)) {
                    if (error instanceof java.util.concurrent.TimeoutException) {
                        callback.onTimeout();
                    } else {
                        callback.onLoadError("文件加载失败：" + error.getMessage());
                    }
                }
            }
        );
    }
    
    /**
     * 估算文件大小
     */
    private static long estimateFileSize(@NonNull Context context, @NonNull Uri uri) {
        try (InputStream inputStream = context.getContentResolver().openInputStream(uri)) {
            return inputStream.available();
        } catch (Exception e) {
            return 0;
        }
    }
    
    /**
     * 检查是否可能是二进制文件（简单检查前几KB）
     */
    private static boolean isProbablyBinaryFile(@NonNull Context context, @NonNull Uri uri) {
        try (InputStream inputStream = context.getContentResolver().openInputStream(uri)) {
            byte[] buffer = new byte[1024]; // 检查前1KB
            int bytesRead = inputStream.read(buffer);
            
            if (bytesRead <= 0) {
                return false;
            }
            
            // 检查是否包含过多的控制字符或非ASCII字符
            int binaryCount = 0;
            for (int i = 0; i < bytesRead; i++) {
                byte b = buffer[i];
                // 检查是否为控制字符（除了常见的换行、制表符等）
                if (b < 32 && b != 9 && b != 10 && b != 13) {
                    binaryCount++;
                }
            }
            
            // 如果超过10%是控制字符，可能是二进制文件
            return (binaryCount * 100.0 / bytesRead) > 10;
            
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 检查内容是否包含过多二进制字符
     */
    private static boolean containsTooManyBinaryChars(@NonNull String content) {
        if (content.length() < 100) {
            return false;
        }
        
        int binaryCount = 0;
        int checkLength = Math.min(1000, content.length()); // 只检查前1000个字符
        
        for (int i = 0; i < checkLength; i++) {
            char c = content.charAt(i);
            // 检查是否为不可打印字符（除了常见的空白字符）
            if (c < 32 && c != 9 && c != 10 && c != 13) {
                binaryCount++;
            }
        }
        
        // 如果超过5%是不可打印字符，可能不是文本文件
        return (binaryCount * 100.0 / checkLength) > 5;
    }
    
    /**
     * 获取文件预览（用于大文件或可疑文件）
     */
    public static void getFilePreview(@NonNull Context context, 
                                     @NonNull Uri uri, 
                                     @NonNull LoadCallback callback) {
        
        callback.onLoadStart();
        
        Single.fromCallable(() -> {
            String encoding = EncodingDetector.detectEncoding(context, uri);
            StringBuilder preview = new StringBuilder();
            
            try (InputStream inputStream = context.getContentResolver().openInputStream(uri);
                 BufferedReader reader = new BufferedReader(
                     new InputStreamReader(inputStream, encoding))) {
                
                String line;
                int linesRead = 0;
                
                while ((line = reader.readLine()) != null && linesRead < MAX_PREVIEW_LINES) {
                    preview.append(line).append("\n");
                    linesRead++;
                    
                    // 更新进度
                    callback.onProgress((linesRead * 100) / MAX_PREVIEW_LINES);
                }
                
                if (linesRead >= MAX_PREVIEW_LINES) {
                    preview.append("\n... (仅显示前 ").append(linesRead).append(" 行预览)");
                }
                
                return new String[]{preview.toString(), encoding};
            }
        })
        .subscribeOn(Schedulers.io())
        .timeout(10, TimeUnit.SECONDS)
        .subscribe(
            result -> {
                callback.onProgress(100);
                callback.onLoadSuccess(result[0], result[1]);
            },
            error -> {
                if (error instanceof java.util.concurrent.TimeoutException) {
                    callback.onTimeout();
                } else {
                    callback.onLoadError("预览加载失败：" + error.getMessage());
                }
            }
        );
    }
}
