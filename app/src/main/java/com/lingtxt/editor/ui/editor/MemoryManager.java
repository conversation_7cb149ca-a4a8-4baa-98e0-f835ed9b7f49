package com.lingtxt.editor.ui.editor;

import android.app.ActivityManager;
import android.content.Context;
import android.util.Log;
import androidx.annotation.NonNull;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 内存管理器
 * 实现智能内存管理，及时释放不可见区域的内容，添加LRU缓存机制
 */
public class MemoryManager {
    
    private static final String TAG = "MemoryManager";
    
    // 内存阈值配置
    private static final float LOW_MEMORY_THRESHOLD = 0.8f;     // 低内存阈值（80%）
    private static final float CRITICAL_MEMORY_THRESHOLD = 0.9f; // 临界内存阈值（90%）
    private static final long MEMORY_CHECK_INTERVAL = 5000;     // 内存检查间隔（5秒）
    
    // 缓存配置
    private static final int DEFAULT_MAX_CACHE_SIZE = 50;       // 默认最大缓存大小
    private static final long CACHE_EXPIRE_TIME = 300000;      // 缓存过期时间（5分钟）
    
    private final Context context;
    private final ActivityManager activityManager;
    private final ActivityManager.MemoryInfo memoryInfo;
    
    // 缓存管理
    private final LRUCache<String, CacheEntry> cache;
    private int maxCacheSize = DEFAULT_MAX_CACHE_SIZE;
    
    // 内存监控
    private long lastMemoryCheck = 0;
    private boolean isLowMemoryMode = false;
    private final List<WeakReference<MemoryListener>> listeners = new ArrayList<>();
    
    /**
     * 缓存条目
     */
    private static class CacheEntry {
        final Object data;
        final long timestamp;
        final long size;
        
        CacheEntry(Object data, long size) {
            this.data = data;
            this.size = size;
            this.timestamp = System.currentTimeMillis();
        }
        
        boolean isExpired() {
            return System.currentTimeMillis() - timestamp > CACHE_EXPIRE_TIME;
        }
    }
    
    /**
     * LRU缓存实现
     */
    private static class LRUCache<K, V> extends LinkedHashMap<K, V> {
        private final int maxSize;
        
        LRUCache(int maxSize) {
            super(16, 0.75f, true);
            this.maxSize = maxSize;
        }
        
        @Override
        protected boolean removeEldestEntry(Map.Entry<K, V> eldest) {
            return size() > maxSize;
        }
    }
    
    /**
     * 内存事件监听器
     */
    public interface MemoryListener {
        void onMemoryPressureChanged(MemoryPressure pressure);
        void onMemoryReleaseRequested(int targetReleasePercent);
    }
    
    /**
     * 内存压力级别
     */
    public enum MemoryPressure {
        NORMAL,     // 正常
        LOW,        // 低内存
        CRITICAL    // 临界内存
    }
    
    public MemoryManager(@NonNull Context context) {
        this.context = context.getApplicationContext();
        this.activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        this.memoryInfo = new ActivityManager.MemoryInfo();
        this.cache = new LRUCache<>(maxCacheSize);
    }

    /**
     * 添加内存监听器
     */
    public void addMemoryListener(@NonNull MemoryListener listener) {
        listeners.add(new WeakReference<>(listener));
    }

    /**
     * 检查内存状态
     */
    public MemoryPressure checkMemoryPressure() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastMemoryCheck < MEMORY_CHECK_INTERVAL) {
            return isLowMemoryMode ? MemoryPressure.LOW : MemoryPressure.NORMAL;
        }
        
        lastMemoryCheck = currentTime;
        activityManager.getMemoryInfo(memoryInfo);
        
        long availableMemory = memoryInfo.availMem;
        long totalMemory = memoryInfo.totalMem;
        float memoryUsageRatio = 1.0f - (float) availableMemory / totalMemory;
        
        MemoryPressure pressure;
        if (memoryUsageRatio >= CRITICAL_MEMORY_THRESHOLD) {
            pressure = MemoryPressure.CRITICAL;
            isLowMemoryMode = true;
        } else if (memoryUsageRatio >= LOW_MEMORY_THRESHOLD) {
            pressure = MemoryPressure.LOW;
            isLowMemoryMode = true;
        } else {
            pressure = MemoryPressure.NORMAL;
            isLowMemoryMode = false;
        }
        
        Log.d(TAG, String.format("Memory usage: %.1f%%, pressure: %s",
            memoryUsageRatio * 100, pressure));

        // 通知监听器
        notifyMemoryPressureChanged(pressure);

        return pressure;
    }

    
    /**
     * 获取缓存条目
     */
    @SuppressWarnings("unchecked")
    public <T> T getCache(@NonNull String key, @NonNull Class<T> type) {
        synchronized (cache) {
            CacheEntry entry = cache.get(key);
            if (entry != null && !entry.isExpired()) {
                return type.cast(entry.data);
            }
        }
        return null;
    }
    
    /**
     * 清空所有缓存
     */
    public void clearAllCache() {
        synchronized (cache) {
            cache.clear();
            Log.d(TAG, "Cleared all cache");
        }
    }
    
    /**
     * 获取内存使用信息
     */
    public String getMemoryInfo() {
        activityManager.getMemoryInfo(memoryInfo);
        
        long availableMemory = memoryInfo.availMem;
        long totalMemory = memoryInfo.totalMem;
        long usedMemory = totalMemory - availableMemory;
        float usageRatio = (float) usedMemory / totalMemory;
        
        return String.format("Memory: %.1f MB / %.1f MB (%.1f%%)", 
            usedMemory / (1024.0 * 1024.0), totalMemory / (1024.0 * 1024.0), usageRatio * 100);
    }
    
    public void cleanup() {
        clearAllCache();
        listeners.clear();
    }

    /**
     * 通知内存压力变化
     */
    private void notifyMemoryPressureChanged(MemoryPressure pressure) {
        Iterator<WeakReference<MemoryListener>> iterator = listeners.iterator();
        while (iterator.hasNext()) {
            WeakReference<MemoryListener> ref = iterator.next();
            MemoryListener listener = ref.get();
            if (listener == null) {
                iterator.remove();
            } else {
                try {
                    listener.onMemoryPressureChanged(pressure);
                } catch (Exception e) {
                    Log.w(TAG, "Error notifying memory listener", e);
                }
            }
        }
    }
}
