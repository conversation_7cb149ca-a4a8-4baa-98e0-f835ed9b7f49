package com.lingtxt.editor.ui.widget;

import android.text.Editable;
import android.text.TextWatcher;
import android.widget.EditText;
import java.util.ArrayList;
import java.util.List;

/**
 * 撤销/重做管理器
 * 管理文本编辑的历史记录，支持撤销和重做操作
 */
public class UndoRedoManager {

    /**
     * 编辑操作类型
     */
    public enum ActionType {
        INSERT,  // 插入文本
        DELETE,  // 删除文本
        REPLACE  // 替换文本
    }

    /**
     * 编辑动作记录
     */
    public static class EditAction {
        public ActionType type;
        public int start;
        public int end;
        public CharSequence oldText;
        public CharSequence newText;
        public long timestamp;

        public EditAction(ActionType type, int start, int end, 
                         CharSequence oldText, CharSequence newText) {
            this.type = type;
            this.start = start;
            this.end = end;
            this.oldText = oldText;
            this.newText = newText;
            this.timestamp = System.currentTimeMillis();
        }

        @Override
        public String toString() {
            return String.format("EditAction{type=%s, start=%d, end=%d, old='%s', new='%s'}",
                    type, start, end, oldText, newText);
        }
    }

    private final List<EditAction> undoStack = new ArrayList<>();
    private final List<EditAction> redoStack = new ArrayList<>();
    private EditText editText;
    
    // 配置参数
    private int maxHistorySize = 100;
    private long mergeTimeWindow = 1000; // 1秒内的连续操作会被合并
    private boolean isRecording = true;
    private boolean isUndoRedoInProgress = false;
    
    // 监听器
    private OnUndoRedoStateChangedListener stateChangedListener;
    private TextWatcher textWatcher;
    
    public UndoRedoManager(EditText editText) {
        this.editText = editText;
        if (editText != null) {
            setupTextWatcher();
        }
    }

    /**
     * 设置EditText（延迟初始化）
     */
    public void setEditText(EditText editText) {
        // 如果之前有EditText，先移除TextWatcher
        if (this.editText != null && textWatcher != null) {
            this.editText.removeTextChangedListener(textWatcher);
        }

        this.editText = editText;

        // 如果新的EditText不为null，设置TextWatcher
        if (editText != null) {
            setupTextWatcher();
        }
    }

    /**
     * 设置文本变化监听器
     */
    private void setupTextWatcher() {
        if (editText == null) return;

        textWatcher = new TextWatcher() {
            private CharSequence beforeText;
            private int beforeStart;
            private int beforeCount;
            private int afterCount;

            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                if (!isRecording || isUndoRedoInProgress) return;

                beforeText = s.subSequence(start, start + count);
                beforeStart = start;
                beforeCount = count;
                afterCount = after;

                android.util.Log.d("UndoRedoManager", "beforeTextChanged: start=" + start +
                    ", count=" + count + ", after=" + after + ", text='" + beforeText + "'");
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int after) {
                // 在这里不处理，在afterTextChanged中处理
            }

            @Override
            public void afterTextChanged(Editable s) {
                if (!isRecording || isUndoRedoInProgress) return;

                int start = beforeStart;
                int oldLength = beforeCount;
                int newLength = afterCount;

                CharSequence newText = "";
                if (newLength > 0 && start + newLength <= s.length()) {
                    newText = s.subSequence(start, start + newLength);
                }

                ActionType actionType;
                if (oldLength == 0 && newLength > 0) {
                    actionType = ActionType.INSERT;
                } else if (oldLength > 0 && newLength == 0) {
                    actionType = ActionType.DELETE;
                } else if (oldLength > 0 && newLength > 0) {
                    actionType = ActionType.REPLACE;
                } else {
                    // 没有实际变化，不记录
                    return;
                }

                EditAction action = new EditAction(actionType, start, start + oldLength,
                                                 beforeText, newText);

                android.util.Log.d("UndoRedoManager", "Recording action: " + action);
                addAction(action);
            }
        };

        editText.addTextChangedListener(textWatcher);
    }

    /**
     * 添加编辑动作到历史记录
     */
    private void addAction(EditAction action) {
        // 尝试与最后一个动作合并
        if (tryMergeWithLastAction(action)) {
            notifyStateChanged();
            return;
        }
        
        // 添加新动作
        undoStack.add(action);
        
        // 清空重做栈
        redoStack.clear();
        
        // 限制历史记录大小
        while (undoStack.size() > maxHistorySize) {
            undoStack.remove(0);
        }
        
        notifyStateChanged();
    }

    /**
     * 尝试与最后一个动作合并
     */
    private boolean tryMergeWithLastAction(EditAction newAction) {
        if (undoStack.isEmpty()) return false;
        
        EditAction lastAction = undoStack.get(undoStack.size() - 1);
        
        // 检查时间窗口
        if (newAction.timestamp - lastAction.timestamp > mergeTimeWindow) {
            return false;
        }
        
        // 只合并相同类型的连续操作
        if (lastAction.type != newAction.type) {
            return false;
        }
        
        // 合并插入操作
        if (newAction.type == ActionType.INSERT && 
            lastAction.end == newAction.start) {
            
            lastAction.newText = lastAction.newText.toString() + newAction.newText.toString();
            lastAction.end = newAction.end;
            lastAction.timestamp = newAction.timestamp;
            return true;
        }
        
        // 合并删除操作
        if (newAction.type == ActionType.DELETE && 
            newAction.end == lastAction.start) {
            
            lastAction.oldText = newAction.oldText.toString() + lastAction.oldText.toString();
            lastAction.start = newAction.start;
            lastAction.timestamp = newAction.timestamp;
            return true;
        }
        
        return false;
    }

    /**
     * 撤销操作
     */
    public boolean undo() {
        if (!canUndo() || editText == null) return false;

        EditAction action = undoStack.remove(undoStack.size() - 1);
        redoStack.add(action);

        applyReverseAction(action);
        notifyStateChanged();

        return true;
    }

    /**
     * 重做操作
     */
    public boolean redo() {
        if (!canRedo() || editText == null) return false;

        EditAction action = redoStack.remove(redoStack.size() - 1);
        undoStack.add(action);

        applyAction(action);
        notifyStateChanged();

        return true;
    }

    /**
     * 应用动作
     */
    private void applyAction(EditAction action) {
        if (editText == null) return;

        isUndoRedoInProgress = true;

        try {
            Editable editable = editText.getText();
            if (editable != null) {
                int targetCursorPos = -1; // 目标光标位置

                android.util.Log.d("UndoRedoManager", "Applying action: " + action);

                switch (action.type) {
                    case INSERT:
                        if (action.start <= editable.length()) {
                            editable.insert(action.start, action.newText);
                            // 光标位置设置为插入文本的末尾
                            targetCursorPos = Math.min(action.start + action.newText.length(), editable.length());
                        }
                        break;
                    case DELETE:
                        int deleteEnd = Math.min(action.end, editable.length());
                        if (action.start <= editable.length() && deleteEnd > action.start) {
                            editable.delete(action.start, deleteEnd);
                            // 光标位置设置为删除位置的开始
                            targetCursorPos = Math.min(action.start, editable.length());
                        }
                        break;
                    case REPLACE:
                        int replaceEnd = Math.min(action.end, editable.length());
                        if (action.start <= editable.length() && replaceEnd > action.start) {
                            editable.replace(action.start, replaceEnd, action.newText);
                            // 光标位置设置为替换文本的末尾
                            targetCursorPos = Math.min(action.start + action.newText.length(), editable.length());
                        }
                        break;
                }

                // 延迟设置光标位置，确保在所有文本变化监听器执行完毕后再设置
                if (targetCursorPos >= 0) {
                    final int finalCursorPos = targetCursorPos;
                    editText.post(() -> {
                        try {
                            if (editText != null && finalCursorPos <= editText.getText().length()) {
                                editText.setSelection(finalCursorPos);
                                android.util.Log.d("UndoRedoManager", "Set cursor position to: " + finalCursorPos);
                            }
                        } catch (Exception e) {
                            android.util.Log.w("UndoRedoManager", "Error setting cursor position", e);
                        }
                    });
                }
            }
        } catch (Exception e) {
            // 防止索引越界等异常
            android.util.Log.w("UndoRedoManager", "Error applying action", e);
        } finally {
            isUndoRedoInProgress = false;
        }
    }

    /**
     * 应用反向动作（用于撤销）
     */
    private void applyReverseAction(EditAction action) {
        if (editText == null) return;

        isUndoRedoInProgress = true;

        try {
            Editable editable = editText.getText();
            if (editable != null) {
                int targetCursorPos = -1; // 目标光标位置

                switch (action.type) {
                    case INSERT:
                        // 撤销插入 = 删除
                        int deleteEnd = Math.min(action.start + action.newText.length(), editable.length());
                        if (action.start <= editable.length() && deleteEnd > action.start) {
                            editable.delete(action.start, deleteEnd);
                        }
                        // 光标位置设置为删除位置的开始
                        targetCursorPos = Math.min(action.start, editable.length());
                        break;
                    case DELETE:
                        // 撤销删除 = 插入
                        if (action.start <= editable.length()) {
                            editable.insert(action.start, action.oldText);
                            // 光标位置设置为插入文本的末尾
                            targetCursorPos = Math.min(action.start + action.oldText.length(), editable.length());
                        }
                        break;
                    case REPLACE:
                        // 撤销替换 = 用旧文本替换新文本
                        int replaceEnd = Math.min(action.start + action.newText.length(), editable.length());
                        if (action.start <= editable.length() && replaceEnd > action.start) {
                            editable.replace(action.start, replaceEnd, action.oldText);
                            // 光标位置设置为替换文本的末尾
                            targetCursorPos = Math.min(action.start + action.oldText.length(), editable.length());
                        }
                        break;
                }

                // 延迟设置光标位置，确保在所有文本变化监听器执行完毕后再设置
                if (targetCursorPos >= 0) {
                    final int finalCursorPos = targetCursorPos;
                    editText.post(() -> {
                        try {
                            if (editText != null && finalCursorPos <= editText.getText().length()) {
                                editText.setSelection(finalCursorPos);
                                android.util.Log.d("UndoRedoManager", "Set cursor position to: " + finalCursorPos);
                            }
                        } catch (Exception e) {
                            android.util.Log.w("UndoRedoManager", "Error setting cursor position", e);
                        }
                    });
                }
            }
        } catch (Exception e) {
            // 防止索引越界等异常
            android.util.Log.w("UndoRedoManager", "Error applying reverse action", e);
        } finally {
            isUndoRedoInProgress = false;
        }
    }

    /**
     * 检查是否可以撤销
     */
    public boolean canUndo() {
        return !undoStack.isEmpty();
    }

    /**
     * 检查是否可以重做
     */
    public boolean canRedo() {
        return !redoStack.isEmpty();
    }

    /**
     * 清空历史记录
     */
    public void clearHistory() {
        undoStack.clear();
        redoStack.clear();
        notifyStateChanged();
    }

    /**
     * 暂停记录
     */
    public void pauseRecording() {
        isRecording = false;
    }

    /**
     * 恢复记录
     */
    public void resumeRecording() {
        isRecording = true;
    }

    /**
     * 创建检查点（清空重做栈，但保留撤销栈）
     */
    public void createCheckpoint() {
        redoStack.clear();
        notifyStateChanged();
    }

    /**
     * 获取撤销栈大小
     */
    public int getUndoStackSize() {
        return undoStack.size();
    }

    /**
     * 获取重做栈大小
     */
    public int getRedoStackSize() {
        return redoStack.size();
    }

    /**
     * 设置最大历史记录大小
     */
    public void setMaxHistorySize(int maxHistorySize) {
        this.maxHistorySize = Math.max(1, maxHistorySize);
        
        // 如果当前历史记录超过新的限制，删除旧记录
        while (undoStack.size() > this.maxHistorySize) {
            undoStack.remove(0);
        }
    }

    /**
     * 设置合并时间窗口
     */
    public void setMergeTimeWindow(long mergeTimeWindow) {
        this.mergeTimeWindow = Math.max(0, mergeTimeWindow);
    }

    /**
     * 状态变化监听器
     */
    public interface OnUndoRedoStateChangedListener {
        void onUndoRedoStateChanged(boolean canUndo, boolean canRedo);
    }

    public void setOnUndoRedoStateChangedListener(OnUndoRedoStateChangedListener listener) {
        this.stateChangedListener = listener;
    }

    /**
     * 通知状态变化
     */
    private void notifyStateChanged() {
        if (stateChangedListener != null) {
            stateChangedListener.onUndoRedoStateChanged(canUndo(), canRedo());
        }
    }

    /**
     * 获取调试信息
     */
    public String getDebugInfo() {
        return String.format("UndoRedoManager{undoStack=%d, redoStack=%d, recording=%b}",
                undoStack.size(), redoStack.size(), isRecording);
    }
}