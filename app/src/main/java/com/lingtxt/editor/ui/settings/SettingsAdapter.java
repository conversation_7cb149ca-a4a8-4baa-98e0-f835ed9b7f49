package com.lingtxt.editor.ui.settings;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.SeekBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.appcompat.widget.SwitchCompat;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.dialog.MaterialAlertDialogBuilder;
import com.lingtxt.editor.R;
import com.lingtxt.editor.data.model.AppLanguage;
import com.lingtxt.editor.data.model.Theme;
import java.util.ArrayList;
import java.util.List;

/**
 * 设置适配器
 */
public class SettingsAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {
    
    private static final int TYPE_CATEGORY = 0;
    private static final int TYPE_SWITCH = 1;
    private static final int TYPE_SLIDER = 2;
    private static final int TYPE_SELECTION = 3;
    private static final int TYPE_ACTION = 4;
    
    private final Context context;
    private final OnSettingChangeListener listener;
    private final List<SettingItem> items = new ArrayList<>();
    
    public interface OnSettingChangeListener {
        void onFontSizeChanged(float fontSize);
        void onFontFamilyChanged(String fontFamily);
        void onThemeChanged(Theme theme);
        void onLanguageChanged(AppLanguage language);
        void onShowLineNumbersChanged(boolean show);
        void onShowInvisibleCharsChanged(boolean show);
        void onSupportedFileTypesClicked();
    }
    
    public SettingsAdapter(Context context, OnSettingChangeListener listener) {
        this.context = context;
        this.listener = listener;
        initializeItems();
    }
    
    private void initializeItems() {
        items.clear();
        
        // 外观设置
        items.add(SettingItem.createCategory(context.getString(R.string.category_appearance)));
        
        items.add(SettingItem.createSlider(
            "font_size",
            context.getString(R.string.setting_font_size),
            context.getString(R.string.setting_font_size_summary),
            R.drawable.ic_text_increase,
            12f, 8f, 32f
        ));
        
        items.add(SettingItem.createSelection(
            "font_family",
            context.getString(R.string.setting_font_family),
            context.getString(R.string.setting_font_family_summary),
            R.drawable.ic_text_increase,
            "monospace",
            new String[]{"monospace", "serif", "sans-serif"}
        ));
        
        items.add(SettingItem.createSelection(
            "theme",
            context.getString(R.string.setting_theme),
            context.getString(R.string.setting_theme_summary),
            R.drawable.ic_settings,
            Theme.SYSTEM,
            Theme.values()
        ));
        
        items.add(SettingItem.createSelection(
            "language",
            context.getString(R.string.setting_language),
            context.getString(R.string.setting_language_summary),
            R.drawable.ic_language,
            AppLanguage.SYSTEM,
            AppLanguage.values()
        ));
        
        // 编辑器设置
        items.add(SettingItem.createCategory(context.getString(R.string.category_editor)));
        
        items.add(SettingItem.createSwitch(
            "show_line_numbers",
            context.getString(R.string.setting_show_line_numbers),
            context.getString(R.string.setting_show_line_numbers_summary),
            R.drawable.ic_format_list_numbered,
            true
        ));
        
        items.add(SettingItem.createSwitch(
            "show_invisible_chars",
            context.getString(R.string.setting_show_invisible_chars),
            context.getString(R.string.setting_show_invisible_chars_summary),
            R.drawable.ic_visibility,
            false
        ));

        // 文件类型设置
        items.add(SettingItem.createCategory("文件类型"));

        items.add(SettingItem.createAction(
            "supported_file_types",
            "支持的文件类型",
            "查看和管理支持的文本文件扩展名",
            R.drawable.ic_description
        ));
    }
    
    @Override
    public int getItemViewType(int position) {
        SettingItem item = items.get(position);
        switch (item.getType()) {
            case CATEGORY: return TYPE_CATEGORY;
            case SWITCH: return TYPE_SWITCH;
            case SLIDER: return TYPE_SLIDER;
            case SELECTION: return TYPE_SELECTION;
            case ACTION: return TYPE_ACTION;
            default: return TYPE_ACTION;
        }
    }
    
    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        LayoutInflater inflater = LayoutInflater.from(context);
        
        switch (viewType) {
            case TYPE_CATEGORY:
                return new CategoryViewHolder(inflater.inflate(R.layout.item_setting_category, parent, false));
            case TYPE_SWITCH:
                return new SwitchViewHolder(inflater.inflate(R.layout.item_setting_switch, parent, false));
            case TYPE_SLIDER:
                return new SliderViewHolder(inflater.inflate(R.layout.item_setting_slider, parent, false));
            case TYPE_SELECTION:
                return new SelectionViewHolder(inflater.inflate(R.layout.item_setting_selection, parent, false));
            case TYPE_ACTION:
            default:
                return new ActionViewHolder(inflater.inflate(R.layout.item_setting_action, parent, false));
        }
    }
    
    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        SettingItem item = items.get(position);
        
        if (holder instanceof CategoryViewHolder) {
            ((CategoryViewHolder) holder).bind(item);
        } else if (holder instanceof SwitchViewHolder) {
            ((SwitchViewHolder) holder).bind(item);
        } else if (holder instanceof SliderViewHolder) {
            ((SliderViewHolder) holder).bind(item);
        } else if (holder instanceof SelectionViewHolder) {
            ((SelectionViewHolder) holder).bind(item);
        } else if (holder instanceof ActionViewHolder) {
            ((ActionViewHolder) holder).bind(item);
        }
    }
    
    @Override
    public int getItemCount() {
        return items.size();
    }
    
    // ViewHolder classes
    static class CategoryViewHolder extends RecyclerView.ViewHolder {
        TextView titleText;
        
        CategoryViewHolder(@NonNull View itemView) {
            super(itemView);
            titleText = itemView.findViewById(R.id.tv_title);
        }
        
        void bind(SettingItem item) {
            titleText.setText(item.getTitle());
        }
    }
    
    class SwitchViewHolder extends RecyclerView.ViewHolder {
        ImageView iconView;
        TextView titleText;
        TextView summaryText;
        SwitchCompat switchView;
        
        SwitchViewHolder(@NonNull View itemView) {
            super(itemView);
            iconView = itemView.findViewById(R.id.iv_icon);
            titleText = itemView.findViewById(R.id.tv_title);
            summaryText = itemView.findViewById(R.id.tv_summary);
            switchView = itemView.findViewById(R.id.switch_setting);
        }
        
        void bind(SettingItem item) {
            if (item.getIconRes() != 0) {
                iconView.setImageResource(item.getIconRes());
                iconView.setVisibility(View.VISIBLE);
            } else {
                iconView.setVisibility(View.GONE);
            }
            
            titleText.setText(item.getTitle());
            summaryText.setText(item.getSummary());
            switchView.setChecked((Boolean) item.getValue());
            
            switchView.setOnCheckedChangeListener((buttonView, isChecked) -> {
                handleSwitchChange(item.getKey(), isChecked);
            });
        }
    }
    
    class SliderViewHolder extends RecyclerView.ViewHolder {
        ImageView iconView;
        TextView titleText;
        TextView summaryText;
        TextView valueText;
        SeekBar seekBar;
        
        SliderViewHolder(@NonNull View itemView) {
            super(itemView);
            iconView = itemView.findViewById(R.id.iv_icon);
            titleText = itemView.findViewById(R.id.tv_title);
            summaryText = itemView.findViewById(R.id.tv_summary);
            valueText = itemView.findViewById(R.id.tv_value);
            seekBar = itemView.findViewById(R.id.seek_bar);
        }
        
        void bind(SettingItem item) {
            if (item.getIconRes() != 0) {
                iconView.setImageResource(item.getIconRes());
                iconView.setVisibility(View.VISIBLE);
            } else {
                iconView.setVisibility(View.GONE);
            }
            
            titleText.setText(item.getTitle());
            summaryText.setText(item.getSummary());
            
            float value = (Float) item.getValue();
            float min = item.getMinValue();
            float max = item.getMaxValue();
            
            valueText.setText(String.format("%.0f", value));
            seekBar.setMax((int) (max - min));
            seekBar.setProgress((int) (value - min));
            
            seekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
                @Override
                public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                    if (fromUser) {
                        float newValue = min + progress;
                        valueText.setText(String.format("%.0f", newValue));
                        handleSliderChange(item.getKey(), newValue);
                    }
                }
                
                @Override
                public void onStartTrackingTouch(SeekBar seekBar) {}
                
                @Override
                public void onStopTrackingTouch(SeekBar seekBar) {}
            });
        }
    }
    
    class SelectionViewHolder extends RecyclerView.ViewHolder {
        ImageView iconView;
        TextView titleText;
        TextView summaryText;
        TextView valueText;
        
        SelectionViewHolder(@NonNull View itemView) {
            super(itemView);
            iconView = itemView.findViewById(R.id.iv_icon);
            titleText = itemView.findViewById(R.id.tv_title);
            summaryText = itemView.findViewById(R.id.tv_summary);
            valueText = itemView.findViewById(R.id.tv_value);
        }
        
        void bind(SettingItem item) {
            if (item.getIconRes() != 0) {
                iconView.setImageResource(item.getIconRes());
                iconView.setVisibility(View.VISIBLE);
            } else {
                iconView.setVisibility(View.GONE);
            }
            
            titleText.setText(item.getTitle());
            summaryText.setText(item.getSummary());
            valueText.setText(getDisplayValue(item.getValue()));
            
            itemView.setOnClickListener(v -> showSelectionDialog(item));
        }
    }
    
    class ActionViewHolder extends RecyclerView.ViewHolder {
        ImageView iconView;
        TextView titleText;
        TextView summaryText;
        
        ActionViewHolder(@NonNull View itemView) {
            super(itemView);
            iconView = itemView.findViewById(R.id.iv_icon);
            titleText = itemView.findViewById(R.id.tv_title);
            summaryText = itemView.findViewById(R.id.tv_summary);
        }
        
        void bind(SettingItem item) {
            if (item.getIconRes() != 0) {
                iconView.setImageResource(item.getIconRes());
                iconView.setVisibility(View.VISIBLE);
            } else {
                iconView.setVisibility(View.GONE);
            }
            
            titleText.setText(item.getTitle());
            summaryText.setText(item.getSummary());
            
            itemView.setOnClickListener(v -> handleActionClick(item.getKey()));
        }
    }
    
    // 处理设置变化的方法
    private void handleSwitchChange(String key, boolean value) {
        switch (key) {
            case "show_line_numbers":
                listener.onShowLineNumbersChanged(value);
                break;
            case "show_invisible_chars":
                listener.onShowInvisibleCharsChanged(value);
                break;
        }
    }

    private void handleSliderChange(String key, float value) {
        switch (key) {
            case "font_size":
                listener.onFontSizeChanged(value);
                break;
        }
    }

    private void showSelectionDialog(SettingItem item) {
        Object[] options = item.getOptions();
        String[] displayOptions = new String[options.length];
        int selectedIndex = 0;

        for (int i = 0; i < options.length; i++) {
            displayOptions[i] = getDisplayValue(options[i]);
            if (options[i].equals(item.getValue())) {
                selectedIndex = i;
            }
        }

        new MaterialAlertDialogBuilder(context)
            .setTitle(item.getTitle())
            .setSingleChoiceItems(displayOptions, selectedIndex, (dialog, which) -> {
                Object selectedValue = options[which];
                handleSelectionChange(item.getKey(), selectedValue);
                dialog.dismiss();
            })
            .setNegativeButton(android.R.string.cancel, null)
            .show();
    }

    private void handleSelectionChange(String key, Object value) {
        switch (key) {
            case "font_family":
                listener.onFontFamilyChanged((String) value);
                break;
            case "theme":
                listener.onThemeChanged((Theme) value);
                break;
            case "language":
                listener.onLanguageChanged((AppLanguage) value);
                break;
        }
    }

    private void handleActionClick(String key) {
        switch (key) {
            case "supported_file_types":
                listener.onSupportedFileTypesClicked();
                break;
        }
    }

    private String getDisplayValue(Object value) {
        if (value instanceof Theme) {
            Theme theme = (Theme) value;
            switch (theme) {
                case LIGHT: return context.getString(R.string.theme_light);
                case DARK: return context.getString(R.string.theme_dark);
                case SYSTEM: return context.getString(R.string.theme_system);
                default: return theme.toString();
            }
        } else if (value instanceof AppLanguage) {
            AppLanguage language = (AppLanguage) value;
            switch (language) {
                case CHINESE: return context.getString(R.string.language_chinese);
                case ENGLISH: return context.getString(R.string.language_english);
                case SYSTEM: return context.getString(R.string.language_system);
                default: return language.toString();
            }
        } else {
            return value.toString();
        }
    }

    // 更新方法
    public void updateFontSize(float fontSize) {
        updateItemValue("font_size", fontSize);
    }

    public void updateFontFamily(String fontFamily) {
        updateItemValue("font_family", fontFamily);
    }

    public void updateTheme(Theme theme) {
        updateItemValue("theme", theme);
    }

    public void updateLanguage(AppLanguage language) {
        updateItemValue("language", language);
    }

    public void updateShowLineNumbers(boolean show) {
        updateItemValue("show_line_numbers", show);
    }

    public void updateShowInvisibleChars(boolean show) {
        updateItemValue("show_invisible_chars", show);
    }

    private void updateItemValue(String key, Object value) {
        for (int i = 0; i < items.size(); i++) {
            SettingItem item = items.get(i);
            if (key.equals(item.getKey())) {
                // 创建新的SettingItem替换旧的
                SettingItem.Builder builder = new SettingItem.Builder()
                    .setType(item.getType())
                    .setKey(item.getKey())
                    .setTitle(item.getTitle())
                    .setSummary(item.getSummary())
                    .setIcon(item.getIconRes())
                    .setValue(value)
                    .setOptions(item.getOptions())
                    .setRange(item.getMinValue(), item.getMaxValue())
                    .setEnabled(item.isEnabled());

                items.set(i, builder.build());
                notifyItemChanged(i);
                break;
            }
        }
    }
}
