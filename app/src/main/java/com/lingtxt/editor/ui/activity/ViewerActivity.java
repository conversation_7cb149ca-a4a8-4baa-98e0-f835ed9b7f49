package com.lingtxt.editor.ui.activity;

import android.os.Bundle;
import android.widget.FrameLayout;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.lingtxt.editor.ui.editor.CodeEditText;

/**
 * 双模式演示Activity
 * 专门用于演示和测试双模式（查看/编辑）功能
 */
public class ViewerActivity extends AppCompatActivity {

    private CodeEditText codeEditText;
    private FloatingActionButton fabEdit;
    private FloatingActionButton fabView;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 创建根布局
        FrameLayout rootLayout = new FrameLayout(this);
        setContentView(rootLayout);
        
        // 初始化编辑器
        setupCodeEditor(rootLayout);
        
        // 初始化浮动按钮
        setupFloatingButtons(rootLayout);
        
        // 设置测试内容
        setupTestContent();
    }

    private void setupCodeEditor(FrameLayout rootLayout) {
        codeEditText = new CodeEditText(this);
        
        // 配置编辑器
        codeEditText.setShowLineNumbers(true);
        codeEditText.setSyntaxHighlightEnabled(true);
        codeEditText.setCursorMagnifierEnabled(true);
        codeEditText.setMagneticSnapEnabled(true);
        
        // 配置退出编辑模式的选项
        codeEditText.setAutoExitEditOnFocusLoss(false);      // 失去焦点时不自动退出
        codeEditText.setAutoExitEditOnKeyboardHide(true);    // 键盘隐藏时自动退出
        codeEditText.setDoubleClickExitEditEnabled(false);   // 双击空白区域退出（可选）

        // 设置模式变化监听器
        codeEditText.setOnModeChangeListener(new CodeEditText.OnModeChangeListener() {
            @Override
            public void onModeChanged(CodeEditText.ViewMode newMode) {
                updateUI(newMode);
                String modeText = newMode == CodeEditText.ViewMode.VIEW_ONLY ? "查看模式" : "编辑模式";
                showToast("切换到：" + modeText);
            }

            @Override
            public void onRequestEdit() {
                showToast("双击进入编辑模式");
                codeEditText.enterEditMode(true);
            }

            @Override
            public void onRequestView() {
                showToast("自动退出编辑模式");
                codeEditText.enterViewMode();
            }
        });
        
        // 设置字体大小变化监听器
        codeEditText.setOnFontSizeChangeListener(fontSize -> {
            showToast("字体大小：" + (int)fontSize + "sp");
        });
        
        // 添加到布局
        FrameLayout.LayoutParams editorParams = new FrameLayout.LayoutParams(
            FrameLayout.LayoutParams.MATCH_PARENT,
            FrameLayout.LayoutParams.MATCH_PARENT
        );
        rootLayout.addView(codeEditText, editorParams);
    }

    private void setupFloatingButtons(FrameLayout rootLayout) {
        // 编辑按钮
        fabEdit = new FloatingActionButton(this);
        fabEdit.setImageResource(android.R.drawable.ic_menu_edit);
        fabEdit.setOnClickListener(v -> {
            if (codeEditText.isViewOnlyMode()) {
                codeEditText.enterEditMode(true);
            }
        });
        
        FrameLayout.LayoutParams editParams = new FrameLayout.LayoutParams(
            FrameLayout.LayoutParams.WRAP_CONTENT,
            FrameLayout.LayoutParams.WRAP_CONTENT
        );
        editParams.gravity = android.view.Gravity.BOTTOM | android.view.Gravity.END;
        editParams.setMargins(0, 0, 64, 64);
        rootLayout.addView(fabEdit, editParams);
        
        // 查看按钮
        fabView = new FloatingActionButton(this);
        fabView.setImageResource(android.R.drawable.ic_menu_view);
        fabView.setOnClickListener(v -> {
            if (codeEditText.isEditMode()) {
                codeEditText.enterViewMode();
            }
        });
        
        FrameLayout.LayoutParams viewParams = new FrameLayout.LayoutParams(
            FrameLayout.LayoutParams.WRAP_CONTENT,
            FrameLayout.LayoutParams.WRAP_CONTENT
        );
        viewParams.gravity = android.view.Gravity.BOTTOM | android.view.Gravity.END;
        viewParams.setMargins(0, 0, 64, 150);
        rootLayout.addView(fabView, viewParams);
        
        // 初始状态
        updateUI(CodeEditText.ViewMode.VIEW_ONLY);
    }

    private void updateUI(CodeEditText.ViewMode mode) {
        switch (mode) {
            case VIEW_ONLY:
                fabEdit.show();
                fabView.hide();
                break;
            case EDIT_MODE:
                fabEdit.hide();
                fabView.show();
                break;
        }
    }

    private void setupTestContent() {
        String testCode = "# LingTxt 使用说明\\n\\n" +
                "LingTxt是一个专为安卓平台设计的轻量级文本编辑器。\\n\\n" +
                "## 双模式设计\\n\\n" +
                "### 📖 查看模式（默认）\\n" +
                "- 双指缩放调整字体大小\\n" +
                "- 滚动查看文档内容\\n" +
                "- 搜索和跳转功能\\n" +
                "- 复制文本内容\\n" +
                "- **双击文本区域进入编辑模式**\\n\\n" +
                "### ✏️ 编辑模式\\n" +
                "- 所有查看模式功能\\n" +
                "- 文本编辑和输入\\n" +
                "- 光标放大镜辅助定位\\n" +
                "- 磁吸效果精确选择\\n" +
                "- 智能键盘管理\\n\\n" +
                "## 手势操作\\n\\n" +
                "1. **双指缩放**：调整字体大小（任何模式）\\n" +
                "2. **双击文本**：查看模式下进入编辑模式\\n" +
                "3. **长按文本**：编辑模式下显示放大镜\\n" +
                "4. **单击定位**：编辑模式下光标磁吸到单词边界\\n\\n" +
                "## 退出编辑模式的方式\\n\\n" +
                "1. **返回键**：按手机返回键退出编辑\\n" +
                "2. **查看按钮**：点击右下角眼睛图标\\n" +
                "3. **隐藏键盘**：隐藏键盘时自动退出编辑\\n" +
                "4. **失去焦点**：点击其他区域（可配置）\\n\\n" +
                "## 智能键盘管理\\n\\n" +
                "- 查看模式：永不显示键盘\\n" +
                "- 编辑模式：按需显示键盘\\n" +
                "- 缩放时：临时隐藏键盘，缩放结束后恢复\\n" +
                "- 键盘隐藏：自动退出编辑模式\\n\\n" +
                "---\\n\\n" +
                "**提示**：双击进入编辑模式，然后尝试不同的退出方式！";
        
        codeEditText.setText(testCode);
        codeEditText.setFileName("README.md");
    }

    private void showToast(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }

    @Override
    public void onBackPressed() {
        // 如果在编辑模式，先切换到查看模式
        if (codeEditText.isEditMode()) {
            codeEditText.enterViewMode();
        } else {
            super.onBackPressed();
        }
    }
}