package com.lingtxt.editor.ui.widget;

import android.content.Context;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.ArrayAdapter;
import android.widget.AutoCompleteTextView;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import com.lingtxt.editor.R;

/**
 * 搜索栏组件
 * 提供文本搜索和替换功能的UI界面
 */
public class SearchBar extends LinearLayout {

    // 搜索相关控件
    private AutoCompleteTextView etSearchText;
    private AutoCompleteTextView etReplaceText;
    private ImageButton btnPrevious;
    private ImageButton btnNext;
    private ImageButton btnReplace;
    private ImageButton btnReplaceAll;
    private ImageButton btnClose;
    
    // 搜索选项
    private CheckBox cbCaseSensitive;
    private CheckBox cbWholeWord;
    private CheckBox cbRegex;
    
    // 状态显示
    private TextView tvSearchResult;
    
    // 替换模式控件容器
    private LinearLayout layoutReplace;
    
    // 状态
    private boolean isReplaceMode = false;
    private boolean isVisible = false;
    
    // 监听器
    private OnSearchActionListener searchActionListener;
    
    // 搜索历史管理器
    private SearchHistoryManager historyManager;
    private ArrayAdapter<String> searchAdapter;
    private ArrayAdapter<String> replaceAdapter;
    
    public SearchBar(Context context) {
        super(context);
        init();
    }

    public SearchBar(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public SearchBar(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        setOrientation(VERTICAL);
        setVisibility(GONE);
        
        LayoutInflater.from(getContext()).inflate(R.layout.widget_search_bar, this, true);
        
        // 初始化搜索历史管理器
        historyManager = new SearchHistoryManager(getContext());
        
        initViews();
        setupHistoryAdapters();
        setupListeners();
    }

    private void initViews() {
        etSearchText = findViewById(R.id.et_search_text);
        etReplaceText = findViewById(R.id.et_replace_text);
        btnPrevious = findViewById(R.id.btn_previous);
        btnNext = findViewById(R.id.btn_next);
        btnReplace = findViewById(R.id.btn_replace);
        btnReplaceAll = findViewById(R.id.btn_replace_all);
        btnClose = findViewById(R.id.btn_close);
        
        cbCaseSensitive = findViewById(R.id.cb_case_sensitive);
        cbWholeWord = findViewById(R.id.cb_whole_word);
        cbRegex = findViewById(R.id.cb_regex);
        
        tvSearchResult = findViewById(R.id.tv_search_result);
        layoutReplace = findViewById(R.id.layout_replace);
        
        // NonEditableTextView已经自动处理了所有禁用输入的逻辑
    }

    /**
     * 设置搜索历史适配器
     */
    private void setupHistoryAdapters() {
        // 搜索历史适配器
        searchAdapter = new ArrayAdapter<>(getContext(), 
            android.R.layout.simple_dropdown_item_1line, 
            historyManager.getSearchHistory());
        etSearchText.setAdapter(searchAdapter);
        etSearchText.setThreshold(1); // 输入1个字符就显示提示
        
        // 替换历史适配器
        replaceAdapter = new ArrayAdapter<>(getContext(), 
            android.R.layout.simple_dropdown_item_1line, 
            historyManager.getReplaceHistory());
        etReplaceText.setAdapter(replaceAdapter);
        etReplaceText.setThreshold(1);
    }

    /**
     * 更新搜索历史
     */
    private void updateSearchHistory() {
        searchAdapter.clear();
        searchAdapter.addAll(historyManager.getSearchHistory());
        searchAdapter.notifyDataSetChanged();
    }

    /**
     * 更新替换历史
     */
    private void updateReplaceHistory() {
        replaceAdapter.clear();
        replaceAdapter.addAll(historyManager.getReplaceHistory());
        replaceAdapter.notifyDataSetChanged();
    }

    private void setupListeners() {
        // 搜索文本变化监听
        etSearchText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                performSearch();
            }

            @Override
            public void afterTextChanged(Editable s) {}
        });

        // 搜索框回车键监听
        etSearchText.setOnEditorActionListener((v, actionId, event) -> {
            if (actionId == EditorInfo.IME_ACTION_SEARCH || actionId == EditorInfo.IME_ACTION_NEXT) {
                findNext();
                return true;
            }
            return false;
        });

        // 替换框回车键监听
        etReplaceText.setOnEditorActionListener((v, actionId, event) -> {
            if (actionId == EditorInfo.IME_ACTION_DONE) {
                replaceCurrent();
                return true;
            }
            return false;
        });

        // 按钮点击监听
        btnPrevious.setOnClickListener(v -> findPrevious());
        btnNext.setOnClickListener(v -> findNext());
        btnReplace.setOnClickListener(v -> replaceCurrent());
        btnReplaceAll.setOnClickListener(v -> replaceAll());
        btnClose.setOnClickListener(v -> hide());

        // 搜索选项变化监听
        cbCaseSensitive.setOnCheckedChangeListener((buttonView, isChecked) -> performSearch());
        cbWholeWord.setOnCheckedChangeListener((buttonView, isChecked) -> performSearch());
        cbRegex.setOnCheckedChangeListener((buttonView, isChecked) -> performSearch());
    }

    /**
     * 显示搜索栏 - 从底部平滑弹出
     */
    public void show(boolean replaceMode) {
        isReplaceMode = replaceMode;
        isVisible = true;
        
        layoutReplace.setVisibility(replaceMode ? VISIBLE : GONE);
        setVisibility(VISIBLE);
        
        // 等待布局完成后再开始动画
        post(() -> {
            // 设置初始状态（从底部开始）
            float height = getHeight();
            if (height == 0) {
                // 如果高度还是0，强制测量
                measure(MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED),
                       MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED));
                height = getMeasuredHeight();
            }
            
            setTranslationY(height);
            
            // 平滑动画弹出
            animate()
                .translationY(0)
                .setDuration(300)
                .setInterpolator(new android.view.animation.DecelerateInterpolator())
                .start();
        });
        
        // 聚焦到搜索框
        etSearchText.requestFocus();
        
        // 如果有选中文本，自动填入搜索框
        if (searchActionListener != null) {
            String selectedText = searchActionListener.getSelectedText();
            if (selectedText != null && !selectedText.isEmpty()) {
                etSearchText.setText(selectedText);
                etSearchText.selectAll();
            }
        }
        
        // 显示软键盘
        postDelayed(() -> {
            android.view.inputmethod.InputMethodManager imm = 
                (android.view.inputmethod.InputMethodManager) getContext().getSystemService(android.content.Context.INPUT_METHOD_SERVICE);
            if (imm != null) {
                imm.showSoftInput(etSearchText, android.view.inputmethod.InputMethodManager.SHOW_IMPLICIT);
            }
        }, 100);
    }

    /**
     * 隐藏搜索栏 - 平滑滑出到底部
     */
    public void hide() {
        isVisible = false;
        
        // 隐藏软键盘
        android.view.inputmethod.InputMethodManager imm = 
            (android.view.inputmethod.InputMethodManager) getContext().getSystemService(android.content.Context.INPUT_METHOD_SERVICE);
        if (imm != null) {
            imm.hideSoftInputFromWindow(getWindowToken(), 0);
        }
        
        // 获取当前高度进行动画
        float height = getHeight();
        if (height > 0) {
            // 平滑动画滑出
            animate()
                .translationY(height)
                .setDuration(300)
                .setInterpolator(new android.view.animation.AccelerateInterpolator())
                .withEndAction(() -> {
                    setVisibility(GONE);
                    setTranslationY(0); // 重置位置
                })
                .start();
        } else {
            // 如果高度为0，直接隐藏
            setVisibility(GONE);
        }
        
        // 清除搜索高亮
        if (searchActionListener != null) {
            searchActionListener.clearSearch();
        }
    }

    /**
     * 切换替换模式
     */
    public void toggleReplaceMode() {
        isReplaceMode = !isReplaceMode;
        layoutReplace.setVisibility(isReplaceMode ? VISIBLE : GONE);
    }

    /**
     * 执行搜索
     */
    private void performSearch() {
        String searchText = etSearchText.getText().toString();
        
        // 保存搜索历史（只有当搜索文本不为空时）
        if (!searchText.trim().isEmpty()) {
            historyManager.addSearchHistory(searchText);
            updateSearchHistory();
        }
        
        if (searchActionListener != null) {
            TextSearchManager.SearchOptions options = new TextSearchManager.SearchOptions(
                cbCaseSensitive.isChecked(),
                cbWholeWord.isChecked(),
                cbRegex.isChecked(),
                true // wrapAround
            );
            
            searchActionListener.onSearch(searchText, options);
        }
    }

    /**
     * 查找下一个
     */
    private void findNext() {
        if (searchActionListener != null) {
            searchActionListener.onFindNext();
        }
    }

    /**
     * 查找上一个
     */
    private void findPrevious() {
        if (searchActionListener != null) {
            searchActionListener.onFindPrevious();
        }
    }

    /**
     * 替换当前
     */
    private void replaceCurrent() {
        String replaceText = etReplaceText.getText().toString();
        
        // 保存替换历史
        if (!replaceText.trim().isEmpty()) {
            historyManager.addReplaceHistory(replaceText);
            updateReplaceHistory();
        }
        
        if (searchActionListener != null) {
            searchActionListener.onReplaceCurrent(replaceText);
        }
    }

    /**
     * 替换全部
     */
    private void replaceAll() {
        String replaceText = etReplaceText.getText().toString();
        
        // 保存替换历史
        if (!replaceText.trim().isEmpty()) {
            historyManager.addReplaceHistory(replaceText);
            updateReplaceHistory();
        }
        
        if (searchActionListener != null) {
            searchActionListener.onReplaceAll(replaceText);
        }
    }

    /**
     * 更新搜索结果显示
     */
    public void updateSearchResult(int totalCount, int currentIndex) {
        if (totalCount == 0) {
            tvSearchResult.setText(getContext().getString(R.string.search_not_found));
            btnPrevious.setEnabled(false);
            btnNext.setEnabled(false);
            btnReplace.setEnabled(false);
            btnReplaceAll.setEnabled(false);
        } else {
            tvSearchResult.setText(String.format("%d / %d", currentIndex, totalCount));
            btnPrevious.setEnabled(true);
            btnNext.setEnabled(true);
            btnReplace.setEnabled(isReplaceMode);
            btnReplaceAll.setEnabled(isReplaceMode);
        }
    }

    /**
     * 显示搜索错误
     */
    public void showSearchError(String error) {
        String errorText = getContext().getString(R.string.search_error_prefix, error);
        tvSearchResult.setText(errorText);
        btnPrevious.setEnabled(false);
        btnNext.setEnabled(false);
        btnReplace.setEnabled(false);
        btnReplaceAll.setEnabled(false);
    }

    /**
     * 获取搜索文本
     */
    public String getSearchText() {
        return etSearchText.getText().toString();
    }

    /**
     * 设置搜索文本
     */
    public void setSearchText(String text) {
        etSearchText.setText(text);
    }

    /**
     * 获取替换文本
     */
    public String getReplaceText() {
        return etReplaceText.getText().toString();
    }

    /**
     * 设置替换文本
     */
    public void setReplaceText(String text) {
        etReplaceText.setText(text);
    }

    /**
     * 获取搜索选项
     */
    public TextSearchManager.SearchOptions getSearchOptions() {
        return new TextSearchManager.SearchOptions(
            cbCaseSensitive.isChecked(),
            cbWholeWord.isChecked(),
            cbRegex.isChecked(),
            true
        );
    }

    /**
     * 设置搜索选项
     */
    public void setSearchOptions(TextSearchManager.SearchOptions options) {
        cbCaseSensitive.setChecked(options.caseSensitive);
        cbWholeWord.setChecked(options.wholeWord);
        cbRegex.setChecked(options.useRegex);
    }

    /**
     * 检查是否可见
     */
    public boolean isSearchBarVisible() {
        return isVisible;
    }

    /**
     * 检查是否为替换模式
     */
    public boolean isReplaceMode() {
        return isReplaceMode;
    }

    /**
     * 搜索动作监听器
     */
    public interface OnSearchActionListener {
        void onSearch(String searchText, TextSearchManager.SearchOptions options);
        void onFindNext();
        void onFindPrevious();
        void onReplaceCurrent(String replaceText);
        void onReplaceAll(String replaceText);
        void clearSearch();
        String getSelectedText();
    }

    public void setOnSearchActionListener(OnSearchActionListener listener) {
        this.searchActionListener = listener;
    }
}