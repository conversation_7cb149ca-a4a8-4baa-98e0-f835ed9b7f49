package com.lingtxt.editor.ui.editor;

import android.text.Editable;
import android.widget.EditText;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 文本选择和操作辅助类
 */
public class TextSelectionHelper {

    private final EditText editText;

    public TextSelectionHelper(EditText editText) {
        this.editText = editText;
    }

    /**
     * 选择当前单词
     */
    public boolean selectCurrentWord() {
        int cursor = editText.getSelectionStart();
        String text = editText.getText().toString();
        
        if (cursor < 0 || cursor >= text.length()) {
            return false;
        }

        // 找到单词边界
        int start = findWordStart(text, cursor);
        int end = findWordEnd(text, cursor);

        if (start < end) {
            editText.setSelection(start, end);
            return true;
        }
        
        return false;
    }

    /**
     * 选择当前行
     */
    public boolean selectCurrentLine() {
        int cursor = editText.getSelectionStart();
        String text = editText.getText().toString();
        
        if (cursor < 0) {
            return false;
        }

        // 找到行的开始和结束
        int start = text.lastIndexOf('\n', cursor - 1) + 1;
        int end = text.indexOf('\n', cursor);
        if (end == -1) {
            end = text.length();
        }

        editText.setSelection(start, end);
        return true;
    }

    /**
     * 删除当前行
     */
    public boolean deleteCurrentLine() {
        int cursor = editText.getSelectionStart();
        String text = editText.getText().toString();
        
        if (cursor < 0) {
            return false;
        }

        // 找到行的开始和结束（包括换行符）
        int start = text.lastIndexOf('\n', cursor - 1) + 1;
        int end = text.indexOf('\n', cursor);
        
        if (end == -1) {
            // 最后一行
            end = text.length();
            // 如果不是第一行，包括前面的换行符
            if (start > 0) {
                start--;
            }
        } else {
            // 包括换行符
            end++;
        }

        Editable editable = editText.getText();
        if (editable != null && start < end) {
            editable.delete(start, end);
            return true;
        }
        
        return false;
    }

    /**
     * 复制当前行
     */
    public boolean duplicateCurrentLine() {
        int cursor = editText.getSelectionStart();
        String text = editText.getText().toString();
        
        if (cursor < 0) {
            return false;
        }

        // 找到当前行
        int lineStart = text.lastIndexOf('\n', cursor - 1) + 1;
        int lineEnd = text.indexOf('\n', cursor);
        if (lineEnd == -1) {
            lineEnd = text.length();
        }

        String currentLine = text.substring(lineStart, lineEnd);
        
        // 在当前行后插入复制的行
        Editable editable = editText.getText();
        if (editable != null) {
            editable.insert(lineEnd, "\n" + currentLine);
            return true;
        }
        
        return false;
    }

    /**
     * 向上移动当前行
     */
    public boolean moveLineUp() {
        int cursor = editText.getSelectionStart();
        String text = editText.getText().toString();
        
        if (cursor < 0) {
            return false;
        }

        // 找到当前行和上一行
        int currentLineStart = text.lastIndexOf('\n', cursor - 1) + 1;
        int currentLineEnd = text.indexOf('\n', cursor);
        if (currentLineEnd == -1) {
            currentLineEnd = text.length();
        }

        // 如果已经是第一行，无法上移
        if (currentLineStart == 0) {
            return false;
        }

        int prevLineStart = text.lastIndexOf('\n', currentLineStart - 2) + 1;
        
        String currentLine = text.substring(currentLineStart, currentLineEnd);
        String prevLine = text.substring(prevLineStart, currentLineStart - 1);

        Editable editable = editText.getText();
        if (editable != null) {
            // 替换两行的内容
            editable.replace(prevLineStart, currentLineEnd, currentLine + "\n" + prevLine);
            
            // 调整光标位置
            int newCursor = cursor - (currentLineStart - prevLineStart);
            editText.setSelection(newCursor);
            return true;
        }
        
        return false;
    }

    /**
     * 向下移动当前行
     */
    public boolean moveLineDown() {
        int cursor = editText.getSelectionStart();
        String text = editText.getText().toString();
        
        if (cursor < 0) {
            return false;
        }

        // 找到当前行和下一行
        int currentLineStart = text.lastIndexOf('\n', cursor - 1) + 1;
        int currentLineEnd = text.indexOf('\n', cursor);
        if (currentLineEnd == -1) {
            currentLineEnd = text.length();
        }

        int nextLineEnd = text.indexOf('\n', currentLineEnd + 1);
        if (nextLineEnd == -1) {
            nextLineEnd = text.length();
        }

        // 如果已经是最后一行，无法下移
        if (currentLineEnd == text.length()) {
            return false;
        }

        String currentLine = text.substring(currentLineStart, currentLineEnd);
        String nextLine = text.substring(currentLineEnd + 1, nextLineEnd);

        Editable editable = editText.getText();
        if (editable != null) {
            // 替换两行的内容
            editable.replace(currentLineStart, nextLineEnd, nextLine + "\n" + currentLine);
            
            // 调整光标位置
            int newCursor = cursor + (nextLine.length() + 1);
            editText.setSelection(newCursor);
            return true;
        }
        
        return false;
    }

    /**
     * 增加选中文本的缩进
     */
    public boolean indentSelection() {
        int start = editText.getSelectionStart();
        int end = editText.getSelectionEnd();
        
        if (start == end) {
            // 没有选择，缩进当前行
            return indentCurrentLine();
        }

        String text = editText.getText().toString();
        
        // 找到选择范围内的所有行
        int lineStart = text.lastIndexOf('\n', start - 1) + 1;
        int lineEnd = text.indexOf('\n', end - 1);
        if (lineEnd == -1) {
            lineEnd = text.length();
        }

        String selectedText = text.substring(lineStart, lineEnd);
        String[] lines = selectedText.split("\n", -1);
        
        StringBuilder indentedText = new StringBuilder();
        for (int i = 0; i < lines.length; i++) {
            if (i > 0) {
                indentedText.append("\n");
            }
            indentedText.append("    ").append(lines[i]); // 4个空格缩进
        }

        Editable editable = editText.getText();
        if (editable != null) {
            editable.replace(lineStart, lineEnd, indentedText.toString());
            
            // 调整选择范围
            int newStart = start + 4;
            int newEnd = end + (lines.length * 4);
            editText.setSelection(newStart, newEnd);
            return true;
        }
        
        return false;
    }

    /**
     * 减少选中文本的缩进
     */
    public boolean unindentSelection() {
        int start = editText.getSelectionStart();
        int end = editText.getSelectionEnd();
        
        if (start == end) {
            // 没有选择，反缩进当前行
            return unindentCurrentLine();
        }

        String text = editText.getText().toString();
        
        // 找到选择范围内的所有行
        int lineStart = text.lastIndexOf('\n', start - 1) + 1;
        int lineEnd = text.indexOf('\n', end - 1);
        if (lineEnd == -1) {
            lineEnd = text.length();
        }

        String selectedText = text.substring(lineStart, lineEnd);
        String[] lines = selectedText.split("\n", -1);
        
        StringBuilder unindentedText = new StringBuilder();
        int totalRemoved = 0;
        
        for (int i = 0; i < lines.length; i++) {
            if (i > 0) {
                unindentedText.append("\n");
            }
            
            String line = lines[i];
            int removed = 0;
            
            // 移除最多4个空格或1个tab
            if (line.startsWith("    ")) {
                line = line.substring(4);
                removed = 4;
            } else if (line.startsWith("\t")) {
                line = line.substring(1);
                removed = 1;
            } else {
                // 移除开头的空格，最多4个
                int spaceCount = 0;
                while (spaceCount < line.length() && spaceCount < 4 && line.charAt(spaceCount) == ' ') {
                    spaceCount++;
                }
                if (spaceCount > 0) {
                    line = line.substring(spaceCount);
                    removed = spaceCount;
                }
            }
            
            unindentedText.append(line);
            totalRemoved += removed;
        }

        Editable editable = editText.getText();
        if (editable != null) {
            editable.replace(lineStart, lineEnd, unindentedText.toString());
            
            // 调整选择范围
            int newStart = Math.max(lineStart, start - 4);
            int newEnd = Math.max(newStart, end - totalRemoved);
            editText.setSelection(newStart, newEnd);
            return true;
        }
        
        return false;
    }

    private boolean indentCurrentLine() {
        int cursor = editText.getSelectionStart();
        String text = editText.getText().toString();
        
        int lineStart = text.lastIndexOf('\n', cursor - 1) + 1;
        
        Editable editable = editText.getText();
        if (editable != null) {
            editable.insert(lineStart, "    ");
            return true;
        }
        
        return false;
    }

    private boolean unindentCurrentLine() {
        int cursor = editText.getSelectionStart();
        String text = editText.getText().toString();
        
        int lineStart = text.lastIndexOf('\n', cursor - 1) + 1;
        int lineEnd = text.indexOf('\n', cursor);
        if (lineEnd == -1) {
            lineEnd = text.length();
        }

        String line = text.substring(lineStart, lineEnd);
        
        // 移除开头的缩进
        int removed = 0;
        if (line.startsWith("    ")) {
            removed = 4;
        } else if (line.startsWith("\t")) {
            removed = 1;
        } else {
            // 移除开头的空格
            while (removed < line.length() && removed < 4 && line.charAt(removed) == ' ') {
                removed++;
            }
        }

        if (removed > 0) {
            Editable editable = editText.getText();
            if (editable != null) {
                editable.delete(lineStart, lineStart + removed);
                return true;
            }
        }
        
        return false;
    }

    private int findWordStart(String text, int cursor) {
        int start = cursor;
        while (start > 0 && isWordCharacter(text.charAt(start - 1))) {
            start--;
        }
        return start;
    }

    private int findWordEnd(String text, int cursor) {
        int end = cursor;
        while (end < text.length() && isWordCharacter(text.charAt(end))) {
            end++;
        }
        return end;
    }

    private boolean isWordCharacter(char c) {
        return Character.isLetterOrDigit(c) || c == '_';
    }
}