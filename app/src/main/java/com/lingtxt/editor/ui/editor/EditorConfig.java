package com.lingtxt.editor.ui.editor;

/**
 * 编辑器配置类
 */
public class EditorConfig {
    
    // 字体设置
    private float fontSize = 14f;
    private String fontFamily = "monospace";
    
    // 显示设置
    private boolean showLineNumbers = true;

    // 编辑设置
    private int tabSize = 4;
    private boolean useSpacesForTabs = true;



    // Getters
    public float getFontSize() { return fontSize; }
    public String getFontFamily() { return fontFamily; }


    // Setters with validation
    public EditorConfig setFontSize(float fontSize) {
        this.fontSize = Math.max(8f, Math.min(72f, fontSize));
        return this;
    }

    public EditorConfig setFontFamily(String fontFamily) {
        this.fontFamily = fontFamily != null ? fontFamily : "monospace";
        return this;
    }


    @Override
    public String toString() {
        return "EditorConfig{" +
                "fontSize=" + fontSize +
                ", fontFamily='" + fontFamily + '\'' +
                ", showLineNumbers=" + showLineNumbers +
                ", tabSize=" + tabSize +
                ", useSpacesForTabs=" + useSpacesForTabs +
                '}';
    }
}