package com.lingtxt.editor.ui.gesture;

import android.content.Context;
import android.view.MotionEvent;
import android.view.ViewConfiguration;

/**
 * 边缘滑动手势检测器
 * 检测从屏幕四个边缘向内滑动的手势
 */
public class EdgeSwipeGestureDetector {

    public enum Edge {
        LEFT, RIGHT, TOP, BOTTOM
    }

    public interface OnEdgeSwipeListener {
        void onEdgeSwipe(Edge edge, float distance);
        void onEdgeSwipeStart(Edge edge);
        void onEdgeSwipeEnd(Edge edge);
    }

    private final Context context;
    private final OnEdgeSwipeListener listener;
    private final float edgeThreshold; // 边缘检测阈值
    private final float minSwipeDistance; // 最小滑动距离
    
    private boolean isSwipeInProgress = false;
    private Edge currentEdge = null;
    private float startX, startY;
    private float lastX, lastY;
    private int screenWidth, screenHeight;

    public EdgeSwipeGestureDetector(Context context, OnEdgeSwipeListener listener) {
        this.context = context;
        this.listener = listener;
        
        ViewConfiguration config = ViewConfiguration.get(context);
        this.minSwipeDistance = config.getScaledTouchSlop() * 2;
        
        // 边缘阈值设为20dp
        float density = context.getResources().getDisplayMetrics().density;
        this.edgeThreshold = 20 * density;
        
        // 获取屏幕尺寸
        android.util.DisplayMetrics metrics = context.getResources().getDisplayMetrics();
        screenWidth = metrics.widthPixels;
        screenHeight = metrics.heightPixels;
    }

    public boolean onTouchEvent(MotionEvent event) {
        float x = event.getX();
        float y = event.getY();

        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                startX = x;
                startY = y;
                lastX = x;
                lastY = y;
                
                // 检查是否在边缘区域开始触摸
                Edge edge = detectEdge(x, y);
                if (edge != null) {
                    currentEdge = edge;
                    isSwipeInProgress = false; // 等待实际滑动开始
                    return true;
                }
                break;

            case MotionEvent.ACTION_MOVE:
                if (currentEdge != null) {
                    float deltaX = x - startX;
                    float deltaY = y - startY;
                    float distance = (float) Math.sqrt(deltaX * deltaX + deltaY * deltaY);
                    
                    // 检查是否开始滑动
                    if (!isSwipeInProgress && distance > minSwipeDistance) {
                        if (isValidSwipeDirection(currentEdge, deltaX, deltaY)) {
                            isSwipeInProgress = true;
                            if (listener != null) {
                                listener.onEdgeSwipeStart(currentEdge);
                            }
                        } else {
                            // 滑动方向不正确，取消
                            currentEdge = null;
                            return false;
                        }
                    }
                    
                    // 如果正在滑动，通知监听器
                    if (isSwipeInProgress && listener != null) {
                        float swipeDistance = calculateSwipeDistance(currentEdge, deltaX, deltaY);
                        listener.onEdgeSwipe(currentEdge, swipeDistance);
                    }
                    
                    lastX = x;
                    lastY = y;
                    return isSwipeInProgress;
                }
                break;

            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                if (isSwipeInProgress && listener != null) {
                    listener.onEdgeSwipeEnd(currentEdge);
                }
                
                isSwipeInProgress = false;
                currentEdge = null;
                break;
        }

        return false;
    }

    /**
     * 检测触摸点是否在边缘区域
     */
    private Edge detectEdge(float x, float y) {
        if (x <= edgeThreshold) {
            return Edge.LEFT;
        } else if (x >= screenWidth - edgeThreshold) {
            return Edge.RIGHT;
        } else if (y <= edgeThreshold) {
            return Edge.TOP;
        } else if (y >= screenHeight - edgeThreshold) {
            return Edge.BOTTOM;
        }
        return null;
    }

    /**
     * 检查滑动方向是否有效
     */
    private boolean isValidSwipeDirection(Edge edge, float deltaX, float deltaY) {
        float absDeltaX = Math.abs(deltaX);
        float absDeltaY = Math.abs(deltaY);
        
        switch (edge) {
            case LEFT:
                return deltaX > 0 && absDeltaX > absDeltaY; // 向右滑动
            case RIGHT:
                return deltaX < 0 && absDeltaX > absDeltaY; // 向左滑动
            case TOP:
                return deltaY > 0 && absDeltaY > absDeltaX; // 向下滑动
            case BOTTOM:
                return deltaY < 0 && absDeltaY > absDeltaX; // 向上滑动
        }
        return false;
    }

    /**
     * 计算滑动距离
     */
    private float calculateSwipeDistance(Edge edge, float deltaX, float deltaY) {
        switch (edge) {
            case LEFT:
            case RIGHT:
                return Math.abs(deltaX);
            case TOP:
            case BOTTOM:
                return Math.abs(deltaY);
        }
        return 0;
    }

    /**
     * 更新屏幕尺寸
     */
    public void updateScreenSize(int width, int height) {
        this.screenWidth = width;
        this.screenHeight = height;
    }
}