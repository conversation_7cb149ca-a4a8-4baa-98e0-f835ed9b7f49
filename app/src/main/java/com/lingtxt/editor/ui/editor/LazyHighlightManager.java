package com.lingtxt.editor.ui.editor;

import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.util.Log;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.lingtxt.editor.syntax.SyntaxHighlighter;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

/**
 * 懒加载语法高亮管理器
 * 只对可见区域进行语法高亮处理，提高大文件的渲染性能
 */
public class LazyHighlightManager {
    
    private static final String TAG = "LazyHighlightManager";
    
    // 配置参数
    private static final int MAX_CACHE_SIZE = 100;        // 最大缓存块数
    private static final int HIGHLIGHT_CHUNK_SIZE = 50;   // 每个高亮块的行数
    private static final int PRELOAD_CHUNKS = 2;          // 预加载的块数（上下各2块）
    
    // 语法高亮器
    private SyntaxHighlighter syntaxHighlighter;
    private String fileExtension;
    private boolean isEnabled = true;
    
    // 缓存管理 (LRU)
    private final LinkedHashMap<Integer, HighlightChunk> highlightCache = 
        new LinkedHashMap<Integer, HighlightChunk>(MAX_CACHE_SIZE, 0.75f, true) {
            @Override
            protected boolean removeEldestEntry(Map.Entry<Integer, HighlightChunk> eldest) {
                return size() > MAX_CACHE_SIZE;
            }
        };
    
    // 异步处理
    private final ExecutorService executorService = Executors.newSingleThreadExecutor();
    private Future<?> currentHighlightTask;
    
    // 监听器
    private HighlightListener listener;
    
    /**
     * 高亮块数据
     */
    private static class HighlightChunk {
        final int chunkIndex;
        final int startLine;
        final int endLine;
        final SpannableStringBuilder highlightedText;
        final long timestamp;
        
        HighlightChunk(int chunkIndex, int startLine, int endLine, 
                      SpannableStringBuilder highlightedText) {
            this.chunkIndex = chunkIndex;
            this.startLine = startLine;
            this.endLine = endLine;
            this.highlightedText = highlightedText;
            this.timestamp = System.currentTimeMillis();
        }
    }
    
    /**
     * 高亮事件监听器
     */
    public interface HighlightListener {
        /**
         * 高亮完成时调用
         */
        void onHighlightCompleted(int startLine, int endLine, SpannableStringBuilder highlightedText);
        
        /**
         * 高亮进度更新
         */
        void onHighlightProgress(int completedChunks, int totalChunks);
    }
    
    public LazyHighlightManager(@NonNull SyntaxHighlighter syntaxHighlighter) {
        this.syntaxHighlighter = syntaxHighlighter;
    }
    
    /**
     * 设置监听器
     */
    public void setListener(@Nullable HighlightListener listener) {
        this.listener = listener;
    }
    
    /**
     * 设置文件扩展名
     */
    public void setFileExtension(@Nullable String fileExtension) {
        this.fileExtension = fileExtension;
        // 清空缓存，因为语言可能变化
        clearCache();
    }
    
    /**
     * 启用或禁用懒加载高亮
     */
    public void setEnabled(boolean enabled) {
        this.isEnabled = enabled;
        if (!enabled) {
            cancelCurrentTask();
            clearCache();
        }
    }
    
    /**
     * 请求高亮指定区域
     */
    public void requestHighlight(@NonNull String[] lines, int startLine, int endLine) {
        if (!isEnabled || syntaxHighlighter == null || fileExtension == null) {
            return;
        }
        
        // 取消当前任务
        cancelCurrentTask();
        
        // 计算需要高亮的块
        int startChunk = startLine / HIGHLIGHT_CHUNK_SIZE;
        int endChunk = (endLine - 1) / HIGHLIGHT_CHUNK_SIZE;
        
        // 扩展到预加载区域
        int preloadStartChunk = Math.max(0, startChunk - PRELOAD_CHUNKS);
        int preloadEndChunk = Math.min((lines.length - 1) / HIGHLIGHT_CHUNK_SIZE, endChunk + PRELOAD_CHUNKS);
        
        Log.d(TAG, "Requesting highlight for chunks " + preloadStartChunk + " to " + preloadEndChunk);
        
        // 异步处理高亮
        currentHighlightTask = executorService.submit(() -> {
            processHighlightChunks(lines, preloadStartChunk, preloadEndChunk, startChunk, endChunk);
        });
    }
    
    /**
     * 处理高亮块
     */
    private void processHighlightChunks(String[] lines, int preloadStartChunk, int preloadEndChunk,
                                       int priorityStartChunk, int priorityEndChunk) {
        try {
            int totalChunks = preloadEndChunk - preloadStartChunk + 1;
            int completedChunks = 0;
            
            // 优先处理可见区域
            for (int chunkIndex = priorityStartChunk; chunkIndex <= priorityEndChunk; chunkIndex++) {
                if (Thread.currentThread().isInterrupted()) {
                    return;
                }
                
                processChunk(lines, chunkIndex, true);
                completedChunks++;
                
                if (listener != null) {
                    listener.onHighlightProgress(completedChunks, totalChunks);
                }
            }
            
            // 处理预加载区域
            for (int chunkIndex = preloadStartChunk; chunkIndex <= preloadEndChunk; chunkIndex++) {
                if (Thread.currentThread().isInterrupted()) {
                    return;
                }
                
                // 跳过已处理的优先区域
                if (chunkIndex >= priorityStartChunk && chunkIndex <= priorityEndChunk) {
                    continue;
                }
                
                processChunk(lines, chunkIndex, false);
                completedChunks++;
                
                if (listener != null) {
                    listener.onHighlightProgress(completedChunks, totalChunks);
                }
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error processing highlight chunks", e);
        }
    }
    
    /**
     * 处理单个高亮块
     */
    private void processChunk(String[] lines, int chunkIndex, boolean isPriority) {
        // 检查缓存
        HighlightChunk cachedChunk = highlightCache.get(chunkIndex);
        if (cachedChunk != null) {
            if (isPriority && listener != null) {
                listener.onHighlightCompleted(cachedChunk.startLine, cachedChunk.endLine, 
                    cachedChunk.highlightedText);
            }
            return;
        }
        
        // 计算块的行范围
        int startLine = chunkIndex * HIGHLIGHT_CHUNK_SIZE;
        int endLine = Math.min(startLine + HIGHLIGHT_CHUNK_SIZE, lines.length);
        
        if (startLine >= lines.length) {
            return;
        }
        
        // 构建块内容
        StringBuilder chunkContent = new StringBuilder();
        for (int i = startLine; i < endLine; i++) {
            chunkContent.append(lines[i]);
            if (i < endLine - 1) {
                chunkContent.append('\n');
            }
        }
        
        // 执行语法高亮
        SpannableStringBuilder highlightedText = new SpannableStringBuilder();
        try {
            android.text.Spannable highlighted = syntaxHighlighter.highlight(chunkContent.toString());
            highlightedText.append(highlighted);
        } catch (Exception e) {
            Log.w(TAG, "Failed to highlight chunk " + chunkIndex, e);
            highlightedText.append(chunkContent.toString());
        }
        
        // 创建高亮块
        HighlightChunk chunk = new HighlightChunk(chunkIndex, startLine, endLine, highlightedText);
        
        // 添加到缓存
        synchronized (highlightCache) {
            highlightCache.put(chunkIndex, chunk);
        }
        
        // 通知监听器（仅对优先区域）
        if (isPriority && listener != null) {
            listener.onHighlightCompleted(startLine, endLine, highlightedText);
        }
        
        Log.d(TAG, "Highlighted chunk " + chunkIndex + " (lines " + startLine + "-" + (endLine-1) + ")");
    }

    
    /**
     * 取消当前高亮任务
     */
    public void cancelCurrentTask() {
        if (currentHighlightTask != null && !currentHighlightTask.isDone()) {
            currentHighlightTask.cancel(true);
            Log.d(TAG, "Cancelled current highlight task");
        }
    }
    
    /**
     * 清空缓存
     */
    public void clearCache() {
        synchronized (highlightCache) {
            highlightCache.clear();
        }
        Log.d(TAG, "Highlight cache cleared");
    }

    
    /**
     * 清理资源
     */
    public void cleanup() {
        cancelCurrentTask();
        clearCache();
        executorService.shutdown();
        listener = null;
        Log.d(TAG, "LazyHighlightManager cleaned up");
    }
}
