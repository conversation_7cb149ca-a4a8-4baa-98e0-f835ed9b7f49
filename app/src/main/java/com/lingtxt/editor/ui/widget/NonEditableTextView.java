package com.lingtxt.editor.ui.widget;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import androidx.appcompat.widget.AppCompatTextView;

/**
 * 不可编辑的TextView
 * 完全禁用输入法和编辑功能
 */
public class NonEditableTextView extends AppCompatTextView {

    public NonEditableTextView(Context context) {
        super(context);
        init();
    }

    public NonEditableTextView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public NonEditableTextView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        // 完全禁用所有输入相关功能
        setFocusable(false);
        setFocusableInTouchMode(false);
        setClickable(false);
        setLongClickable(false);
        setCursorVisible(false);
        setKeyListener(null);
        setInputType(android.text.InputType.TYPE_NULL);
        setTextIsSelectable(false);
        
        // 额外的防护措施
        setEnabled(true); // 保持enabled状态以维持正常外观
        clearFocus(); // 清除任何现有焦点
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        // 完全拦截触摸事件，不传递给父类
        return true;
    }

    @Override
    public boolean performClick() {
        // 禁用点击事件
        return true;
    }

    @Override
    public boolean performLongClick() {
        // 禁用长按事件
        return true;
    }

    @Override
    public void onWindowFocusChanged(boolean hasWindowFocus) {
        // 不调用父类方法，避免获得焦点
    }

    @SuppressLint("MissingSuperCall")
    @Override
    protected void onFocusChanged(boolean focused, int direction, android.graphics.Rect previouslyFocusedRect) {
        // 强制失去焦点
        if (focused) {
            // 立即清除焦点
            post(() -> {
                clearFocus();
                // 隐藏输入法
                InputMethodManager imm = (InputMethodManager) getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
                if (imm != null) {
                    imm.hideSoftInputFromWindow(getWindowToken(), 0);
                }
                
                // 尝试将焦点转移到父视图
                if (getParent() instanceof View) {
                    ((View) getParent()).requestFocus();
                }
            });
        }
        // 不调用父类方法，完全阻止焦点变化
        //super.onFocusChanged(focused, direction, previouslyFocusedRect);
    }

    @Override
    public boolean requestFocus(int direction, android.graphics.Rect previouslyFocusedRect) {
        // 拒绝获得焦点
        return false;
    }

    // 注意：requestFocus() 和 requestFocusFromTouch() 在某些Android版本中是final方法，无法重写
    // 我们通过其他方式来防止焦点获取
}