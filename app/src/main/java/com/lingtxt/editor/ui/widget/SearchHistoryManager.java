package com.lingtxt.editor.ui.widget;

import android.content.Context;
import android.content.SharedPreferences;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 搜索历史记录管理器
 * 管理搜索和替换的历史记录
 */
public class SearchHistoryManager {
    
    private static final String PREFS_NAME = "search_history";
    private static final String KEY_SEARCH_HISTORY = "search_history";
    private static final String KEY_REPLACE_HISTORY = "replace_history";
    private static final int MAX_HISTORY_SIZE = 20; // 最大历史记录数量
    
    private final SharedPreferences preferences;
    private final List<String> searchHistory;
    private final List<String> replaceHistory;
    
    public SearchHistoryManager(Context context) {
        preferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        searchHistory = new ArrayList<>();
        replaceHistory = new ArrayList<>();
        
        loadHistory();
    }
    
    /**
     * 加载历史记录
     */
    private void loadHistory() {
        Set<String> searchSet = preferences.getStringSet(KEY_SEARCH_HISTORY, new HashSet<>());
        Set<String> replaceSet = preferences.getStringSet(KEY_REPLACE_HISTORY, new HashSet<>());
        
        searchHistory.clear();
        searchHistory.addAll(searchSet);
        
        replaceHistory.clear();
        replaceHistory.addAll(replaceSet);
    }
    
    /**
     * 保存历史记录
     */
    private void saveHistory() {
        SharedPreferences.Editor editor = preferences.edit();
        
        Set<String> searchSet = new HashSet<>(searchHistory);
        Set<String> replaceSet = new HashSet<>(replaceHistory);
        
        editor.putStringSet(KEY_SEARCH_HISTORY, searchSet);
        editor.putStringSet(KEY_REPLACE_HISTORY, replaceSet);
        editor.apply();
    }
    
    /**
     * 添加搜索历史
     */
    public void addSearchHistory(String searchText) {
        if (searchText == null || searchText.trim().isEmpty()) {
            return;
        }
        
        searchText = searchText.trim();
        
        // 如果已存在，先移除
        searchHistory.remove(searchText);
        
        // 添加到开头
        searchHistory.add(0, searchText);
        
        // 限制历史记录数量
        while (searchHistory.size() > MAX_HISTORY_SIZE) {
            searchHistory.remove(searchHistory.size() - 1);
        }
        
        saveHistory();
    }
    
    /**
     * 添加替换历史
     */
    public void addReplaceHistory(String replaceText) {
        if (replaceText == null || replaceText.trim().isEmpty()) {
            return;
        }
        
        replaceText = replaceText.trim();
        
        // 如果已存在，先移除
        replaceHistory.remove(replaceText);
        
        // 添加到开头
        replaceHistory.add(0, replaceText);
        
        // 限制历史记录数量
        while (replaceHistory.size() > MAX_HISTORY_SIZE) {
            replaceHistory.remove(replaceHistory.size() - 1);
        }
        
        saveHistory();
    }
    
    /**
     * 获取搜索历史
     */
    public List<String> getSearchHistory() {
        return new ArrayList<>(searchHistory);
    }
    
    /**
     * 获取替换历史
     */
    public List<String> getReplaceHistory() {
        return new ArrayList<>(replaceHistory);
    }
    
    /**
     * 清除搜索历史
     */
    public void clearSearchHistory() {
        searchHistory.clear();
        saveHistory();
    }
    
    /**
     * 清除替换历史
     */
    public void clearReplaceHistory() {
        replaceHistory.clear();
        saveHistory();
    }
    
    /**
     * 清除所有历史
     */
    public void clearAllHistory() {
        searchHistory.clear();
        replaceHistory.clear();
        saveHistory();
    }
    
    /**
     * 删除指定的搜索历史项
     */
    public void removeSearchHistory(String searchText) {
        searchHistory.remove(searchText);
        saveHistory();
    }
    
    /**
     * 删除指定的替换历史项
     */
    public void removeReplaceHistory(String replaceText) {
        replaceHistory.remove(replaceText);
        saveHistory();
    }
    
    /**
     * 获取搜索历史数量
     */
    public int getSearchHistoryCount() {
        return searchHistory.size();
    }
    
    /**
     * 获取替换历史数量
     */
    public int getReplaceHistoryCount() {
        return replaceHistory.size();
    }
    
    /**
     * 检查是否有搜索历史
     */
    public boolean hasSearchHistory() {
        return !searchHistory.isEmpty();
    }
    
    /**
     * 检查是否有替换历史
     */
    public boolean hasReplaceHistory() {
        return !replaceHistory.isEmpty();
    }
}