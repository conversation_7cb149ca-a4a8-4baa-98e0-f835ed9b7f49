package com.lingtxt.editor.ui.main;

import static com.lingtxt.editor.utils.FileUtils.getFileNameFromUri;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.res.Configuration;
import android.net.Uri;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.TextView;


import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.lifecycle.ViewModelProvider;

import com.google.android.material.snackbar.Snackbar;
import com.lingtxt.editor.LingTxtApplication;
import com.lingtxt.editor.R;
import com.lingtxt.editor.base.BaseActivity;
import com.lingtxt.editor.data.model.AppLanguage;
import com.lingtxt.editor.data.model.Theme;
import com.lingtxt.editor.data.repository.FileRepositoryImpl;
import com.lingtxt.editor.data.repository.RecentFileRepository;
import com.lingtxt.editor.data.repository.SettingsRepository;
import com.lingtxt.editor.data.repository.SettingsRepositoryImpl;
import com.lingtxt.editor.databinding.ActivityMainBinding;
import com.lingtxt.editor.di.ViewModelFactory;
import com.lingtxt.editor.ui.editor.CodeEditText;
import com.lingtxt.editor.ui.editor.LargeFileStrategy;
import com.lingtxt.editor.ui.settings.SettingsActivity;
import com.lingtxt.editor.ui.widget.FocusModeManager;
import com.lingtxt.editor.ui.widget.SearchBar;
import com.lingtxt.editor.ui.widget.TextSearchManager;
import com.lingtxt.editor.ui.widget.UndoRedoManager;
import com.lingtxt.editor.utils.FileAccessHelper;
import com.lingtxt.editor.utils.FileInfoHelper;
import com.lingtxt.editor.utils.FileSaveHelper;
import com.lingtxt.editor.utils.GlobalExceptionHandler;
import com.lingtxt.editor.utils.PermissionManager;
import com.lingtxt.editor.utils.SafeFileLoader;
import com.lingtxt.editor.utils.SettingsChangeManager;
import com.lingtxt.editor.utils.UserFeedbackManager;
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.schedulers.Schedulers;

import java.io.File;

import javax.inject.Inject;

/**
 * 主Activity，处理文件关联和应用入口
 */
public class MainActivity extends BaseActivity<ActivityMainBinding, MainViewModel>
        implements SettingsChangeManager.SettingsChangeListener {

    @Inject
    ViewModelFactory viewModelFactory;

    @Inject
    SettingsRepository settingsRepository;
    
    // 编辑器组件
    private CodeEditText codeEditText;
    private SearchBar searchBar;
    private UndoRedoManager undoRedoManager;
    private TextSearchManager textSearchManager;
    
    // 状态显示
    private TextView tvStatusInfo;

    // 最近文件管理
    private RecentFileRepository recentFileRepository;

    // 文件访问对话框
    private AlertDialog fileAccessDialog;

    // 当前文件信息
    private Uri currentFileUri;
    private String currentFileName;
    private File currentFile;

    // 键盘状态跟踪
    private boolean isKeyboardVisible = false;

    // 专注模式状态跟踪
    private boolean isFocusMode = false;



    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        // 注入依赖 - 必须在super.onCreate()之前
        Log.d("MainActivity", "Before injection, viewModelFactory = " + viewModelFactory);
        ((LingTxtApplication) getApplication()).getAppComponent().inject(this);
        Log.d("MainActivity", "After injection, viewModelFactory = " + viewModelFactory);
        
        super.onCreate(savedInstanceState);

        // 注册设置变更监听器
        SettingsChangeManager.getInstance().registerListener(this);

        // 加载和应用设置
        loadAndApplySettings();

        // 处理Intent
        handleIntent(getIntent());
    }

    @Override
    protected void onResume() {
        super.onResume();
        // 从设置页面返回时重新加载设置
        loadAndApplySettings();

        // 额外检查语言设置是否需要应用
        checkAndApplyLanguageSettings();

        // 重新检查权限状态，如果已有权限则关闭对话框
        if (fileAccessDialog != null && fileAccessDialog.isShowing()) {
            if (PermissionManager.hasBasicStoragePermission(this) || PermissionManager.hasStoragePermission(this)) {
                Log.d("MainActivity", "Permissions granted, dismissing dialog");
                fileAccessDialog.dismiss();
                fileAccessDialog = null;
                UserFeedbackManager.getInstance().showToast(this, "权限已授权，可以正常使用");
            }
        }

        // 确保横屏时的全屏模式得到维护
        updateFullscreenMode();
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);

        if (hasFocus) {
            // 窗口获得焦点时，确保横屏全屏模式
            boolean isLandscape = getResources().getConfiguration().orientation ==
                Configuration.ORIENTATION_LANDSCAPE;

            if (isLandscape) {
                Log.d("MainActivity", "Window focus gained in landscape, ensuring fullscreen");
                // 延迟一点执行，确保系统UI状态稳定
                getWindow().getDecorView().postDelayed(() -> {
                    updateFullscreenMode();
                }, 100);
            }
        }
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        setIntent(intent);
        handleIntent(intent);
    }



    @Override
    protected int getLayoutId() {
        return R.layout.activity_main;
    }

    @Override
    protected Class<MainViewModel> getViewModelClass() {
        return MainViewModel.class;
    }

    @Override
    protected void setViewModel() {
        // 调试信息
        Log.d("MainActivity", "setViewModel called, viewModelFactory = " + viewModelFactory);

        if (viewModelFactory == null) {
            // 临时解决方案：创建一个简单的ViewModel实例
            Log.w("MainActivity", "ViewModelFactory is null, using temporary solution");
            viewModel = createTemporaryViewModel();
        } else {
            viewModel = new ViewModelProvider(this, viewModelFactory).get(MainViewModel.class);
        }
        binding.setViewModel(viewModel);
    }
    
    /**
     * 临时创建ViewModel的方法，用于解决依赖注入问题
     */
    private MainViewModel createTemporaryViewModel() {
        // 创建临时的Repository实例
        FileRepositoryImpl fileRepo = new FileRepositoryImpl(this);

        // 创建SharedPreferences实例
        SharedPreferences sharedPreferences =
            getSharedPreferences("app_settings", MODE_PRIVATE);
        SettingsRepositoryImpl settingsRepo = new SettingsRepositoryImpl(sharedPreferences);

        return new MainViewModel(fileRepo, settingsRepo, this);
    }

    @Override
    protected void initializeUI() {
        // 检查崩溃恢复
        checkCrashRecovery();

        // 检查权限
        checkPermissions();

        // 设置工具栏
        setSupportActionBar(binding.toolbar);

        // 初始化编辑器组件
        initializeEditorComponents();

        // 确保所有管理器正确初始化（必须在initializeEditorComponents之后）
        ensureUndoRedoManagerInitialized();

        // 初始化UI组件
        setupBottomAppBar();
        setupEmptyState();
        
        // 设置沉浸式状态栏
        setupImmersiveMode();



        // 初始化最近文件管理器
        recentFileRepository = new RecentFileRepository(this);

        // 初始化主内容区域边距
        initializeMainContentMargin();

        // 添加键盘状态调试监听器
        setupKeyboardDebugListener();

        // 设置屏幕方向变化监听
        setupOrientationChangeListener();

        // 设置系统UI可见性变化监听器
        setupSystemUiVisibilityListener();
    }

    @Override
    protected void observeData() {
        // 观察文件加载状态
        viewModel.getFileLoadingState().observe(this, isLoading -> {
            if (isLoading) {
                // 显示加载指示器
                binding.progressBar.setVisibility(View.VISIBLE);
            } else {
                // 隐藏加载指示器
                binding.progressBar.setVisibility(View.GONE);
            }
        });

        // 观察错误信息
        viewModel.getErrorMessage().observe(this, errorMessage -> {
            if (errorMessage != null && !errorMessage.isEmpty()) {
                // 显示错误信息
                Snackbar.make(
                    binding.getRoot(),
                    errorMessage,
                    Snackbar.LENGTH_LONG
                ).show();
            }
        });
        
        // 观察文件内容变化
        viewModel.getFileContent().observe(this, content -> {
            if (content != null && !content.isEmpty()) {
                // 检查是否有文件策略信息
                LargeFileStrategy.FileInfo fileInfo = viewModel.getFileStrategy().getValue();
                if (fileInfo != null && codeEditText != null) {
                    // 使用大文件优化设置文本
                    codeEditText.setTextWithOptimization(content, fileInfo);
                } else if (codeEditText != null) {
                    // 普通设置文本
                    codeEditText.setText(content);
                }

                // 文件加载完成，更新编辑器状态
                updateEditorState();

                // 清空撤销/重做历史
                if (undoRedoManager != null) {
                    undoRedoManager.clearHistory();
                }
            }
        });
        
        // 观察文件名变化，用于语法高亮
        viewModel.getFileName().observe(this, fileName -> {
            if (fileName != null && codeEditText != null) {
                codeEditText.setFileName(fileName);
            }
        });
        
        // 观察文件编码变化
        viewModel.getFileEncoding().observe(this, encoding -> {
            if (encoding != null) {
                currentEncoding = encoding;
                updateStatusDisplay();
            }
        });
        
        // 观察文件大小变化
        viewModel.getFileSize().observe(this, size -> {
            if (size != null) {
                currentFileSize = size;
                updateStatusDisplay();
            }
        });

        // 观察文件策略变化
        viewModel.getFileStrategy().observe(this, fileInfo -> {
            if (fileInfo != null) {
                handleFileStrategy(fileInfo);
            }
        });

        // 观察文件警告信息
        viewModel.getFileWarning().observe(this, warning -> {
            if (warning != null && !warning.isEmpty()) {
                showFileWarning(warning);
            }
        });
    }

    /**
     * 更新编辑器状态
     */
    private void updateEditorState() {
        if (codeEditText != null) {
            // 更新光标位置显示
            updateCursorPosition();
            
            // 设置焦点到编辑器
            codeEditText.requestFocus();
        }
    }

    /**
     * 处理Intent，包括文件关联
     */
    private void handleIntent(Intent intent) {
        if (intent != null) {
            String action = intent.getAction();
            if (Intent.ACTION_VIEW.equals(action)) {
                Uri uri = intent.getData();
                if (uri != null) {
                    // 保存当前文件信息
                    currentFileUri = uri;
                    currentFileName = getFileNameFromUri(uri);

                    // 通过ViewModel加载文件
                    viewModel.loadFile(uri);

                    // 添加到最近文件列表
                    addToRecentFiles(uri);
                }
            }
        }
    }



    /**
     * 初始化编辑器组件
     */
    private void initializeEditorComponents() {
        // 获取组件引用
        codeEditText = binding.codeEditText;
        searchBar = binding.searchBar;
        tvStatusInfo = binding.tvStatusInfo;

        // 初始化撤销/重做管理器
        undoRedoManager = new UndoRedoManager(codeEditText);
        // 如果codeEditText为null，稍后会通过setEditText设置
        if (codeEditText == null) {
            Log.w("MainActivity", "codeEditText is null during initialization, will set later");
        }
        undoRedoManager.setOnUndoRedoStateChangedListener((canUndo, canRedo) -> {
            // 更新底部菜单按钮状态
            updateUndoRedoMenuState(canUndo, canRedo);
        });
        
        // 初始化搜索管理器
        textSearchManager = new TextSearchManager(codeEditText);
        // 如果codeEditText为null，稍后会通过setEditText设置
        if (codeEditText == null) {
            android.util.Log.w("MainActivity", "codeEditText is null during TextSearchManager initialization, will set later");
        }
        textSearchManager.setOnSearchResultListener(new TextSearchManager.OnSearchResultListener() {
            @Override
            public void onSearchResult(int totalCount, int currentIndex) {
                searchBar.updateSearchResult(totalCount, currentIndex);
            }

            @Override
            public void onSearchError(String error) {
                searchBar.showSearchError(error);
            }
        });
        
        // 设置搜索栏监听器
        setupSearchBarListeners();

        // 设置光标位置变化监听器
        if (codeEditText != null) {
            codeEditText.setOnSelectionChangedListener((selStart, selEnd) -> {
                updateCursorPosition();
            });
        } else {
            android.util.Log.w("MainActivity", "codeEditText is null during selection listener setup");
        }
        
        // 配置双模式功能
        setupDualModeFeatures();
        
        // 初始化沉浸式阅读功能
        setupImmersiveReading();
    }

    /**
     * 初始化沉浸式阅读功能
     */
    private void setupImmersiveReading() {
        if (codeEditText == null) return;

        // 创建专注模式管理器
        FocusModeManager focusModeManager = new FocusModeManager(this);

        // 设置到编辑器（这会自动调用setTargetEditor）
        codeEditText.setFocusModeManager(focusModeManager);

        // 添加可隐藏的UI元素
        focusModeManager.addHidableView(binding.toolbar);
        focusModeManager.addHidableView(binding.bottomAppBar);

        // 将进度指示器添加到主布局
        focusModeManager.attachProgressIndicatorToParent((android.view.ViewGroup) binding.getRoot());
        
        // 设置专注模式变化监听器
        focusModeManager.setOnFocusModeChangeListener(new FocusModeManager.OnFocusModeChangeListener() {
            @Override
            public void onEnterFocusMode() {
                // 进入专注模式时的处理
                updateStatusBarForFocusMode(true);
                adjustMainContentMarginForFocusMode(true);
            }

            @Override
            public void onExitFocusMode() {
                // 退出专注模式时的处理
                updateStatusBarForFocusMode(false);
                adjustMainContentMarginForFocusMode(false);

                // 如果键盘仍然显示，需要重新隐藏底部菜单栏
                if (isKeyboardVisible) {
                    android.util.Log.d("MainActivity", "Exit focus mode but keyboard still visible, hiding bottomAppBar again");
                    if (binding.bottomAppBar != null) {
                        binding.bottomAppBar.setVisibility(android.view.View.GONE);
                    }
                }
            }

            @Override
            public void onUserInteraction() {
                // 用户交互时的处理
            }
        });
        
        // 启动自动隐藏计时器
        focusModeManager.startAutoHideTimer();
    }
    
    /**
     * 更新状态栏以适应专注模式
     */
    private void updateStatusBarForFocusMode(boolean isFocusMode) {
        boolean isLandscape = getResources().getConfiguration().orientation ==
            android.content.res.Configuration.ORIENTATION_LANDSCAPE;

        android.util.Log.d("MainActivity", "updateStatusBarForFocusMode: isFocusMode=" + isFocusMode +
            ", isLandscape=" + isLandscape);

        if (isLandscape) {
            // 横屏时：始终保持完全全屏模式，不管是否专注模式
            getWindow().getDecorView().setSystemUiVisibility(
                android.view.View.SYSTEM_UI_FLAG_LAYOUT_STABLE |
                android.view.View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION |
                android.view.View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN |
                android.view.View.SYSTEM_UI_FLAG_HIDE_NAVIGATION |
                android.view.View.SYSTEM_UI_FLAG_FULLSCREEN |
                android.view.View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
            );
            android.util.Log.d("MainActivity", "Landscape: maintaining fullscreen mode");
        } else {
            // 竖屏时：根据专注模式状态调整
            if (isFocusMode) {
                // 专注模式：隐藏状态栏
                getWindow().getDecorView().setSystemUiVisibility(
                    android.view.View.SYSTEM_UI_FLAG_LAYOUT_STABLE |
                    android.view.View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION |
                    android.view.View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN |
                    android.view.View.SYSTEM_UI_FLAG_HIDE_NAVIGATION |
                    android.view.View.SYSTEM_UI_FLAG_FULLSCREEN |
                    android.view.View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                );
                android.util.Log.d("MainActivity", "Portrait focus mode: hiding status bar");
            } else {
                // 正常模式：显示状态栏
                getWindow().getDecorView().setSystemUiVisibility(
                    android.view.View.SYSTEM_UI_FLAG_LAYOUT_STABLE |
                    android.view.View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION |
                    android.view.View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                );
                android.util.Log.d("MainActivity", "Portrait normal mode: showing status bar");
            }
        }
    }

    /**
     * 调整主内容区域边距以适应专注模式
     */
    private void adjustMainContentMarginForFocusMode(boolean isFocusMode) {
        // 获取主内容区域
        androidx.constraintlayout.widget.ConstraintLayout mainContent =
            findViewById(R.id.mainContentLayout);

        if (mainContent == null) {
            // 如果没有找到mainContentLayout，尝试查找第一个ConstraintLayout
            android.view.ViewGroup rootView = (android.view.ViewGroup) binding.getRoot();
            for (int i = 0; i < rootView.getChildCount(); i++) {
                android.view.View child = rootView.getChildAt(i);
                if (child instanceof androidx.constraintlayout.widget.ConstraintLayout) {
                    mainContent = (androidx.constraintlayout.widget.ConstraintLayout) child;
                    break;
                }
            }
        }

        if (mainContent != null) {
            androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams params =
                (androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams) mainContent.getLayoutParams();

            if (isFocusMode) {
                // 专注模式：移除底部边距
                params.bottomMargin = 0;
            } else {
                // 正常模式：恢复底部边距（但要考虑键盘状态）
                if (isKeyboardVisible) {
                    // 如果键盘显示，不设置底部边距
                    params.bottomMargin = 0;
                    android.util.Log.d("MainActivity", "Exit focus mode but keyboard visible, no bottom margin");
                } else {
                    // 键盘隐藏时才设置底部边距
                    int bottomAppBarHeight = (int) (56 * getResources().getDisplayMetrics().density); // 56dp转px
                    params.bottomMargin = bottomAppBarHeight;
                    android.util.Log.d("MainActivity", "Exit focus mode and keyboard hidden, restored bottom margin");
                }
            }

            mainContent.setLayoutParams(params);
        }
    }

    /**
     * 初始化主内容区域边距
     */
    private void initializeMainContentMargin() {
        androidx.constraintlayout.widget.ConstraintLayout mainContent =
            findViewById(R.id.mainContentLayout);

        if (mainContent != null) {
            androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams params =
                (androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams) mainContent.getLayoutParams();

            // 初始状态：为底部菜单栏预留空间
            int bottomAppBarHeight = (int) (56 * getResources().getDisplayMetrics().density); // 56dp转px
            params.bottomMargin = bottomAppBarHeight;
            mainContent.setLayoutParams(params);

            android.util.Log.d("MainActivity", "Initialized main content margin: " + bottomAppBarHeight + "px");
        }
    }

    /**
     * 调试方法：检查当前布局状态
     */
    private void debugLayoutState(String context) {
        android.util.Log.d("MainActivity", "=== Debug Layout State: " + context + " ===");

        if (binding.bottomAppBar != null) {
            android.util.Log.d("MainActivity", "BottomAppBar visibility: " + binding.bottomAppBar.getVisibility());
            android.util.Log.d("MainActivity", "BottomAppBar alpha: " + binding.bottomAppBar.getAlpha());
            android.util.Log.d("MainActivity", "BottomAppBar translationY: " + binding.bottomAppBar.getTranslationY());
            android.util.Log.d("MainActivity", "BottomAppBar height: " + binding.bottomAppBar.getHeight());
            android.util.Log.d("MainActivity", "BottomAppBar menu items: " + binding.bottomAppBar.getMenu().size());
        }

        androidx.constraintlayout.widget.ConstraintLayout mainContent = findViewById(R.id.mainContentLayout);
        if (mainContent != null) {
            androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams params =
                (androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams) mainContent.getLayoutParams();
            android.util.Log.d("MainActivity", "MainContent bottomMargin: " + params.bottomMargin + "px");
            android.util.Log.d("MainActivity", "MainContent height: " + mainContent.getHeight());
        }

        android.util.Log.d("MainActivity", "=== End Debug Layout State ===");
    }

    /**
     * 设置键盘调试监听器
     */
    private void setupKeyboardDebugListener() {
        android.view.ViewGroup rootView = findViewById(android.R.id.content);

        // 添加基于WindowInsets的键盘检测（API 21+）
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {
            rootView.setOnApplyWindowInsetsListener((v, insets) -> {
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.R) {
                    // API 30+ 使用新的WindowInsets API
                    boolean keyboardVisible = insets.isVisible(android.view.WindowInsets.Type.ime());
                    android.util.Log.d("MainActivity", "WindowInsets keyboard detection: " + keyboardVisible);

                    if (keyboardVisible != isKeyboardVisible) {
                        android.util.Log.d("MainActivity", "WindowInsets keyboard state changed: " +
                            isKeyboardVisible + " -> " + keyboardVisible);
                        isKeyboardVisible = keyboardVisible;

                        boolean isLandscape = getResources().getConfiguration().orientation ==
                            android.content.res.Configuration.ORIENTATION_LANDSCAPE;

                        if (isLandscape) {
                            // 横屏时：键盘显示/隐藏直接控制专注模式
                            if (keyboardVisible) {
                                android.util.Log.d("MainActivity", "WindowInsets Landscape: keyboard shown, entering focus mode");
                                // 延迟一点执行，确保键盘完全显示
                                getWindow().getDecorView().postDelayed(() -> {
                                    if (isKeyboardVisible) { // 再次确认键盘状态
                                        enterFocusMode();
                                    }
                                }, 100);
                            } else {
                                android.util.Log.d("MainActivity", "WindowInsets Landscape: keyboard hidden, exiting focus mode");
                                // 延迟一点执行，确保键盘完全隐藏
                                getWindow().getDecorView().postDelayed(() -> {
                                    if (!isKeyboardVisible) { // 再次确认键盘状态
                                        exitFocusMode();
                                    }
                                }, 100);
                            }
                        } else {
                            // 竖屏时：调整菜单和边距
                            adjustMainContentMarginForKeyboard(keyboardVisible);
                        }
                        debugLayoutState(keyboardVisible ? "Keyboard Shown (WindowInsets)" : "Keyboard Hidden (WindowInsets)");
                    }
                }
                return insets;
            });
        }

        rootView.getViewTreeObserver().addOnGlobalLayoutListener(new android.view.ViewTreeObserver.OnGlobalLayoutListener() {
            private int previousHeight = 0;

            @Override
            public void onGlobalLayout() {
                android.graphics.Rect rect = new android.graphics.Rect();
                rootView.getWindowVisibleDisplayFrame(rect);

                int currentHeight = rect.height();

                if (previousHeight == 0) {
                    previousHeight = currentHeight;
                    debugLayoutState("Initial");
                    return;
                }

                int heightDiff = previousHeight - currentHeight;

                // 根据屏幕方向调整键盘检测阈值
                boolean isLandscape = getResources().getConfiguration().orientation ==
                    android.content.res.Configuration.ORIENTATION_LANDSCAPE;
                int keyboardThreshold = isLandscape ?
                    (int) (80 * getResources().getDisplayMetrics().density) :   // 横屏时使用更小的阈值
                    (int) (150 * getResources().getDisplayMetrics().density);   // 竖屏时使用原来的阈值

                // 横屏时使用更敏感的检测
                if (isLandscape && Math.abs(heightDiff) > 50) {
                    android.util.Log.d("MainActivity", "Landscape sensitive detection - heightDiff: " + heightDiff);
                }

                android.util.Log.d("MainActivity", "Keyboard detection - isLandscape: " + isLandscape +
                    ", heightDiff: " + heightDiff + ", threshold: " + keyboardThreshold +
                    ", previousHeight: " + previousHeight + ", currentHeight: " + currentHeight);

                if (Math.abs(heightDiff) > keyboardThreshold) {
                    if (heightDiff > 0) {
                        android.util.Log.d("MainActivity", "Keyboard SHOWN: heightDiff=" + heightDiff +
                            ", isLandscape=" + isLandscape + ", threshold=" + keyboardThreshold);
                        // 更新键盘状态
                        isKeyboardVisible = true;

                        if (isLandscape) {
                            // 横屏时：键盘显示时进入专注模式
                            android.util.Log.d("MainActivity", "GlobalLayout Landscape: keyboard shown, entering focus mode");
                            // 延迟执行，确保键盘状态稳定
                            rootView.postDelayed(() -> {
                                if (isKeyboardVisible && !isFocusMode) {
                                    enterFocusMode();
                                    android.util.Log.d("MainActivity", "GlobalLayout: Focus mode entered for landscape keyboard");
                                }
                            }, 150);
                        } else {
                            // 竖屏时：隐藏菜单栏并调整边距
                            adjustMainContentMarginForKeyboard(true);
                        }
                        debugLayoutState("Keyboard Shown");
                    } else {
                        android.util.Log.d("MainActivity", "Keyboard HIDDEN: heightDiff=" + heightDiff +
                            ", isLandscape=" + isLandscape + ", threshold=" + keyboardThreshold);
                        // 更新键盘状态
                        isKeyboardVisible = false;

                        if (isLandscape) {
                            // 横屏时：键盘隐藏时退出专注模式
                            android.util.Log.d("MainActivity", "GlobalLayout Landscape: keyboard hidden, exiting focus mode");
                            // 延迟执行，确保键盘状态稳定
                            rootView.postDelayed(() -> {
                                if (!isKeyboardVisible && isFocusMode) {
                                    exitFocusMode();
                                    android.util.Log.d("MainActivity", "GlobalLayout: Focus mode exited for landscape keyboard hide");
                                }
                            }, 150);
                        } else {
                            // 竖屏时：恢复菜单栏和边距
                            adjustMainContentMarginForKeyboard(false);
                        }
                        debugLayoutState("Keyboard Hidden");
                    }
                } else {
                    android.util.Log.v("MainActivity", "Height change below threshold: heightDiff=" + heightDiff +
                        ", threshold=" + keyboardThreshold);
                }

                previousHeight = currentHeight;
            }
        });
    }

    /**
     * 确保UndoRedoManager正确初始化
     */
    private void ensureUndoRedoManagerInitialized() {
        if (undoRedoManager != null && codeEditText != null) {
            // 重新设置EditText以确保TextWatcher正确添加
            undoRedoManager.setEditText(codeEditText);
            android.util.Log.d("MainActivity", "UndoRedoManager re-initialized with codeEditText");
        } else {
            android.util.Log.w("MainActivity", "Cannot initialize UndoRedoManager: undoRedoManager=" +
                undoRedoManager + ", codeEditText=" + codeEditText);
        }

        // 同时确保TextSearchManager正确初始化
        if (textSearchManager != null && codeEditText != null) {
            textSearchManager.setEditText(codeEditText);
            android.util.Log.d("MainActivity", "TextSearchManager re-initialized with codeEditText");
        } else {
            android.util.Log.w("MainActivity", "Cannot initialize TextSearchManager: textSearchManager=" +
                textSearchManager + ", codeEditText=" + codeEditText);
        }

        // 确保SearchBar监听器正确设置
        if (searchBar != null && textSearchManager != null) {
            setupSearchBarListeners();
            android.util.Log.d("MainActivity", "SearchBar listeners re-initialized");
        } else {
            android.util.Log.w("MainActivity", "Cannot initialize SearchBar listeners: searchBar=" +
                searchBar + ", textSearchManager=" + textSearchManager);
        }

        // 确保codeEditText监听器正确设置
        if (codeEditText != null) {
            codeEditText.setOnSelectionChangedListener((selStart, selEnd) -> {
                updateCursorPosition();
            });
            android.util.Log.d("MainActivity", "CodeEditText selection listener re-initialized");

            // 添加焦点监听器来辅助键盘检测
            codeEditText.setOnFocusChangeListener((v, hasFocus) -> {
                android.util.Log.d("MainActivity", "CodeEditText focus changed: " + hasFocus);
                if (hasFocus) {
                    boolean isLandscape = getResources().getConfiguration().orientation ==
                        android.content.res.Configuration.ORIENTATION_LANDSCAPE;

                    if (isLandscape) {
                        // 横屏时：双击进入编辑模式时，等待键盘显示后自动进入专注模式
                        android.util.Log.d("MainActivity", "Landscape focus: waiting for keyboard to trigger focus mode");
                        // 不需要手动进入专注模式，让键盘监听器自动处理
                        // 这样可以确保专注模式与键盘状态完全同步
                    } else {
                        // 竖屏时：延迟检查键盘状态
                        v.postDelayed(() -> {
                            // 通过InputMethodManager检查键盘状态
                            android.view.inputmethod.InputMethodManager imm =
                                (android.view.inputmethod.InputMethodManager) getSystemService(android.content.Context.INPUT_METHOD_SERVICE);
                            if (imm != null && imm.isActive(v)) {
                                android.util.Log.d("MainActivity", "InputMethodManager indicates keyboard is active");
                                if (!isKeyboardVisible) {
                                    android.util.Log.d("MainActivity", "Focus-based keyboard detection: showing keyboard");
                                    isKeyboardVisible = true;
                                    adjustMainContentMarginForKeyboard(true);
                                    debugLayoutState("Keyboard Shown (Focus)");
                                }
                            }
                        }, 200); // 延迟200ms等待键盘动画
                    }
                } else {
                    // 失去焦点时，横屏让键盘监听器处理专注模式退出
                    boolean isLandscape = getResources().getConfiguration().orientation ==
                        android.content.res.Configuration.ORIENTATION_LANDSCAPE;
                    if (isLandscape) {
                        android.util.Log.d("MainActivity", "Landscape focus lost: letting keyboard listener handle focus mode");
                        // 不需要手动退出专注模式，让键盘隐藏监听器自动处理
                        // 这样可以确保专注模式与键盘状态完全同步
                    }
                }
            });
        }

        // 重新设置键盘监听器
        setupKeyboardDebugListener();

        // 重新设置专注模式管理器
        if (codeEditText != null) {
            // 检查专注模式管理器是否需要重新初始化
            if (codeEditText.getFocusModeManager() == null) {
                android.util.Log.d("MainActivity", "Re-initializing focus mode manager");
                setupImmersiveReading();
            } else {
                android.util.Log.d("MainActivity", "Focus mode manager already exists, skipping re-initialization");
            }

            // 恢复延迟的状态
            restorePendingState();
        }
    }

    /**
     * 设置屏幕方向变化监听器
     */
    private void setupOrientationChangeListener() {
        // 初始设置
        updateFullscreenMode();
    }

    /**
     * 设置系统UI可见性变化监听器
     * 确保横屏时系统Banner不会重新出现
     */
    private void setupSystemUiVisibilityListener() {
        getWindow().getDecorView().setOnSystemUiVisibilityChangeListener(visibility -> {
            boolean isLandscape = getResources().getConfiguration().orientation ==
                android.content.res.Configuration.ORIENTATION_LANDSCAPE;

            android.util.Log.d("MainActivity", "System UI visibility changed: " + visibility +
                ", isLandscape: " + isLandscape);

            if (isLandscape) {
                // 横屏时，如果系统UI变为可见，立即重新隐藏
                if ((visibility & android.view.View.SYSTEM_UI_FLAG_FULLSCREEN) == 0 ||
                    (visibility & android.view.View.SYSTEM_UI_FLAG_HIDE_NAVIGATION) == 0) {

                    android.util.Log.d("MainActivity", "Landscape: System UI became visible, re-hiding");

                    // 延迟一点重新设置，避免与系统冲突
                    getWindow().getDecorView().postDelayed(() -> {
                        boolean stillLandscape = getResources().getConfiguration().orientation ==
                            android.content.res.Configuration.ORIENTATION_LANDSCAPE;
                        if (stillLandscape) {
                            updateFullscreenMode();
                        }
                    }, 500);
                }
            }
        });
    }

    /**
     * 根据屏幕方向更新全屏模式
     */
    private void updateFullscreenMode() {
        boolean isLandscape = getResources().getConfiguration().orientation ==
            android.content.res.Configuration.ORIENTATION_LANDSCAPE;

        android.util.Log.d("MainActivity", "Updating fullscreen mode - isLandscape: " + isLandscape);

        if (isLandscape) {
            // 横屏时：完全隐藏系统UI，包括状态栏和导航栏
            getWindow().getDecorView().setSystemUiVisibility(
                android.view.View.SYSTEM_UI_FLAG_LAYOUT_STABLE |
                android.view.View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION |
                android.view.View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN |
                android.view.View.SYSTEM_UI_FLAG_HIDE_NAVIGATION |
                android.view.View.SYSTEM_UI_FLAG_FULLSCREEN |
                android.view.View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
            );

            // 设置窗口标志，确保完全全屏
            getWindow().addFlags(android.view.WindowManager.LayoutParams.FLAG_FULLSCREEN);
            getWindow().clearFlags(android.view.WindowManager.LayoutParams.FLAG_FORCE_NOT_FULLSCREEN);

            android.util.Log.d("MainActivity", "Entered complete fullscreen mode for landscape");
        } else {
            // 竖屏时：显示状态栏，退出全屏模式
            getWindow().getDecorView().setSystemUiVisibility(
                android.view.View.SYSTEM_UI_FLAG_LAYOUT_STABLE |
                android.view.View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION |
                android.view.View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
            );

            // 清除全屏标志
            getWindow().clearFlags(android.view.WindowManager.LayoutParams.FLAG_FULLSCREEN);
            getWindow().addFlags(android.view.WindowManager.LayoutParams.FLAG_FORCE_NOT_FULLSCREEN);

            android.util.Log.d("MainActivity", "Exited fullscreen mode for portrait");
        }
    }

    @Override
    public void onConfigurationChanged(android.content.res.Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        android.util.Log.d("MainActivity", "Configuration changed - orientation: " + newConfig.orientation);

        // 更新全屏模式
        updateFullscreenMode();

        // 如果从横屏切换到竖屏，且当前在专注模式，需要退出专注模式
        boolean isLandscape = newConfig.orientation == android.content.res.Configuration.ORIENTATION_LANDSCAPE;
        if (!isLandscape && isFocusMode) {
            android.util.Log.d("MainActivity", "Switched to portrait, exiting focus mode");
            exitFocusMode();
        }
    }

    /**
     * 恢复延迟的状态
     */
    private void restorePendingState() {
        if (codeEditText == null) return;

        // 恢复编辑器内容
        if (pendingEditorContent != null) {
            codeEditText.setText(pendingEditorContent);
            codeEditText.setSelection(Math.min(pendingCursorPosition, pendingEditorContent.length()));
            android.util.Log.d("MainActivity", "Editor content restored after codeEditText initialization");
            pendingEditorContent = null;
            pendingCursorPosition = 0;
        }

        // 恢复行号显示
        android.util.Log.d("MainActivity", "Restoring line numbers: " + pendingShowLineNumbers);
        codeEditText.setShowLineNumbers(pendingShowLineNumbers);
        android.util.Log.d("MainActivity", "Line numbers restored: " + pendingShowLineNumbers + ", actual: " + codeEditText.isShowLineNumbers());

        // 恢复专注模式
        android.util.Log.d("MainActivity", "Checking focus mode restoration: isFocusMode=" + isFocusMode);
        if (isFocusMode) {
            android.util.Log.d("MainActivity", "Posting focus mode restoration");
            codeEditText.post(() -> {
                android.util.Log.d("MainActivity", "Executing focus mode restoration");
                enterFocusMode();
                android.util.Log.d("MainActivity", "Focus mode restored after codeEditText initialization");
            });
        } else {
            android.util.Log.d("MainActivity", "No focus mode to restore");
        }
    }

    /**
     * 进入专注模式
     */
    private void enterFocusMode() {
        isFocusMode = true;
        updateStatusBarForFocusMode(true);
        adjustMainContentMarginForFocusMode(true);
        android.util.Log.d("MainActivity", "Entered focus mode");
    }

    /**
     * 退出专注模式
     */
    private void exitFocusMode() {
        isFocusMode = false;
        updateStatusBarForFocusMode(false);
        adjustMainContentMarginForFocusMode(false);

        // 如果键盘仍然显示，需要重新隐藏底部菜单栏
        if (isKeyboardVisible) {
            if (binding.bottomAppBar != null) {
                binding.bottomAppBar.setVisibility(android.view.View.GONE);
            }
        }
        android.util.Log.d("MainActivity", "Exited focus mode");
    }

    /**
     * 设置搜索栏监听器
     */
    private void setupSearchBarListeners() {
        if (searchBar == null || textSearchManager == null) return;

        searchBar.setOnSearchActionListener(new SearchBar.OnSearchActionListener() {
            @Override
            public void onSearch(String searchText, TextSearchManager.SearchOptions options) {
                textSearchManager.search(searchText, options);
            }

            @Override
            public void onFindNext() {
                textSearchManager.findNext();
            }

            @Override
            public void onFindPrevious() {
                textSearchManager.findPrevious();
            }

            @Override
            public void onReplaceCurrent(String replaceText) {
                textSearchManager.replaceCurrent(replaceText);
            }

            @Override
            public void onReplaceAll(String replaceText) {
                int replacedCount = textSearchManager.replaceAll(replaceText);
                com.google.android.material.snackbar.Snackbar.make(
                    binding.getRoot(),
                    getString(R.string.replaced_count, replacedCount),
                    com.google.android.material.snackbar.Snackbar.LENGTH_SHORT
                ).show();
            }

            @Override
            public void clearSearch() {
                textSearchManager.clearSearch();
            }

            @Override
            public String getSelectedText() {
                if (codeEditText == null) return null;

                int start = codeEditText.getSelectionStart();
                int end = codeEditText.getSelectionEnd();
                if (start != end && codeEditText.getText() != null) {
                    return codeEditText.getText().subSequence(start, end).toString();
                }
                return null;
            }
        });
    }

    /**
     * 根据键盘状态调整UI
     */
    private void adjustMainContentMarginForKeyboard(boolean keyboardVisible) {
        if (binding.bottomAppBar == null) return;

        androidx.constraintlayout.widget.ConstraintLayout mainContent =
            findViewById(R.id.mainContentLayout);

        if (mainContent == null) return;

        androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams params =
            (androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams) mainContent.getLayoutParams();

        if (keyboardVisible) {
            // 键盘显示时：隐藏底部菜单栏，移除底部边距
            binding.bottomAppBar.setVisibility(android.view.View.GONE);
            params.bottomMargin = 0;
            android.util.Log.d("MainActivity", "Keyboard visible: hidden bottomAppBar and removed margin");
        } else {
            // 键盘隐藏时：显示底部菜单栏，恢复底部边距
            binding.bottomAppBar.setVisibility(android.view.View.VISIBLE);

            // 检查是否在专注模式
            com.lingtxt.editor.ui.widget.FocusModeManager focusModeManager =
                codeEditText != null ? codeEditText.getFocusModeManager() : null;

            if (focusModeManager != null && focusModeManager.isFocusMode()) {
                // 专注模式：无底部边距（菜单栏会被专注模式隐藏）
                params.bottomMargin = 0;
                android.util.Log.d("MainActivity", "Keyboard hidden + Focus mode: shown bottomAppBar but no margin");
            } else {
                // 正常模式：底部菜单高度
                int bottomAppBarHeight = (int) (56 * getResources().getDisplayMetrics().density);
                params.bottomMargin = bottomAppBarHeight;
                android.util.Log.d("MainActivity", "Keyboard hidden + Normal mode: shown bottomAppBar and restored margin " + bottomAppBarHeight + "px");
            }
        }

        mainContent.setLayoutParams(params);
    }

    /**
     * 配置双模式功能
     */
    private void setupDualModeFeatures() {
        if (codeEditText == null) return;
        
        // 配置退出编辑模式的选项 - 取消自动退出功能
        codeEditText.setAutoExitEditOnFocusLoss(false);      // 失去焦点时不自动退出
        codeEditText.setAutoExitEditOnKeyboardHide(false);   // 键盘隐藏时不自动退出
        
        // 设置模式变化监听器
        codeEditText.setOnModeChangeListener(new CodeEditText.OnModeChangeListener() {
            @Override
            public void onModeChanged(CodeEditText.ViewMode newMode) {
                updateModeIndicator(newMode);
            }

            @Override
            public void onRequestEdit() {
                codeEditText.enterEditMode(true);
            }

            @Override
            public void onRequestView() {
                codeEditText.enterViewMode();
                // 退出编辑模式时自动保存
                saveCurrentFile();
            }
        });
    }

    /**
     * 更新模式指示器
     */
    private void updateModeIndicator(CodeEditText.ViewMode mode) {
        // 在状态栏中显示模式信息
        updateStatusDisplay();
        // 更新底部菜单按钮图标
        updateEditModeButton(mode);
    }

    /**
     * 切换编辑模式
     */
    private void toggleEditMode() {
        if (codeEditText == null) return;

        if (codeEditText.isViewOnlyMode()) {
            // 进入编辑模式
            codeEditText.enterEditMode(true);
        } else {
            // 退出编辑模式并保存文件
            codeEditText.enterViewMode();
            saveCurrentFile();
        }
    }

    /**
     * 更新编辑模式按钮图标
     */
    private void updateEditModeButton(CodeEditText.ViewMode mode) {
        if (binding.bottomAppBar == null) return;

        android.view.Menu menu = binding.bottomAppBar.getMenu();
        android.view.MenuItem editModeItem = menu.findItem(R.id.action_toggle_edit_mode);

        if (editModeItem != null) {
            if (mode == CodeEditText.ViewMode.VIEW_ONLY) {
                // 查看模式：显示编辑图标
                editModeItem.setIcon(R.drawable.ic_edit);
                editModeItem.setTitle(R.string.action_edit_mode);
            } else {
                // 编辑模式：显示阴体对勾图标
                editModeItem.setIcon(R.drawable.ic_check_filled);
                editModeItem.setTitle(R.string.action_exit_edit_mode);
            }
        }
    }

    /**
     * 更新光标位置显示
     */
    private void updateCursorPosition() {
        if (codeEditText == null || tvStatusInfo == null) return;
        
        android.text.Layout layout = codeEditText.getLayout();
        if (layout == null) return;
        
        try {
            int line = codeEditText.getCurrentLine();
            int selectionStart = codeEditText.getSelectionStart();
            
            // 安全地计算列位置
            if (line > 0 && line <= codeEditText.getLineCount() && selectionStart >= 0) {
                int lineStart = layout.getLineStart(line - 1);
                int column = selectionStart - lineStart + 1;
                updateStatusInfo(line, column);
            }
        } catch (Exception e) {
            // 如果出现任何异常，使用默认值
            updateStatusInfo(1, 1);
        }
    }

    // 当前状态信息
    private int currentLine = 1;
    private int currentColumn = 1;
    private String currentEncoding = "UTF-8";
    private long currentFileSize = 0;

    /**
     * 更新状态信息显示
     */
    private void updateStatusInfo(int line, int column) {
        this.currentLine = line;
        this.currentColumn = column;
        updateStatusDisplay();
    }

    /**
     * 更新状态信息显示
     */
    private void updateStatusDisplay() {
        if (tvStatusInfo != null) {
            String sizeText = formatFileSize(currentFileSize);
            String modeText = getModeText();
            String statusText = getString(R.string.status_format,
                currentLine, currentColumn, currentEncoding, sizeText, modeText);
            tvStatusInfo.setText(statusText);
        }
    }

    /**
     * 获取当前模式文本
     */
    private String getModeText() {
        if (codeEditText == null) return "查看";
        
        CodeEditText.ViewMode mode = codeEditText.getCurrentMode();
        switch (mode) {
            case VIEW_ONLY:
                return "📖查看";
            case EDIT_MODE:
                return "✏️编辑";
            default:
                return "查看";
        }
    }

    /**
     * 格式化文件大小
     */
    private String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + "B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.1fKB", bytes / 1024.0);
        } else {
            return String.format("%.1fMB", bytes / (1024.0 * 1024.0));
        }
    }

    /**
     * 更新撤销/重做菜单状态
     */
    private void updateUndoRedoMenuState(boolean canUndo, boolean canRedo) {
        // 这里可以通过Menu来更新按钮状态
        // 由于BottomAppBar的限制，我们在菜单点击时检查状态
    }

    /**
     * 显示跳转到行对话框 - 与搜索栏风格一致
     */
    private void showGotoLineDialog() {
        // 创建与搜索栏风格一致的输入界面
        android.view.View gotoLineView = getLayoutInflater().inflate(R.layout.input_goto_line, null);
        
        android.widget.EditText etLineNumber = gotoLineView.findViewById(R.id.et_line_number);
        android.widget.TextView tvMaxLine = gotoLineView.findViewById(R.id.tv_max_line);
        android.widget.ImageButton btnGoto = gotoLineView.findViewById(R.id.btn_goto);
        android.widget.ImageButton btnClose = gotoLineView.findViewById(R.id.btn_close);
        
        // 显示最大行数
        if (codeEditText != null) {
            int maxLine = codeEditText.getLineCount();
            tvMaxLine.setText(getString(R.string.max_line_count, maxLine));
        }
        
        // 获取底部菜单栏的高度
        int bottomAppBarHeight = binding.bottomAppBar.getHeight();
        if (bottomAppBarHeight == 0) {
            // 如果还没有测量，使用默认高度
            bottomAppBarHeight = (int) (56 * getResources().getDisplayMetrics().density); // 56dp转px
        }
        final int finalBottomAppBarHeight = bottomAppBarHeight;
        
        // 创建临时容器并添加到主布局
        android.widget.FrameLayout container = new android.widget.FrameLayout(this);
        android.widget.FrameLayout.LayoutParams containerParams = new android.widget.FrameLayout.LayoutParams(
            android.widget.FrameLayout.LayoutParams.MATCH_PARENT,
            android.widget.FrameLayout.LayoutParams.WRAP_CONTENT,
            android.view.Gravity.BOTTOM
        );
        // 设置底部边距，让对话框显示在底部菜单之上
        containerParams.bottomMargin = bottomAppBarHeight;
        container.setLayoutParams(containerParams);
        container.setElevation(16f);
        container.setTag("goto_line_container"); // 添加标签用于识别
        container.addView(gotoLineView);
        
        // 添加到根布局
        android.view.ViewGroup rootView = findViewById(android.R.id.content);
        rootView.addView(container);
        
        // 设置键盘监听器
        setupKeyboardListener(container, finalBottomAppBarHeight);
        
        // 设置初始位置（从底部开始）
        container.post(() -> {
            float height = container.getHeight();
            container.setTranslationY(height);
            
            // 平滑弹出动画
            container.animate()
                .translationY(0)
                .setDuration(300)
                .setInterpolator(new android.view.animation.DecelerateInterpolator())
                .start();
        });
        
        // 按钮事件
        btnGoto.setOnClickListener(v -> {
            try {
                String lineText = etLineNumber.getText().toString().trim();
                if (lineText.isEmpty()) {
                    etLineNumber.setError(getString(R.string.please_input_line_number));
                    return;
                }
                
                int lineNumber = Integer.parseInt(lineText);
                if (lineNumber < 1 || lineNumber > codeEditText.getLineCount()) {
                    etLineNumber.setError(getString(R.string.line_number_out_of_range));
                    return;
                }
                
                codeEditText.gotoLine(lineNumber);
                hideGotoLineDialog(container);
                
            } catch (NumberFormatException e) {
                etLineNumber.setError("请输入有效的数字");
            }
        });
        
        btnClose.setOnClickListener(v -> hideGotoLineDialog(container));
        
        // 自动聚焦并显示键盘
        etLineNumber.requestFocus();
        etLineNumber.postDelayed(() -> {
            android.view.inputmethod.InputMethodManager imm = 
                (android.view.inputmethod.InputMethodManager) getSystemService(INPUT_METHOD_SERVICE);
            if (imm != null) {
                imm.showSoftInput(etLineNumber, android.view.inputmethod.InputMethodManager.SHOW_IMPLICIT);
            }
        }, 100);
    }

    /**
     * 隐藏跳转到行对话框
     */
    private void hideGotoLineDialog(android.view.ViewGroup container) {
        // 清理键盘监听器
        Object keyboardListener = container.getTag(R.id.tag_keyboard_listener);
        if (keyboardListener instanceof android.view.ViewTreeObserver.OnGlobalLayoutListener) {
            android.view.ViewGroup rootView = findViewById(android.R.id.content);
            rootView.getViewTreeObserver().removeOnGlobalLayoutListener(
                (android.view.ViewTreeObserver.OnGlobalLayoutListener) keyboardListener);
        }
        
        // 隐藏键盘
        android.view.inputmethod.InputMethodManager imm = 
            (android.view.inputmethod.InputMethodManager) getSystemService(INPUT_METHOD_SERVICE);
        if (imm != null) {
            imm.hideSoftInputFromWindow(container.getWindowToken(), 0);
        }
        
        // 平滑滑出动画
        container.animate()
            .translationY(container.getHeight())
            .setDuration(300)
            .setInterpolator(new android.view.animation.AccelerateInterpolator())
            .withEndAction(() -> {
                android.view.ViewGroup rootView = findViewById(android.R.id.content);
                rootView.removeView(container);
            })
            .start();
    }

    /**
     * 设置底部操作栏
     */
    private void setupBottomAppBar() {
        android.util.Log.d("MainActivity", "=== setupBottomAppBar ===");
        android.util.Log.d("MainActivity", "BottomAppBar: " + binding.bottomAppBar);
        android.util.Log.d("MainActivity", "BottomAppBar menu items: " + binding.bottomAppBar.getMenu().size());

        // 设置底部应用栏的菜单
        binding.bottomAppBar.setOnMenuItemClickListener(item -> {
            int itemId = item.getItemId();
            if (itemId == R.id.action_more) {
                // 显示更多菜单
                showMoreMenu();
                return true;
            } else if (itemId == R.id.action_undo) {
                // 撤销操作
                if (undoRedoManager != null && undoRedoManager.canUndo()) {
                    undoRedoManager.undo();
                }
                return true;
            } else if (itemId == R.id.action_redo) {
                // 重做操作
                if (undoRedoManager != null && undoRedoManager.canRedo()) {
                    undoRedoManager.redo();
                }
                return true;
            } else if (itemId == R.id.action_search) {
                // 关闭其他功能，打开搜索功能
                closeAllActiveFeatures();
                if (searchBar != null) {
                    searchBar.show(false);
                }
                return true;
            } else if (itemId == R.id.action_replace) {
                // 关闭其他功能，打开替换功能
                closeAllActiveFeatures();
                if (searchBar != null) {
                    searchBar.show(true);
                }
                return true;
            } else if (itemId == R.id.action_toggle_edit_mode) {
                // 切换编辑模式
                toggleEditMode();
                return true;

            }
            return false;
        });

        // 初始化编辑模式按钮状态
        if (codeEditText != null) {
            updateEditModeButton(codeEditText.getCurrentMode());
        }

        android.util.Log.d("MainActivity", "BottomAppBar setup completed with " + binding.bottomAppBar.getMenu().size() + " menu items");
    }



    /**
     * 设置空状态
     */
    private void setupEmptyState() {
        binding.btnOpenFile.setOnClickListener(v -> {
            // 打开文件选择器
            openFileChooser();
        });
        
        // 设置文件信息按钮
        binding.btnFileInfo.setOnClickListener(v -> {
            showFileInfoDialog();
        });
        
        // 设置字体大小调整按钮（仅在平板布局中存在）
        try {
            if (binding.btnDecreaseFontSize != null) {
                binding.btnDecreaseFontSize.setOnClickListener(v -> {
                    viewModel.decreaseFontSize();
                });
            }
            if (binding.btnIncreaseFontSize != null) {
                binding.btnIncreaseFontSize.setOnClickListener(v -> {
                    viewModel.increaseFontSize();
                });
            }
            if (binding.btnToggleLineNumbers != null) {
                binding.btnToggleLineNumbers.setOnClickListener(v -> {
                    viewModel.toggleLineNumbers();
                });
            }
        } catch (Exception e) {
            // 这些按钮可能在某些布局中不存在，忽略错误
        }
    }

    /**
     * 设置沉浸式模式
     */
    private void setupImmersiveMode() {
        // 设置状态栏和导航栏透明
        getWindow().setStatusBarColor(android.graphics.Color.TRANSPARENT);
        getWindow().setNavigationBarColor(android.graphics.Color.TRANSPARENT);
        
        // 设置系统UI可见性
        getWindow().getDecorView().setSystemUiVisibility(
            android.view.View.SYSTEM_UI_FLAG_LAYOUT_STABLE |
            android.view.View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION |
            android.view.View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
        );
    }

    /**
     * 显示更多菜单
     */
    private void showMoreMenu() {
        android.widget.PopupMenu popupMenu = new android.widget.PopupMenu(this, binding.bottomAppBar);
        popupMenu.getMenuInflater().inflate(R.menu.more_menu, popupMenu.getMenu());

        popupMenu.setOnMenuItemClickListener(item -> {
            // 手动处理菜单项点击
            return handleMenuItemClick(item);
        });

        popupMenu.show();
    }

    /**
     * 处理菜单项点击（更多菜单专用）
     */
    private boolean handleMenuItemClick(android.view.MenuItem item) {
        int itemId = item.getItemId();

        if (itemId == R.id.action_goto_line) {
            // 跳转到行
            closeAllActiveFeatures();
            showGotoLineDialog();
            return true;
        } else if (itemId == R.id.action_save_as) {
            // 另存为
            performSaveAs();
            return true;
        } else if (itemId == R.id.action_open_file) {
            // 打开文件
            openFileChooser();
            return true;
        } else if (itemId == R.id.action_recent_files) {
            // 最近文件
            openRecentFiles();
            return true;
        } else if (itemId == R.id.action_select_language) {
            // 选择语言
            showLanguageSelectionDialog();
            return true;
        } else if (itemId == R.id.action_settings) {
            // 设置
            openSettings();
            return true;
        } else if (itemId == R.id.action_about) {
            // 关于
            showAboutDialog();
            return true;
        }

        return false;
    }



    /**
     * 打开最近文件列表
     */
    private void openRecentFiles() {
        Intent intent = new Intent(this, com.lingtxt.editor.ui.recent.RecentFilesActivity.class);
        startActivity(intent);
    }


    /**
     * 添加文件到最近文件列表
     */
    private void addToRecentFiles(android.net.Uri uri) {
        if (recentFileRepository == null) return;
        
        // 在后台线程中处理
        new Thread(() -> {
            try {
                // 获取文件信息
                String fileName = getFileNameFromUri(uri);
                String filePath = uri.toString();
                long fileSize = getFileSizeFromUri(uri);
                String encoding = "UTF-8"; // 默认编码，实际应该从文件检测
                String mimeType = getContentResolver().getType(uri);
                
                // 添加到最近文件
                recentFileRepository.addRecentFile(uri, fileName, filePath, fileSize, encoding, mimeType)
                    .subscribe(
                        () -> {
                            // 成功添加
                        },
                        throwable -> {
                            // 添加失败，忽略错误
                        }
                    );
                    
            } catch (Exception e) {
                // 忽略错误
            }
        }).start();
    }

    /**
     * 获取文件大小
     */
    private long getFileSizeFromUri(android.net.Uri uri) {
        try {
            android.database.Cursor cursor = getContentResolver().query(uri, null, null, null, null);
            if (cursor != null) {
                int sizeIndex = cursor.getColumnIndex(android.provider.OpenableColumns.SIZE);
                if (sizeIndex != -1 && cursor.moveToFirst()) {
                    long size = cursor.getLong(sizeIndex);
                    cursor.close();
                    return size;
                }
                cursor.close();
            }
        } catch (Exception e) {
            // 忽略错误
        }
        return 0;
    }

    /**
     * 打开文件选择器
     */
    private void openFileChooser() {
        Intent intent = new Intent(Intent.ACTION_OPEN_DOCUMENT);
        intent.addCategory(Intent.CATEGORY_OPENABLE);

        // 临时测试：允许所有文件类型
        intent.setType("*/*");

        // 也添加我们支持的 MIME 类型作为提示
        String[] mimeTypes = com.lingtxt.editor.config.SupportedFileTypes.getSupportedMimeTypes();
        intent.putExtra(Intent.EXTRA_MIME_TYPES, mimeTypes);

        try {
            startActivityForResult(intent, REQUEST_CODE_OPEN_FILE);
        } catch (android.content.ActivityNotFoundException e) {
            com.google.android.material.snackbar.Snackbar.make(
                binding.getRoot(),
                getString(R.string.file_manager_not_found),
                com.google.android.material.snackbar.Snackbar.LENGTH_LONG
            ).show();
        }
    }

    private static final int REQUEST_CODE_OPEN_FILE = 1001;
    private static final int REQUEST_CODE_SETTINGS = 1002;

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        // 处理文件保存结果
        FileSaveHelper.handleSaveResult(requestCode, resultCode, data, this);

        if (requestCode == REQUEST_CODE_OPEN_FILE && resultCode == RESULT_OK) {
            if (data != null && data.getData() != null) {
                Uri uri = data.getData();

                // 保存当前文件信息
                currentFileUri = uri;
                currentFileName = getFileNameFromUri(uri);

                // 获取持久化权限
                try {
                    getContentResolver().takePersistableUriPermission(
                        uri,
                        Intent.FLAG_GRANT_READ_URI_PERMISSION | Intent.FLAG_GRANT_WRITE_URI_PERMISSION
                    );
                } catch (SecurityException e) {
                    // 权限获取失败，但仍可以尝试读取文件
                }

                // 使用ViewModel加载文件
                viewModel.loadFile(uri);

                // 添加到最近文件列表
                addToRecentFiles(uri);
            }
        } else if (requestCode == REQUEST_CODE_SETTINGS && resultCode == RESULT_OK) {
            // 从设置页面返回，重新加载设置（主题和语言已在设置页面立即应用）
            loadAndApplySettings();
        }
    }

    /**
     * 关闭所有活动的功能界面
     */
    private void closeAllActiveFeatures() {
        // 关闭搜索栏
        if (searchBar != null && searchBar.isSearchBarVisible()) {
            searchBar.hide();
        }
        
        // 关闭跳转到行对话框（如果存在）
        hideAnyGotoLineDialog();
    }

    /**
     * 隐藏任何存在的跳转到行对话框
     */
    private void hideAnyGotoLineDialog() {
        android.view.ViewGroup rootView = findViewById(android.R.id.content);
        // 查找并移除所有跳转到行的容器
        for (int i = rootView.getChildCount() - 1; i >= 0; i--) {
            android.view.View child = rootView.getChildAt(i);
            if (child instanceof android.widget.FrameLayout && 
                child.getTag() != null && 
                "goto_line_container".equals(child.getTag())) {
                hideGotoLineDialog((android.view.ViewGroup) child);
                break;
            }
        }
    }

    /**
     * 设置键盘监听器，动态调整对话框位置
     */
    private void setupKeyboardListener(android.widget.FrameLayout container, int bottomAppBarHeight) {
        android.view.ViewGroup rootView = findViewById(android.R.id.content);
        
        // 创建键盘监听器
        android.view.ViewTreeObserver.OnGlobalLayoutListener keyboardListener = new android.view.ViewTreeObserver.OnGlobalLayoutListener() {
            private int previousHeight = 0;
            private boolean isKeyboardVisible = false;
            
            @Override
            public void onGlobalLayout() {
                // 检查容器是否还存在，如果不存在则移除监听器
                if (container.getParent() == null) {
                    rootView.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                    return;
                }
                
                android.graphics.Rect rect = new android.graphics.Rect();
                rootView.getWindowVisibleDisplayFrame(rect);
                
                int currentHeight = rect.height();
                
                // 初始化previousHeight
                if (previousHeight == 0) {
                    previousHeight = currentHeight;
                    return;
                }
                
                // 计算高度差
                int heightDiff = previousHeight - currentHeight;
                
                // 键盘显示的阈值（通常大于150dp）
                int keyboardThreshold = (int) (150 * getResources().getDisplayMetrics().density);
                
                if (heightDiff > keyboardThreshold && !isKeyboardVisible) {
                    // 键盘显示
                    isKeyboardVisible = true;
                    adjustDialogForKeyboard(container, heightDiff, true);
                } else if (heightDiff < -keyboardThreshold && isKeyboardVisible) {
                    // 键盘隐藏
                    isKeyboardVisible = false;
                    adjustDialogForKeyboard(container, bottomAppBarHeight, false);
                }
                
                previousHeight = currentHeight;
            }
        };
        
        // 将监听器保存到容器的tag中，便于后续移除
        container.setTag(R.id.tag_keyboard_listener, keyboardListener);
        rootView.getViewTreeObserver().addOnGlobalLayoutListener(keyboardListener);
    }

    /**
     * 根据键盘状态调整对话框位置
     */
    private void adjustDialogForKeyboard(android.widget.FrameLayout container, int offset, boolean keyboardVisible) {
        android.widget.FrameLayout.LayoutParams params = 
            (android.widget.FrameLayout.LayoutParams) container.getLayoutParams();
        
        if (keyboardVisible) {
            // 键盘显示时，将对话框移到键盘上方
            params.bottomMargin = offset + (int) (16 * getResources().getDisplayMetrics().density); // 额外16dp间距
        } else {
            // 键盘隐藏时，恢复到底部菜单上方
            params.bottomMargin = offset;
        }
        
        container.setLayoutParams(params);
        
        // 添加平滑动画
        container.animate()
            .setDuration(200)
            .start();
    }


    /**
     * 显示语言选择对话框
     */
    private void showLanguageSelectionDialog() {
        if (codeEditText == null) return;
        
        com.lingtxt.editor.syntax.LanguageDetector.Language[] languages = 
            codeEditText.getSupportedLanguages();
        
        String[] languageNames = new String[languages.length];
        for (int i = 0; i < languages.length; i++) {
            languageNames[i] = languages[i].getDisplayName();
        }
        
        com.lingtxt.editor.syntax.LanguageDetector.Language currentLanguage = 
            codeEditText.getSyntaxLanguage();
        int selectedIndex = 0;
        for (int i = 0; i < languages.length; i++) {
            if (languages[i] == currentLanguage) {
                selectedIndex = i;
                break;
            }
        }
        
        androidx.appcompat.app.AlertDialog.Builder builder = new androidx.appcompat.app.AlertDialog.Builder(this);
        builder.setTitle(getString(R.string.select_syntax_language))
               .setSingleChoiceItems(languageNames, selectedIndex, (dialog, which) -> {
                   codeEditText.setSyntaxLanguage(languages[which]);
                   dialog.dismiss();
                   
                   // 显示选择结果
                   String message = getString(R.string.language_switched_to, languageNames[which]);
                   android.widget.Toast.makeText(this, message, android.widget.Toast.LENGTH_SHORT).show();
               })
               .setNegativeButton(getString(android.R.string.cancel), null)
               .show();
    }

    @Override
    public void onBackPressed() {
        // 检查是否有活动的功能界面需要关闭
        if (searchBar != null && searchBar.isSearchBarVisible()) {
            searchBar.hide();
            return;
        }

        // 检查是否有跳转到行对话框
        android.view.ViewGroup rootView = findViewById(android.R.id.content);
        for (int i = rootView.getChildCount() - 1; i >= 0; i--) {
            android.view.View child = rootView.getChildAt(i);
            if (child instanceof android.widget.FrameLayout &&
                child.getTag() != null &&
                "goto_line_container".equals(child.getTag())) {
                hideGotoLineDialog((android.view.ViewGroup) child);
                return;
            }
        }

        // 正常返回
        super.onBackPressed();
    }



    @Override
    public void onUserInteraction() {
        super.onUserInteraction();
        
        // 通知专注模式管理器用户有交互
        if (codeEditText != null) {
            com.lingtxt.editor.ui.widget.FocusModeManager focusModeManager = codeEditText.getFocusModeManager();
            if (focusModeManager != null) {
                focusModeManager.onUserInteraction();
            }
        }
    }

    @Override
    protected void onSaveInstanceState(android.os.Bundle outState) {
        super.onSaveInstanceState(outState);

        // 保存当前文件信息
        if (currentFile != null) {
            outState.putString("currentFilePath", currentFile.getAbsolutePath());
        }
        if (currentFileUri != null) {
            outState.putString("currentFileUri", currentFileUri.toString());
        }
        if (currentFileName != null) {
            outState.putString("currentFileName", currentFileName);
        }

        // 保存编辑器内容
        if (codeEditText != null && codeEditText.getText() != null) {
            outState.putString("editorContent", codeEditText.getText().toString());
            outState.putInt("cursorPosition", codeEditText.getSelectionStart());
        }

        // 保存专注模式状态
        outState.putBoolean("isFocusMode", isFocusMode);

        // 保存键盘状态
        outState.putBoolean("isKeyboardVisible", isKeyboardVisible);

        // 保存行号显示状态
        if (codeEditText != null) {
            outState.putBoolean("showLineNumbers", codeEditText.isShowLineNumbers());
        } else {
            // 如果codeEditText为null，保存默认值或当前的pendingShowLineNumbers
            outState.putBoolean("showLineNumbers", pendingShowLineNumbers);
        }

        // 保存崩溃恢复对话框状态，避免重复显示
        outState.putBoolean("crashRecoveryChecked", true);

        Log.d("MainActivity", "State saved - file: " + (currentFile != null ? currentFile.getName() : "null") +
            ", focusMode: " + isFocusMode + ", keyboard: " + isKeyboardVisible);
    }

    @Override
    protected void onRestoreInstanceState(android.os.Bundle savedInstanceState) {
        super.onRestoreInstanceState(savedInstanceState);

        // 恢复当前文件
        String filePath = savedInstanceState.getString("currentFilePath");
        if (filePath != null) {
            currentFile = new java.io.File(filePath);
        }

        String fileUriString = savedInstanceState.getString("currentFileUri");
        if (fileUriString != null) {
            currentFileUri = android.net.Uri.parse(fileUriString);
        }

        currentFileName = savedInstanceState.getString("currentFileName");

        // 恢复编辑器内容
        String content = savedInstanceState.getString("editorContent");
        int cursorPosition = savedInstanceState.getInt("cursorPosition", 0);
        if (content != null && codeEditText != null) {
            codeEditText.setText(content);
            codeEditText.setSelection(Math.min(cursorPosition, content.length()));
        } else if (content != null) {
            // 保存待恢复的内容
            pendingEditorContent = content;
            pendingCursorPosition = cursorPosition;
            android.util.Log.w("MainActivity", "Cannot restore editor content immediately, codeEditText is null. Will restore later.");
        }

        // 恢复专注模式状态
        boolean wasFocusMode = savedInstanceState.getBoolean("isFocusMode", false);
        if (wasFocusMode && codeEditText != null) {
            // 延迟恢复专注模式，确保UI完全初始化
            codeEditText.post(() -> {
                enterFocusMode();
            });
        } else if (wasFocusMode) {
            // 如果codeEditText为null，标记需要稍后恢复专注模式
            isFocusMode = true;
            android.util.Log.w("MainActivity", "Cannot restore focus mode immediately, codeEditText is null. Will restore later.");
        }

        // 恢复键盘状态
        isKeyboardVisible = savedInstanceState.getBoolean("isKeyboardVisible", false);

        // 恢复行号显示状态
        boolean showLineNumbers = savedInstanceState.getBoolean("showLineNumbers", true);
        if (codeEditText != null) {
            codeEditText.setShowLineNumbers(showLineNumbers);
        } else {
            // 保存待恢复的行号状态
            pendingShowLineNumbers = showLineNumbers;
            android.util.Log.w("MainActivity", "Cannot restore line numbers immediately, codeEditText is null. Will restore later.");
        }

        // 恢复崩溃恢复检查状态
        crashRecoveryChecked = savedInstanceState.getBoolean("crashRecoveryChecked", false);

        android.util.Log.d("MainActivity", "State restored - file: " + (currentFile != null ? currentFile.getName() : "null") +
            ", focusMode: " + wasFocusMode + ", keyboard: " + isKeyboardVisible + ", lineNumbers: " + showLineNumbers +
            ", crashRecoveryChecked: " + crashRecoveryChecked);
    }

    // 添加成员变量来跟踪崩溃恢复检查状态
    private boolean crashRecoveryChecked = false;
    private AlertDialog crashRecoveryDialog;

    // 延迟恢复的状态
    private String pendingEditorContent = null;
    private int pendingCursorPosition = 0;
    private boolean pendingShowLineNumbers = true;

    @Override
    protected void onDestroy() {
        super.onDestroy();

        // 清理对话框
        if (fileAccessDialog != null && fileAccessDialog.isShowing()) {
            fileAccessDialog.dismiss();
            fileAccessDialog = null;
        }

        // 清理崩溃恢复对话框
        if (crashRecoveryDialog != null && crashRecoveryDialog.isShowing()) {
            crashRecoveryDialog.dismiss();
            crashRecoveryDialog = null;
        }

        // 重置崩溃恢复检查状态，确保下次启动时能正常检查
        crashRecoveryChecked = false;

        // 注销设置变更监听器
        SettingsChangeManager.getInstance().unregisterListener(this);



        // 清理专注模式管理器资源
        if (codeEditText != null) {
            com.lingtxt.editor.ui.widget.FocusModeManager focusModeManager = codeEditText.getFocusModeManager();
            if (focusModeManager != null) {
                focusModeManager.destroy();
            }
        }

        // 清理搜索管理器资源
        if (textSearchManager != null) {
            textSearchManager.cleanup();
        }
    }

    /**
     * 加载和应用设置
     */
    @SuppressLint("CheckResult")
    private void loadAndApplySettings() {
        if (settingsRepository == null) {
            return;
        }

        // 加载语言设置
        settingsRepository.getLanguage()
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(
                language -> {
                    android.util.Log.d("MainActivity", "Loading language setting: " + language);
                    applyLanguage(language);

                    // 强制刷新界面以确保语言生效
                    if (binding != null) {
                        invalidateOptionsMenu();
                        // 强制重新设置标题
                        setTitle(getString(R.string.app_name));
                    }
                },
                throwable -> android.util.Log.e("MainActivity", "Failed to load language setting", throwable)
            );

        // 加载主题设置
        settingsRepository.getTheme()
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(
                theme -> applyTheme(theme),
                throwable -> android.util.Log.e("MainActivity", "Failed to load theme setting", throwable)
            );

        // 加载字体大小设置
        settingsRepository.getFontSize()
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(
                fontSize -> {
                    if (codeEditText != null) {
                        codeEditText.setFontSize(fontSize);
                    }
                },
                throwable -> android.util.Log.e("MainActivity", "Failed to load font size", throwable)
            );

        // 加载字体族设置
        settingsRepository.getFontFamily()
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(
                fontFamily -> {
                    if (codeEditText != null) {
                        codeEditText.setFontFamily(fontFamily);
                    }
                },
                throwable -> android.util.Log.e("MainActivity", "Failed to load font family", throwable)
            );

        // 加载行号显示设置
        settingsRepository.getShowLineNumbers()
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(
                showLineNumbers -> {
                    if (codeEditText != null) {
                        codeEditText.setShowLineNumbers(showLineNumbers);
                    }
                },
                throwable -> android.util.Log.e("MainActivity", "Failed to load line numbers setting", throwable)
            );

        // 加载不可见字符显示设置
        settingsRepository.getShowInvisibleChars()
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(
                showInvisibleChars -> {
                    if (codeEditText != null) {
                        codeEditText.setShowInvisibleChars(showInvisibleChars);
                    }
                },
                throwable -> android.util.Log.e("MainActivity", "Failed to load invisible chars setting", throwable)
            );
    }

    /**
     * 应用主题设置
     */
    private void applyTheme(Theme theme) {
        int nightMode;
        switch (theme) {
            case LIGHT:
                nightMode = androidx.appcompat.app.AppCompatDelegate.MODE_NIGHT_NO;
                break;
            case DARK:
                nightMode = androidx.appcompat.app.AppCompatDelegate.MODE_NIGHT_YES;
                break;
            case SYSTEM:
            default:
                nightMode = androidx.appcompat.app.AppCompatDelegate.MODE_NIGHT_FOLLOW_SYSTEM;
                break;
        }
        androidx.appcompat.app.AppCompatDelegate.setDefaultNightMode(nightMode);
    }

    /**
     * 应用语言设置
     */
    private void applyLanguage(AppLanguage language) {
        java.util.Locale locale;
        switch (language) {
            case CHINESE:
                locale = java.util.Locale.SIMPLIFIED_CHINESE;
                break;
            case ENGLISH:
                locale = java.util.Locale.ENGLISH;
                break;
            case SYSTEM:
            default:
                locale = java.util.Locale.getDefault();
                break;
        }

        android.util.Log.d("MainActivity", "Applying language: " + language + ", locale: " + locale);

        // 更新应用语言配置
        android.content.res.Configuration config = new android.content.res.Configuration();
        config.setLocale(locale);

        // 获取应用上下文并更新配置
        android.content.Context context = getApplicationContext();
        context.getResources().updateConfiguration(config, context.getResources().getDisplayMetrics());

        // 也更新当前Activity的配置
        getResources().updateConfiguration(config, getResources().getDisplayMetrics());

        android.util.Log.d("MainActivity", "Language applied successfully");
    }

    /**
     * 检查并应用语言设置
     */
    private void checkAndApplyLanguageSettings() {
        if (settingsRepository == null) {
            return;
        }

        settingsRepository.getLanguage()
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(
                language -> {
                    // 检查当前语言配置是否与设置一致
                    java.util.Locale currentLocale = getResources().getConfiguration().locale;
                    java.util.Locale expectedLocale;

                    switch (language) {
                        case CHINESE:
                            expectedLocale = java.util.Locale.SIMPLIFIED_CHINESE;
                            break;
                        case ENGLISH:
                            expectedLocale = java.util.Locale.ENGLISH;
                            break;
                        case SYSTEM:
                        default:
                            expectedLocale = java.util.Locale.getDefault();
                            break;
                    }

                    android.util.Log.d("MainActivity", "Current locale: " + currentLocale + ", Expected: " + expectedLocale);

                    // 如果语言不一致，重新应用语言设置
                    if (!currentLocale.getLanguage().equals(expectedLocale.getLanguage())) {
                        android.util.Log.d("MainActivity", "Language mismatch detected, reapplying language settings");
                        SettingsChangeManager.getInstance().applyLanguageChange(this, language);
                    }
                },
                throwable -> android.util.Log.e("MainActivity", "Failed to check language setting", throwable)
            );
    }

    /**
     * 打开设置界面
     */
    private void openSettings() {
        Intent intent = SettingsActivity.createIntent(this);
        startActivityForResult(intent, REQUEST_CODE_SETTINGS);
    }

    // SettingsChangeManager.SettingsChangeListener 实现
    @Override
    public void onThemeChanged(Theme theme) {
        // 主题变更已在SettingsChangeManager中全局应用，这里更新编辑器主题
        android.util.Log.d("MainActivity", "Theme changed to: " + theme);

        // 更新编辑器主题颜色
        if (codeEditText != null) {
            codeEditText.updateThemeColors();
        }
    }

    @Override
    public void onLanguageChanged(AppLanguage language) {
        // 语言变更已在SettingsChangeManager中应用，这里刷新界面文本
        android.util.Log.d("MainActivity", "Language changed to: " + language);

        // 延迟重新创建Activity，避免与主题变更冲突
        android.os.Handler mainHandler = new android.os.Handler(android.os.Looper.getMainLooper());
        mainHandler.postDelayed(() -> {
            if (!isFinishing() && !isDestroyed()) {
                recreate();
            }
        }, 200); // 延迟200ms，确保主题变更完成
    }

    @Override
    public void onFontSizeChanged(float fontSize) {
        android.util.Log.d("MainActivity", "Font size changed to: " + fontSize);
        if (codeEditText != null) {
            codeEditText.setFontSize(fontSize);
        }
    }

    @Override
    public void onFontFamilyChanged(String fontFamily) {
        android.util.Log.d("MainActivity", "Font family changed to: " + fontFamily);
        if (codeEditText != null) {
            codeEditText.setFontFamily(fontFamily);
        }
    }

    @Override
    public void onOtherSettingChanged(String key, Object value) {
        android.util.Log.d("MainActivity", "Other setting changed: " + key + " = " + value);

        switch (key) {
            case "show_line_numbers":
                if (codeEditText != null && value instanceof Boolean) {
                    codeEditText.setShowLineNumbers((Boolean) value);
                }
                break;
            case "show_invisible_chars":
                if (codeEditText != null && value instanceof Boolean) {
                    codeEditText.setShowInvisibleChars((Boolean) value);
                }
                break;
        }
    }

    // ==================== 大文件策略处理方法 ====================

    /**
     * 处理文件策略
     */
    private void handleFileStrategy(LargeFileStrategy.FileInfo fileInfo) {
        Log.d("MainActivity", "File strategy: " + fileInfo.getStrategy() +
            ", size: " + fileInfo.getFileSizeFormatted() +
            ", reason: " + fileInfo.getReason());

        // 根据策略调整UI
        switch (fileInfo.getStrategy()) {
            case VIRTUALIZED_LOAD:
            case STREAMING_LOAD:
                // 大文件模式：显示性能信息
                showPerformanceInfo(fileInfo);
                break;
            case PREVIEW_LOAD:
                // 预览模式：显示预览提示
                showPreviewInfo(fileInfo);
                break;
            case REJECT_LOAD:
                // 拒绝加载：已在错误处理中显示
                break;
            default:
                // 普通模式：无需特殊处理
                break;
        }
    }

    /**
     * 显示文件警告
     */
    private void showFileWarning(String warning) {
        Snackbar snackbar =
            Snackbar.make(
                binding.getRoot(),
                warning,
                com.google.android.material.snackbar.Snackbar.LENGTH_LONG
            );

        // 设置警告样式
        snackbar.setBackgroundTint(androidx.core.content.ContextCompat.getColor(this, android.R.color.holo_red_dark));
        snackbar.setTextColor(getColor(android.R.color.white));
        snackbar.show();
    }

    /**
     * 显示性能信息
     */
    private void showPerformanceInfo(LargeFileStrategy.FileInfo fileInfo) {
        if (codeEditText != null && codeEditText.isLargeFileMode()) {
            String memoryInfo = codeEditText.getMemoryInfo();
            String performanceInfo = String.format(
                "🚀 性能优化已启用\n文件大小: %s\n内存优化: %s\n优化策略: %s\n✨ 享受流畅的编辑体验",
                fileInfo.getFileSizeFormatted(),
                memoryInfo,
                getStrategyDisplayName(fileInfo.getStrategy())
            );

            Snackbar.make(
                binding.getRoot(),
                performanceInfo,
                Snackbar.LENGTH_LONG
            ).show();
        }
    }

    /**
     * 获取策略显示名称
     */
    private String getStrategyDisplayName(LargeFileStrategy.FileStrategy strategy) {
        switch (strategy) {
            case VIRTUALIZED_LOAD:
                return "虚拟化渲染";
            case STREAMING_LOAD:
                return "流式加载";
            case PREVIEW_LOAD:
                return "快速预览";
            default:
                return "标准模式";
        }
    }

    /**
     * 显示预览信息
     */
    private void showPreviewInfo(LargeFileStrategy.FileInfo fileInfo) {
        String previewInfo = String.format(
            "预览模式：为提升加载速度，显示文件前部分内容\n文件大小: %s\n所有编辑功能正常可用",
            fileInfo.getFileSizeFormatted()
        );

        Snackbar.make(
            binding.getRoot(),
            previewInfo,
            Snackbar.LENGTH_LONG
        ).show();
    }

    // ==================== 崩溃恢复和权限检查 ====================

    /**
     * 检查崩溃恢复
     */
    private void checkCrashRecovery() {
        // 如果已经检查过崩溃恢复，避免重复检查
        if (crashRecoveryChecked) {
            android.util.Log.d("MainActivity", "Crash recovery already checked, skipping");
            return;
        }

        crashRecoveryChecked = true;

        if (GlobalExceptionHandler.hasPendingCrash(this)) {
            String crashLog = GlobalExceptionHandler.getLastCrashLog(this);

            // 确保之前的对话框已关闭
            if (crashRecoveryDialog != null && crashRecoveryDialog.isShowing()) {
                crashRecoveryDialog.dismiss();
            }

            // 检查Activity是否正在销毁或已销毁
            if (isFinishing() || isDestroyed()) {
                android.util.Log.w("MainActivity", "Activity is finishing/destroyed, skipping crash recovery dialog");
                return;
            }

            crashRecoveryDialog = UserFeedbackManager.getInstance().showCrashRecoveryDialog(
                this,
                crashLog,
                () -> {
                    // 发送崩溃报告
                    sendCrashReport(crashLog);
                    GlobalExceptionHandler.clearCrashState(this);
                    crashRecoveryDialog = null;
                },
                () -> {
                    // 忽略崩溃
                    GlobalExceptionHandler.clearCrashState(this);
                    crashRecoveryDialog = null;
                }
            );
        }
    }

    /**
     * 检查权限（文本编辑器需要写入权限）
     */
    private void checkPermissions() {
        // 检查权限状态
        boolean hasBasic = PermissionManager.hasBasicStoragePermission(this);
        boolean hasAll = PermissionManager.hasStoragePermission(this);

        android.util.Log.d("MainActivity", "Permission check - hasBasic: " + hasBasic + ", hasAll: " + hasAll);

        if (hasAll) {
            // 有完整权限，可以正常使用
            android.util.Log.d("MainActivity", "Full permissions available");
        } else if (hasBasic) {
            // 只有基础权限，提示需要写入权限
            android.util.Log.d("MainActivity", "Only basic permissions, need write access");
            showWritePermissionDialog();
        } else {
            // 没有权限，显示文件访问方式选择
            android.util.Log.d("MainActivity", "No permissions, showing dialog");
            showFileAccessMethodDialog();
        }
    }

    /**
     * 显示写入权限提示对话框
     */
    private void showWritePermissionDialog() {
        UserFeedbackManager.getInstance().showConfirmDialog(
            this,
            "需要文件写入权限",
            "当前只有文件读取权限，无法保存修改。\n\n是否授权完整文件访问权限以支持文件保存？",
            () -> FileAccessHelper.requestAdvancedFileAccess(this)
        );
    }

    /**
     * 保存当前文件
     */
    private void saveCurrentFile() {
        if (codeEditText == null) {
            return;
        }

        String content = codeEditText.getText().toString();
        String fileName = currentFileName != null ? currentFileName : "untitled.txt";

        FileSaveHelper.saveFile(this, currentFileUri, content, fileName, new FileSaveHelper.SaveCallback() {
            @Override
            public void onSaveSuccess() {
                UserFeedbackManager.getInstance().showToast(MainActivity.this, "文件保存成功");
            }

            @Override
            public void onSaveError(String error) {
                if (error.contains("另存为")) {
                    // 显示另存为选项
                    showSaveAsDialog(content, fileName);
                } else {
                    UserFeedbackManager.getInstance().showErrorDialog(
                        MainActivity.this,
                        "保存失败",
                        error
                    );
                }
            }

            @Override
            public void onPermissionRequired() {
                showWritePermissionDialog();
            }
        });
    }

    /**
     * 显示另存为对话框
     */
    private void showSaveAsDialog(String content, String fileName) {
        UserFeedbackManager.getInstance().showConfirmDialog(
            this,
            "文件只读",
            "原文件来自其他应用（如微信），无法直接修改。\n\n是否另存为新文件到手机存储？",
            () -> {
                FileSaveHelper.saveAsFile(this, content, fileName, new FileSaveHelper.SaveCallback() {
                    @Override
                    public void onSaveSuccess() {
                        UserFeedbackManager.getInstance().showToast(MainActivity.this, "文件已另存为新文件");
                    }

                    @Override
                    public void onSaveError(String error) {
                        UserFeedbackManager.getInstance().showErrorDialog(
                            MainActivity.this,
                            "另存为失败",
                            error
                        );
                    }

                    @Override
                    public void onPermissionRequired() {
                        showWritePermissionDialog();
                    }
                });
            }
        );
    }

    /**
     * 执行另存为操作
     */
    private void performSaveAs() {
        if (codeEditText == null) {
            return;
        }

        String content = codeEditText.getText().toString();
        String fileName = currentFileName != null ? currentFileName : "untitled.txt";

        FileSaveHelper.saveAsFile(this, content, fileName, new FileSaveHelper.SaveCallback() {
            @Override
            public void onSaveSuccess() {
                UserFeedbackManager.getInstance().showToast(MainActivity.this, "文件已另存为新文件");
            }

            @Override
            public void onSaveError(String error) {
                UserFeedbackManager.getInstance().showErrorDialog(
                    MainActivity.this,
                    "另存为失败",
                    error
                );
            }

            @Override
            public void onPermissionRequired() {
                showWritePermissionDialog();
            }
        });
    }

    /**
     * 显示文件信息对话框
     */
    private void showFileInfoDialog() {
        if (currentFileUri == null) {
            UserFeedbackManager.getInstance().showToast(this, "没有打开的文件");
            return;
        }

        android.view.View dialogView = getLayoutInflater().inflate(R.layout.dialog_file_info, null);

        androidx.appcompat.app.AlertDialog dialog = new com.google.android.material.dialog.MaterialAlertDialogBuilder(this)
            .setView(dialogView)
            .setCancelable(true)
            .create();

        // 在后台线程获取文件信息
        new Thread(() -> {
            FileInfoHelper.FileInfo fileInfo = FileInfoHelper.getFileInfo(this, currentFileUri);

            // 在主线程更新UI
            runOnUiThread(() -> {
                ((TextView) dialogView.findViewById(R.id.tv_file_name)).setText(fileInfo.fileName);
                ((TextView) dialogView.findViewById(R.id.tv_file_path)).setText(fileInfo.filePath);
                ((TextView) dialogView.findViewById(R.id.tv_file_size)).setText(fileInfo.fileSize);
                ((TextView) dialogView.findViewById(R.id.tv_create_time)).setText(fileInfo.createTime);
                ((TextView) dialogView.findViewById(R.id.tv_modify_time)).setText(fileInfo.modifyTime);
                ((TextView) dialogView.findViewById(R.id.tv_permissions)).setText(fileInfo.permissions);
                ((TextView) dialogView.findViewById(R.id.tv_encoding)).setText(fileInfo.encoding);
                ((TextView) dialogView.findViewById(R.id.tv_mime_type)).setText(fileInfo.mimeType);
            });
        }).start();

        dialogView.findViewById(R.id.btn_close).setOnClickListener(v -> dialog.dismiss());

        dialog.show();
    }

    /**
     * 显示关于对话框
     */
    private void showAboutDialog() {
        String appName = getString(R.string.app_name);
        String version = "1.0.0"; // 可以从BuildConfig获取
        String aboutText = String.format(
            "%s\n\n版本：%s\n\n一个功能强大的文本编辑器，支持语法高亮、多种编码格式、文件管理等功能。\n\n© 2024 LingTxt",
            appName, version
        );

        new com.google.android.material.dialog.MaterialAlertDialogBuilder(this)
            .setTitle("关于")
            .setMessage(aboutText)
            .setPositiveButton("确定", null)
            .show();
    }

    /**
     * 显示文件访问方式选择对话框
     */
    private void showFileAccessMethodDialog() {
        // 如果对话框已经显示，不重复显示
        if (fileAccessDialog != null && fileAccessDialog.isShowing()) {
            return;
        }

        android.view.View dialogView = getLayoutInflater().inflate(R.layout.dialog_file_access_method, null);

        fileAccessDialog = new com.google.android.material.dialog.MaterialAlertDialogBuilder(this)
            .setView(dialogView)
            .setCancelable(true)
            .create();

        // 设置点击事件
        dialogView.findViewById(R.id.card_file_picker).setOnClickListener(v -> {
            UserFeedbackManager.getInstance().showToast(this, "已选择文件选择器模式");
            fileAccessDialog.dismiss();
            fileAccessDialog = null;
        });

        dialogView.findViewById(R.id.card_media_permission).setOnClickListener(v -> {
            requestBasicPermissions();
            // 不立即关闭对话框，等权限授权后在onResume中关闭
        });

        dialogView.findViewById(R.id.card_all_files_permission).setOnClickListener(v -> {
            FileAccessHelper.requestAdvancedFileAccess(this);
            // 不立即关闭对话框，等权限授权后在onResume中关闭
        });

        dialogView.findViewById(R.id.btn_cancel).setOnClickListener(v -> {
            fileAccessDialog.dismiss();
            fileAccessDialog = null;
        });

        fileAccessDialog.show();
    }

    /**
     * 创建加载进度对话框
     */
    private androidx.appcompat.app.AlertDialog createLoadingDialog() {
        android.view.View dialogView = getLayoutInflater().inflate(android.R.layout.simple_list_item_1, null);
        android.widget.TextView textView = dialogView.findViewById(android.R.id.text1);
        textView.setText("正在加载文件... 0%");
        textView.setGravity(android.view.Gravity.CENTER);
        textView.setPadding(50, 50, 50, 50);

        return new com.google.android.material.dialog.MaterialAlertDialogBuilder(this)
            .setTitle("加载文件")
            .setView(dialogView)
            .setCancelable(false)
            .create();
    }

    /**
     * 更新加载进度
     */
    private void updateLoadingProgress(androidx.appcompat.app.AlertDialog dialog, int progress) {
        android.widget.TextView textView = dialog.findViewById(android.R.id.text1);
        if (textView != null) {
            textView.setText("正在加载文件... " + progress + "%");
        }
    }

    /**
     * 加载文件预览
     */
    private void loadFilePreview(Uri uri) {
        androidx.appcompat.app.AlertDialog progressDialog = createLoadingDialog();
        progressDialog.show();

        SafeFileLoader.getFilePreview(this, uri, new SafeFileLoader.LoadCallback() {
            @Override
            public void onLoadStart() {
                runOnUiThread(() -> {
                    UserFeedbackManager.getInstance().showToast(MainActivity.this, "正在加载预览...");
                });
            }

            @Override
            public void onProgress(int progress) {
                runOnUiThread(() -> {
                    updateLoadingProgress(progressDialog, progress);
                });
            }

            @Override
            public void onLoadSuccess(String content, String encoding) {
                runOnUiThread(() -> {
                    progressDialog.dismiss();

                    if (codeEditText != null) {
                        codeEditText.setText(content);
                        codeEditText.setSelection(0);
                    }

                    updateStatusDisplay();
                    UserFeedbackManager.getInstance().showToast(
                        MainActivity.this,
                        "文件预览加载成功（编码：" + encoding + "）"
                    );
                });
            }

            @Override
            public void onLoadError(String error) {
                runOnUiThread(() -> {
                    progressDialog.dismiss();
                    UserFeedbackManager.getInstance().showErrorDialog(
                        MainActivity.this,
                        "预览加载失败",
                        error
                    );
                });
            }

            @Override
            public void onTimeout() {
                runOnUiThread(() -> {
                    progressDialog.dismiss();
                    UserFeedbackManager.getInstance().showErrorDialog(
                        MainActivity.this,
                        "预览加载超时",
                        "文件预览加载时间过长，请尝试选择其他文件。"
                    );
                });
            }
        });
    }

    /**
     * 请求基础权限
     */
    private void requestBasicPermissions() {
        PermissionManager.checkAndRequestEssentialPermissions(this, new PermissionManager.PermissionCallback() {
            @Override
            public void onPermissionGranted() {
                UserFeedbackManager.getInstance().showToast(MainActivity.this, getString(R.string.permission_granted));
            }

            @Override
            public void onPermissionDenied(String[] deniedPermissions) {
                UserFeedbackManager.getInstance().showToast(MainActivity.this, getString(R.string.permission_denied_some_features_unavailable));
            }

            @Override
            public void onPermissionPermanentlyDenied(String[] permanentlyDeniedPermissions) {
                PermissionManager.handlePermanentlyDeniedPermissions(MainActivity.this, permanentlyDeniedPermissions);
            }
        });
    }

    /**
     * 发送崩溃报告
     */
    private void sendCrashReport(String crashLog) {
        // TODO: 实现崩溃报告发送逻辑
        UserFeedbackManager.getInstance().showToast(this, getString(R.string.crash_report_sent));
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        // 添加调试日志
        android.util.Log.d("MainActivity", "Permission result - requestCode: " + requestCode + ", permissions: " + java.util.Arrays.toString(permissions));
        for (int i = 0; i < grantResults.length; i++) {
            android.util.Log.d("MainActivity", "Permission " + permissions[i] + " result: " + (grantResults[i] == android.content.pm.PackageManager.PERMISSION_GRANTED ? "GRANTED" : "DENIED"));
        }

        PermissionManager.handlePermissionResult(this, requestCode, permissions, grantResults,
            new PermissionManager.PermissionCallback() {
                @Override
                public void onPermissionGranted() {
                    UserFeedbackManager.getInstance().showToast(MainActivity.this, getString(R.string.permission_granted));
                    // 重新检查权限状态
                    android.util.Log.d("MainActivity", "After grant - hasBasicStoragePermission: " + PermissionManager.hasBasicStoragePermission(MainActivity.this));
                }

                @Override
                public void onPermissionDenied(String[] deniedPermissions) {
                    UserFeedbackManager.getInstance().showToast(MainActivity.this, getString(R.string.permission_denied_some_features_unavailable));
                    android.util.Log.d("MainActivity", "Permissions denied: " + java.util.Arrays.toString(deniedPermissions));
                }

                @Override
                public void onPermissionPermanentlyDenied(String[] permanentlyDeniedPermissions) {
                    PermissionManager.handlePermanentlyDeniedPermissions(MainActivity.this, permanentlyDeniedPermissions);
                    android.util.Log.d("MainActivity", "Permissions permanently denied: " + java.util.Arrays.toString(permanentlyDeniedPermissions));
                }
            });
    }

}