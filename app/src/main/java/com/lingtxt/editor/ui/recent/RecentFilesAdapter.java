package com.lingtxt.editor.ui.recent;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.PopupMenu;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.lingtxt.editor.R;
import com.lingtxt.editor.data.model.RecentFile;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * 最近文件列表适配器
 */
public class RecentFilesAdapter extends RecyclerView.Adapter<RecentFilesAdapter.ViewHolder> {

    private List<RecentFile> files = new ArrayList<>();
    private OnFileActionListener listener;

    public interface OnFileActionListener {
        void onFileClick(RecentFile file);
        void onFileRemove(RecentFile file);
    }

    public RecentFilesAdapter(OnFileActionListener listener) {
        this.listener = listener;
    }

    public void updateFiles(List<RecentFile> newFiles) {
        this.files = new ArrayList<>(newFiles);
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_recent_file, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        RecentFile file = files.get(position);
        holder.bind(file);
    }

    @Override
    public int getItemCount() {
        return files.size();
    }

    class ViewHolder extends RecyclerView.ViewHolder {
        private ImageView ivFileIcon;
        private TextView tvFileName;
        private TextView tvFilePath;
        private TextView tvFileSize;
        private TextView tvLastAccessed;
        private TextView tvEncoding;
        private ImageButton btnMore;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            
            ivFileIcon = itemView.findViewById(R.id.iv_file_icon);
            tvFileName = itemView.findViewById(R.id.tv_file_name);
            tvFilePath = itemView.findViewById(R.id.tv_file_path);
            tvFileSize = itemView.findViewById(R.id.tv_file_size);
            tvLastAccessed = itemView.findViewById(R.id.tv_last_accessed);
            tvEncoding = itemView.findViewById(R.id.tv_encoding);
            btnMore = itemView.findViewById(R.id.btn_more);
        }

        public void bind(RecentFile file) {
            // 设置文件图标
            ivFileIcon.setImageResource(file.getFileTypeIcon());
            
            // 设置文件名
            tvFileName.setText(file.getFileName());
            
            // 设置文件路径
            tvFilePath.setText(file.getFilePath());
            
            // 设置文件大小
            tvFileSize.setText(file.getFormattedFileSize());
            
            // 设置访问时间
            tvLastAccessed.setText(formatAccessTime(file.getLastAccessed()));
            
            // 设置编码
            if (file.getEncoding() != null && !file.getEncoding().isEmpty()) {
                tvEncoding.setText(file.getEncoding());
                tvEncoding.setVisibility(View.VISIBLE);
            } else {
                tvEncoding.setVisibility(View.GONE);
            }
            
            // 设置点击事件
            itemView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onFileClick(file);
                }
            });
            
            // 设置更多操作按钮
            btnMore.setOnClickListener(v -> showPopupMenu(v, file));
        }

        private void showPopupMenu(View anchor, RecentFile file) {
            PopupMenu popup = new PopupMenu(anchor.getContext(), anchor);
            popup.getMenuInflater().inflate(R.menu.recent_file_item_menu, popup.getMenu());
            
            popup.setOnMenuItemClickListener(item -> {
                int itemId = item.getItemId();
                if (itemId == R.id.action_open) {
                    if (listener != null) {
                        listener.onFileClick(file);
                    }
                    return true;
                } else if (itemId == R.id.action_remove) {
                    if (listener != null) {
                        listener.onFileRemove(file);
                    }
                    return true;
                }
                return false;
            });
            
            popup.show();
        }

        private String formatAccessTime(long timestamp) {
            long now = System.currentTimeMillis();
            long diff = now - timestamp;
            
            // 小于1分钟
            if (diff < 60 * 1000) {
                return "刚刚";
            }
            
            // 小于1小时
            if (diff < 60 * 60 * 1000) {
                int minutes = (int) (diff / (60 * 1000));
                return minutes + "分钟前";
            }
            
            // 小于1天
            if (diff < 24 * 60 * 60 * 1000) {
                int hours = (int) (diff / (60 * 60 * 1000));
                return hours + "小时前";
            }
            
            // 小于7天
            if (diff < 7 * 24 * 60 * 60 * 1000) {
                int days = (int) (diff / (24 * 60 * 60 * 1000));
                return days + "天前";
            }
            
            // 超过7天，显示具体日期
            SimpleDateFormat sdf = new SimpleDateFormat("MM-dd HH:mm", Locale.getDefault());
            return sdf.format(new Date(timestamp));
        }
    }
}