package com.lingtxt.editor.ui.widget;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.view.View;
import android.widget.ScrollView;
import androidx.core.widget.NestedScrollView;

/**
 * 阅读进度指示器
 * 在屏幕侧边显示微妙的阅读进度条
 */
public class ReadingProgressIndicator extends View {

    private Paint progressPaint;
    private Paint backgroundPaint;
    private RectF progressRect;
    
    // 配置参数
    private static final int INDICATOR_WIDTH_DP = 3; // 指示器宽度
    private static final int MARGIN_DP = 8; // 边距
    
    // 进度相关
    private float progress = 0f; // 0.0 - 1.0
    private boolean isVisible = true;
    
    // 颜色配置
    private int progressColor = 0x80FF4081; // 半透明粉色
    private int backgroundColor = 0x20000000; // 半透明黑色

    public ReadingProgressIndicator(Context context) {
        super(context);
        init();
    }

    public ReadingProgressIndicator(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public ReadingProgressIndicator(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        float density = getContext().getResources().getDisplayMetrics().density;
        
        // 初始化画笔
        progressPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        progressPaint.setColor(progressColor);
        progressPaint.setStyle(Paint.Style.FILL);
        
        backgroundPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        backgroundPaint.setColor(backgroundColor);
        backgroundPaint.setStyle(Paint.Style.FILL);
        
        progressRect = new RectF();
        
        // 设置视图属性
        setWillNotDraw(false);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        
        if (!isVisible || progress < 0) return;
        
        float density = getContext().getResources().getDisplayMetrics().density;
        float indicatorWidth = INDICATOR_WIDTH_DP * density;
        float margin = MARGIN_DP * density;
        
        int width = getWidth();
        int height = getHeight();
        
        // 计算指示器位置（右侧）
        float left = width - indicatorWidth - margin;
        float right = width - margin;
        float top = margin;
        float bottom = height - margin;
        
        // 绘制背景
        progressRect.set(left, top, right, bottom);
        canvas.drawRoundRect(progressRect, indicatorWidth / 2, indicatorWidth / 2, backgroundPaint);
        
        // 绘制进度
        if (progress > 0) {
            float progressHeight = (bottom - top) * progress;
            progressRect.set(left, top, right, top + progressHeight);
            canvas.drawRoundRect(progressRect, indicatorWidth / 2, indicatorWidth / 2, progressPaint);
        }
    }

    /**
     * 设置进度
     * @param progress 进度值 0.0 - 1.0
     */
    public void setProgress(float progress) {
        this.progress = Math.max(0f, Math.min(1f, progress));
        invalidate();
    }

    /**
     * 获取当前进度
     */
    public float getProgress() {
        return progress;
    }

    /**
     * 设置是否可见
     */
    public void setProgressVisible(boolean visible) {
        if (this.isVisible != visible) {
            this.isVisible = visible;
            setVisibility(visible ? VISIBLE : GONE);
            invalidate();
        }
    }

    /**
     * 检查是否可见
     */
    public boolean isProgressVisible() {
        return isVisible;
    }

    /**
     * 设置进度颜色
     */
    public void setProgressColor(int color) {
        this.progressColor = color;
        progressPaint.setColor(color);
        invalidate();
    }

    /**
     * 设置背景颜色
     */
    public void setBackgroundColor(int color) {
        this.backgroundColor = color;
        backgroundPaint.setColor(color);
        invalidate();
    }

    /**
     * 根据ScrollView自动更新进度
     */
    public void attachToScrollView(ScrollView scrollView) {
        if (scrollView == null) return;
        
        scrollView.getViewTreeObserver().addOnScrollChangedListener(() -> {
            updateProgressFromScrollView(scrollView);
        });
    }

    /**
     * 根据NestedScrollView自动更新进度
     */
    public void attachToNestedScrollView(NestedScrollView scrollView) {
        if (scrollView == null) return;
        
        scrollView.setOnScrollChangeListener((NestedScrollView.OnScrollChangeListener) 
            (v, scrollX, scrollY, oldScrollX, oldScrollY) -> {
                updateProgressFromNestedScrollView(scrollView);
            });
    }

    /**
     * 从ScrollView更新进度
     */
    private void updateProgressFromScrollView(ScrollView scrollView) {
        View child = scrollView.getChildAt(0);
        if (child == null) return;
        
        int scrollY = scrollView.getScrollY();
        int scrollViewHeight = scrollView.getHeight();
        int childHeight = child.getHeight();
        
        int maxScroll = Math.max(0, childHeight - scrollViewHeight);
        if (maxScroll > 0) {
            float progress = (float) scrollY / maxScroll;
            setProgress(progress);
        } else {
            setProgress(0f);
        }
    }

    /**
     * 从NestedScrollView更新进度
     */
    private void updateProgressFromNestedScrollView(NestedScrollView scrollView) {
        View child = scrollView.getChildAt(0);
        if (child == null) return;
        
        int scrollY = scrollView.getScrollY();
        int scrollViewHeight = scrollView.getHeight();
        int childHeight = child.getHeight();
        
        int maxScroll = Math.max(0, childHeight - scrollViewHeight);
        if (maxScroll > 0) {
            float progress = (float) scrollY / maxScroll;
            setProgress(progress);
        } else {
            setProgress(0f);
        }
    }

    /**
     * 根据文本编辑器更新进度
     */
    public void updateProgressFromEditor(View editorView) {
        if (editorView == null) return;
        
        // 获取滚动信息
        int scrollY = editorView.getScrollY();
        int viewHeight = editorView.getHeight();
        
        // 如果编辑器有Layout，使用更精确的计算
        if (editorView instanceof android.widget.TextView) {
            android.widget.TextView textView = (android.widget.TextView) editorView;
            android.text.Layout layout = textView.getLayout();
            
            if (layout != null) {
                int totalHeight = layout.getHeight();
                int maxScroll = Math.max(0, totalHeight - viewHeight);
                
                if (maxScroll > 0) {
                    float progress = (float) scrollY / maxScroll;
                    setProgress(progress);
                } else {
                    setProgress(0f);
                }
                return;
            }
        }
        
        // 默认计算方式
        int contentHeight = editorView.getHeight();
        int maxScroll = Math.max(0, contentHeight - viewHeight);
        
        if (maxScroll > 0) {
            float progress = (float) scrollY / maxScroll;
            setProgress(progress);
        } else {
            setProgress(0f);
        }
    }
}