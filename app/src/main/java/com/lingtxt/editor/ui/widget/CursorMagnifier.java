package com.lingtxt.editor.ui.widget;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.RectF;
import android.os.Handler;
import android.os.Looper;
import android.text.Layout;
import android.util.AttributeSet;
import android.view.View;
import android.widget.EditText;

/**
 * 光标放大镜
 * 在光标移动时显示放大的文本区域，帮助精确定位
 */
public class CursorMagnifier extends View {

    private EditText targetEditText;
    private Paint backgroundPaint;
    private Paint textPaint;
    private Paint borderPaint;
    
    private float magnificationScale = 1.5f;
    private int magnifierSize = 120; // dp
    private int textRadius = 40; // dp，放大镜中显示的文本半径
    
    private float cursorX, cursorY;
    private boolean isVisible = false;
    private Handler hideHandler = new Handler(Looper.getMainLooper());
    private Runnable hideRunnable;
    
    private static final int AUTO_HIDE_DELAY = 2000; // 2秒后自动隐藏

    public CursorMagnifier(Context context) {
        super(context);
        init();
    }

    public CursorMagnifier(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    private void init() {
        // 转换dp到px
        float density = getContext().getResources().getDisplayMetrics().density;
        magnifierSize = (int) (magnifierSize * density);
        textRadius = (int) (textRadius * density);
        
        // 初始化画笔
        backgroundPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        backgroundPaint.setColor(0xF0FFFFFF); // 半透明白色背景
        backgroundPaint.setShadowLayer(8f, 0f, 4f, 0x40000000);
        
        textPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        textPaint.setColor(0xFF000000);
        
        borderPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        borderPaint.setColor(0xFFCCCCCC);
        borderPaint.setStyle(Paint.Style.STROKE);
        borderPaint.setStrokeWidth(2f);
        
        setVisibility(GONE);
        
        hideRunnable = () -> hide();
    }

    /**
     * 设置目标EditText
     */
    public void setTargetEditText(EditText editText) {
        this.targetEditText = editText;
        if (editText != null) {
            // 复制EditText的文本样式
            textPaint.setTextSize(editText.getTextSize() * magnificationScale);
            textPaint.setTypeface(editText.getTypeface());
        }
    }

    /**
     * 显示放大镜
     */
    public void show(float x, float y) {
        if (targetEditText == null) return;
        
        cursorX = x;
        cursorY = y;
        
        // 调整放大镜位置，避免被手指遮挡
        float magnifierX = x - magnifierSize / 2f;
        float magnifierY = y - magnifierSize - 50f; // 在光标上方50dp
        
        // 确保放大镜在屏幕范围内
        magnifierX = Math.max(0, Math.min(getWidth() - magnifierSize, magnifierX));
        magnifierY = Math.max(0, Math.min(getHeight() - magnifierSize, magnifierY));
        
        setX(magnifierX);
        setY(magnifierY);
        
        if (!isVisible) {
            isVisible = true;
            setVisibility(VISIBLE);
            
            // 淡入动画
            setAlpha(0f);
            animate()
                .alpha(1f)
                .setDuration(150)
                .start();
        }
        
        invalidate();
        
        // 重置自动隐藏计时器
        hideHandler.removeCallbacks(hideRunnable);
        hideHandler.postDelayed(hideRunnable, AUTO_HIDE_DELAY);
    }

    /**
     * 隐藏放大镜
     */
    public void hide() {
        if (isVisible) {
            isVisible = false;
            
            // 淡出动画
            animate()
                .alpha(0f)
                .setDuration(150)
                .withEndAction(() -> setVisibility(GONE))
                .start();
        }
        
        hideHandler.removeCallbacks(hideRunnable);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        
        if (!isVisible || targetEditText == null) return;
        
        // 绘制放大镜背景
        float centerX = magnifierSize / 2f;
        float centerY = magnifierSize / 2f;
        float radius = magnifierSize / 2f - 10f;
        
        canvas.drawCircle(centerX, centerY, radius, backgroundPaint);
        canvas.drawCircle(centerX, centerY, radius, borderPaint);
        
        // 绘制放大的文本内容
        drawMagnifiedText(canvas, centerX, centerY);
        
        // 绘制十字准线
        drawCrosshair(canvas, centerX, centerY);
    }

    private void drawMagnifiedText(Canvas canvas, float centerX, float centerY) {
        Layout layout = targetEditText.getLayout();
        if (layout == null) return;
        
        try {
            // 获取光标位置对应的文本偏移
            int offset = targetEditText.getOffsetForPosition(cursorX, cursorY);
            if (offset < 0 || offset >= targetEditText.getText().length()) return;
            
            String text = targetEditText.getText().toString();
            
            // 获取光标周围的文本
            int start = Math.max(0, offset - textRadius / 10);
            int end = Math.min(text.length(), offset + textRadius / 10);
            String surroundingText = text.substring(start, end);
            
            // 计算文本绘制位置
            float textX = centerX;
            float textY = centerY + textPaint.getTextSize() / 3f;
            
            // 绘制文本
            canvas.drawText(surroundingText, textX, textY, textPaint);
            
        } catch (Exception e) {
            // 忽略异常
        }
    }

    private void drawCrosshair(Canvas canvas, float centerX, float centerY) {
        Paint crosshairPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        crosshairPaint.setColor(0x80FF0000); // 半透明红色
        crosshairPaint.setStrokeWidth(1f);
        
        // 绘制十字准线
        canvas.drawLine(centerX - 10f, centerY, centerX + 10f, centerY, crosshairPaint);
        canvas.drawLine(centerX, centerY - 10f, centerX, centerY + 10f, crosshairPaint);
    }

    /**
     * 设置放大倍数
     */
    public void setMagnificationScale(float scale) {
        this.magnificationScale = Math.max(1.2f, Math.min(3.0f, scale));
        if (targetEditText != null) {
            textPaint.setTextSize(targetEditText.getTextSize() * magnificationScale);
        }
    }


    /**
     * 设置自动隐藏延迟
     */
    public void setAutoHideDelay(int delayMs) {
        hideHandler.removeCallbacks(hideRunnable);
        if (isVisible) {
            hideHandler.postDelayed(hideRunnable, delayMs);
        }
    }
}