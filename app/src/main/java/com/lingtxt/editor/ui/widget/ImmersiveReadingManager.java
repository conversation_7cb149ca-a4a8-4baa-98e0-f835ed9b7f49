package com.lingtxt.editor.ui.widget;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ValueAnimator;
import android.os.Handler;
import android.os.Looper;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.DecelerateInterpolator;
import java.util.ArrayList;
import java.util.List;

/**
 * 沉浸式阅读管理器
 * 实现渐进式UI隐藏和智能恢复机制
 */
public class ImmersiveReadingManager {

    public interface OnImmersiveModeChangeListener {
        void onEnterImmersiveMode();
        void onExitImmersiveMode();
    }

    private final List<View> hidableViews = new ArrayList<>();
    private final Handler handler = new Handler(Looper.getMainLooper());
    private OnImmersiveModeChangeListener listener;
    
    // 配置参数
    private static final long AUTO_HIDE_DELAY = 10000; // 10秒后自动隐藏
    private static final long ANIMATION_DURATION = 300; // 动画时长
    private static final int EDGE_TOUCH_THRESHOLD = 50; // 边缘触摸阈值（dp）
    
    // 状态管理
    private boolean isImmersiveMode = false;
    private boolean isAnimating = false;
    private long lastUserInteraction = 0;
    
    // 自动隐藏任务
    private final Runnable autoHideRunnable = new Runnable() {
        @Override
        public void run() {
            if (!isImmersiveMode && !isAnimating) {
                enterImmersiveMode();
            }
        }
    };

    /**
     * 添加可隐藏的视图
     */
    public void addHidableView(View view) {
        if (view != null && !hidableViews.contains(view)) {
            hidableViews.add(view);
        }
    }

    /**
     * 移除可隐藏的视图
     */
    public void removeHidableView(View view) {
        hidableViews.remove(view);
    }

    /**
     * 设置沉浸式模式变化监听器
     */
    public void setOnImmersiveModeChangeListener(OnImmersiveModeChangeListener listener) {
        this.listener = listener;
    }

    /**
     * 处理用户交互事件
     */
    public void onUserInteraction() {
        lastUserInteraction = System.currentTimeMillis();
        
        // 如果在沉浸式模式，退出
        if (isImmersiveMode) {
            exitImmersiveMode();
        } else {
            // 重置自动隐藏计时器
            resetAutoHideTimer();
        }
    }

    /**
     * 处理触摸事件，检测边缘触摸
     */
    public boolean onTouchEvent(MotionEvent event, View parentView) {
        if (event.getAction() == MotionEvent.ACTION_DOWN) {
            if (isImmersiveMode && isEdgeTouch(event, parentView)) {
                exitImmersiveMode();
                return true;
            } else {
                onUserInteraction();
            }
        }
        return false;
    }

    /**
     * 检测是否为边缘触摸
     */
    private boolean isEdgeTouch(MotionEvent event, View parentView) {
        if (parentView == null) return false;
        
        float density = parentView.getContext().getResources().getDisplayMetrics().density;
        int threshold = (int) (EDGE_TOUCH_THRESHOLD * density);
        
        float x = event.getX();
        float y = event.getY();
        int width = parentView.getWidth();
        int height = parentView.getHeight();
        
        // 检查是否在边缘区域
        return x <= threshold || x >= width - threshold || 
               y <= threshold || y >= height - threshold;
    }

    /**
     * 进入沉浸式模式
     */
    public void enterImmersiveMode() {
        if (isImmersiveMode || isAnimating) return;
        
        isAnimating = true;
        isImmersiveMode = true;
        
        // 隐藏所有可隐藏的视图
        for (View view : hidableViews) {
            if (view != null && view.getVisibility() == View.VISIBLE) {
                animateViewHide(view);
            }
        }
        
        // 通知监听器
        if (listener != null) {
            listener.onEnterImmersiveMode();
        }
        
        // 动画完成后重置状态
        handler.postDelayed(() -> isAnimating = false, ANIMATION_DURATION);
    }

    /**
     * 退出沉浸式模式
     */
    public void exitImmersiveMode() {
        if (!isImmersiveMode || isAnimating) return;
        
        isAnimating = true;
        isImmersiveMode = false;
        
        // 显示所有可隐藏的视图
        for (View view : hidableViews) {
            if (view != null) {
                animateViewShow(view);
            }
        }
        
        // 通知监听器
        if (listener != null) {
            listener.onExitImmersiveMode();
        }
        
        // 重置自动隐藏计时器
        resetAutoHideTimer();
        
        // 动画完成后重置状态
        handler.postDelayed(() -> isAnimating = false, ANIMATION_DURATION);
    }

    /**
     * 动画隐藏视图
     */
    private void animateViewHide(View view) {
        if (view.getTag(com.lingtxt.editor.R.id.tag_original_visibility) == null) {
            view.setTag(com.lingtxt.editor.R.id.tag_original_visibility, view.getVisibility());
        }

        // 获取视图高度，如果为0则使用默认值
        float translationY = view.getHeight() > 0 ? -view.getHeight() / 2f : -50f;

        view.animate()
            .alpha(0f)
            .translationY(translationY)
            .setDuration(ANIMATION_DURATION)
            .setInterpolator(new DecelerateInterpolator())
            .withEndAction(() -> view.setVisibility(View.GONE))
            .start();
    }

    /**
     * 动画显示视图
     */
    private void animateViewShow(View view) {
        Object originalVisibility = view.getTag(com.lingtxt.editor.R.id.tag_original_visibility);
        int visibility = originalVisibility instanceof Integer ?
                        (Integer) originalVisibility : View.VISIBLE;

        view.setVisibility(visibility);
        view.setAlpha(0f);

        // 获取视图高度，如果为0则使用默认值
        float translationY = view.getHeight() > 0 ? -view.getHeight() / 2f : -50f;
        view.setTranslationY(translationY);

        view.animate()
            .alpha(1f)
            .translationY(0f)
            .setDuration(ANIMATION_DURATION)
            .setInterpolator(new DecelerateInterpolator())
            .start();
    }

    /**
     * 重置自动隐藏计时器
     */
    private void resetAutoHideTimer() {
        handler.removeCallbacks(autoHideRunnable);
        handler.postDelayed(autoHideRunnable, AUTO_HIDE_DELAY);
    }

    /**
     * 停止自动隐藏计时器
     */
    public void stopAutoHideTimer() {
        handler.removeCallbacks(autoHideRunnable);
    }

    /**
     * 启动自动隐藏计时器
     */
    public void startAutoHideTimer() {
        resetAutoHideTimer();
    }

    /**
     * 检查是否在沉浸式模式
     */
    public boolean isImmersiveMode() {
        return isImmersiveMode;
    }

    /**
     * 强制退出沉浸式模式（不带动画）
     */
    public void forceExitImmersiveMode() {
        if (!isImmersiveMode) return;
        
        isImmersiveMode = false;
        isAnimating = false;
        
        // 立即显示所有视图
        for (View view : hidableViews) {
            if (view != null) {
                Object originalVisibility = view.getTag(com.lingtxt.editor.R.id.tag_original_visibility);
                int visibility = originalVisibility instanceof Integer ? 
                                (Integer) originalVisibility : View.VISIBLE;
                view.setVisibility(visibility);
                view.setAlpha(1f);
                view.setTranslationY(0f);
            }
        }
        
        stopAutoHideTimer();
        
        if (listener != null) {
            listener.onExitImmersiveMode();
        }
    }

    /**
     * 清理资源
     */
    public void destroy() {
        handler.removeCallbacks(autoHideRunnable);
        hidableViews.clear();
        listener = null;
    }
}