package com.lingtxt.editor.ui.widget;

import android.animation.ArgbEvaluator;
import android.animation.ValueAnimator;
import android.os.Handler;
import android.os.Looper;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.style.BackgroundColorSpan;
import android.widget.EditText;
import com.lingtxt.editor.R;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;

/**
 * 文本搜索管理器
 * 提供文本搜索、替换、高亮显示等功能
 */
public class TextSearchManager {

    /**
     * 搜索结果
     */
    public static class SearchResult {
        public int start;
        public int end;
        public String matchedText;

        public SearchResult(int start, int end, String matchedText) {
            this.start = start;
            this.end = end;
            this.matchedText = matchedText;
        }

        @Override
        public String toString() {
            return String.format("SearchResult{start=%d, end=%d, text='%s'}", 
                               start, end, matchedText);
        }
    }

    /**
     * 搜索选项
     */
    public static class SearchOptions {
        public boolean caseSensitive = false;
        public boolean wholeWord = false;
        public boolean useRegex = false;
        public boolean wrapAround = true;

        public SearchOptions() {}

        public SearchOptions(boolean caseSensitive, boolean wholeWord, 
                           boolean useRegex, boolean wrapAround) {
            this.caseSensitive = caseSensitive;
            this.wholeWord = wholeWord;
            this.useRegex = useRegex;
            this.wrapAround = wrapAround;
        }
    }

    private EditText editText;
    private final List<SearchResult> searchResults = new ArrayList<>();
    private final List<BackgroundColorSpan> highlightSpans = new ArrayList<>();
    
    private String currentSearchText = "";
    private SearchOptions currentOptions = new SearchOptions();
    private int currentResultIndex = -1;
    
    // 颜色配置
    private int highlightColor = 0x40FFFF00; // 黄色高亮（更透明）
    private int currentHighlightColor = 0x50FF8800; // 橙色当前结果（更透明）
    private int animationStartColor = 0x60FF0000; // 动画起始颜色（红色）
    private int animationEndColor = 0x40FFFF00; // 动画结束颜色（黄色）
    
    // 动画相关
    private ValueAnimator rippleAnimator;
    private ValueAnimator breathingAnimator;
    private Handler animationHandler = new Handler(Looper.getMainLooper());
    
    // 监听器
    private OnSearchResultListener searchResultListener;
    
    public TextSearchManager(EditText editText) {
        this.editText = editText;
    }

    /**
     * 设置EditText（延迟初始化）
     */
    public void setEditText(EditText editText) {
        this.editText = editText;
    }

    /**
     * 搜索文本
     */
    public int search(String searchText, SearchOptions options) {
        clearHighlights();
        searchResults.clear();

        if (editText == null || searchText == null || searchText.isEmpty()) {
            currentSearchText = "";
            notifySearchResult(0, 0);
            return 0;
        }

        currentSearchText = searchText;
        currentOptions = options != null ? options : new SearchOptions();

        String text = editText.getText().toString();
        if (text.isEmpty()) {
            notifySearchResult(0, 0);
            return 0;
        }
        
        try {
            Pattern pattern = createSearchPattern(searchText, currentOptions);
            Matcher matcher = pattern.matcher(text);
            
            while (matcher.find()) {
                searchResults.add(new SearchResult(
                    matcher.start(), 
                    matcher.end(), 
                    matcher.group()
                ));
            }
            
            if (!searchResults.isEmpty()) {
                highlightAllResults();
                currentResultIndex = findNearestResult();
                highlightCurrentResult();
            } else {
                currentResultIndex = -1;
            }
            
            notifySearchResult(searchResults.size(), currentResultIndex + 1);
            return searchResults.size();
            
        } catch (PatternSyntaxException e) {
            // 正则表达式语法错误
            String errorMessage = editText.getContext().getString(R.string.search_regex_error) + ": " + e.getMessage();
            notifySearchError(errorMessage);
            return 0;
        }
    }

    /**
     * 创建搜索模式
     */
    private Pattern createSearchPattern(String searchText, SearchOptions options) {
        String patternText = searchText;
        
        if (!options.useRegex) {
            // 转义正则表达式特殊字符
            patternText = Pattern.quote(searchText);
        }
        
        if (options.wholeWord) {
            patternText = "\\b" + patternText + "\\b";
        }
        
        int flags = 0;
        if (!options.caseSensitive) {
            flags |= Pattern.CASE_INSENSITIVE;
        }
        
        return Pattern.compile(patternText, flags);
    }

    /**
     * 查找最近的搜索结果
     */
    private int findNearestResult() {
        if (searchResults.isEmpty()) return -1;
        
        int cursorPosition = editText.getSelectionStart();
        
        // 查找光标位置之后的第一个结果
        for (int i = 0; i < searchResults.size(); i++) {
            if (searchResults.get(i).start >= cursorPosition) {
                return i;
            }
        }
        
        // 如果没找到，返回第一个结果（环绕搜索）
        return currentOptions.wrapAround ? 0 : -1;
    }

    /**
     * 查找下一个结果
     */
    public boolean findNext() {
        if (searchResults.isEmpty()) return false;
        
        if (currentResultIndex < searchResults.size() - 1) {
            currentResultIndex++;
        } else if (currentOptions.wrapAround) {
            currentResultIndex = 0;
        } else {
            return false;
        }
        
        highlightCurrentResult();
        scrollToCurrentResult();
        notifySearchResult(searchResults.size(), currentResultIndex + 1);
        return true;
    }

    /**
     * 查找上一个结果
     */
    public boolean findPrevious() {
        if (searchResults.isEmpty()) return false;
        
        if (currentResultIndex > 0) {
            currentResultIndex--;
        } else if (currentOptions.wrapAround) {
            currentResultIndex = searchResults.size() - 1;
        } else {
            return false;
        }
        
        highlightCurrentResult();
        scrollToCurrentResult();
        notifySearchResult(searchResults.size(), currentResultIndex + 1);
        return true;
    }

    /**
     * 替换当前结果
     */
    public boolean replaceCurrent(String replacement) {
        if (currentResultIndex < 0 || currentResultIndex >= searchResults.size()) {
            return false;
        }
        
        SearchResult result = searchResults.get(currentResultIndex);
        
        // 执行替换
        editText.getText().replace(result.start, result.end, replacement);
        
        // 重新搜索以更新结果
        int newResultCount = search(currentSearchText, currentOptions);
        
        return newResultCount > 0;
    }

    /**
     * 替换所有结果
     */
    public int replaceAll(String replacement) {
        if (searchResults.isEmpty()) return 0;
        
        int replacedCount = 0;
        
        // 从后往前替换，避免位置偏移问题
        for (int i = searchResults.size() - 1; i >= 0; i--) {
            SearchResult result = searchResults.get(i);
            editText.getText().replace(result.start, result.end, replacement);
            replacedCount++;
        }
        
        // 清空搜索结果
        clearHighlights();
        searchResults.clear();
        currentResultIndex = -1;
        
        notifySearchResult(0, 0);
        return replacedCount;
    }

    /**
     * 高亮所有搜索结果
     */
    private void highlightAllResults() {
        Spannable spannable = editText.getText();
        
        for (SearchResult result : searchResults) {
            BackgroundColorSpan span = new BackgroundColorSpan(highlightColor);
            spannable.setSpan(span, result.start, result.end, 
                             Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            highlightSpans.add(span);
        }
    }

    /**
     * 高亮当前搜索结果
     */
    private void highlightCurrentResult() {
        if (currentResultIndex < 0 || currentResultIndex >= searchResults.size()) {
            return;
        }
        
        // 先清除之前的当前结果高亮
        clearCurrentHighlight();
        
        SearchResult result = searchResults.get(currentResultIndex);
        
        // 应用流水动画效果
        applyRippleAnimation(result);
        
        // 启动呼吸效果
        startBreathingEffect(result);
    }

    /**
     * 清除当前结果的高亮
     */
    private void clearCurrentHighlight() {
        if (currentResultIndex < 0 || currentResultIndex >= searchResults.size()) {
            return;
        }
        
        SearchResult result = searchResults.get(currentResultIndex);
        Spannable spannable = editText.getText();
        
        // 移除当前位置的高亮
        BackgroundColorSpan[] spans = spannable.getSpans(
            result.start, result.end, BackgroundColorSpan.class);
        
        for (BackgroundColorSpan span : spans) {
            if (spannable.getSpanStart(span) == result.start && 
                spannable.getSpanEnd(span) == result.end) {
                spannable.removeSpan(span);
                highlightSpans.remove(span);
            }
        }
        
        // 重新添加普通高亮
        BackgroundColorSpan normalSpan = new BackgroundColorSpan(highlightColor);
        spannable.setSpan(normalSpan, result.start, result.end, 
                         Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        highlightSpans.add(normalSpan);
    }

    /**
     * 滚动到当前搜索结果
     */
    private void scrollToCurrentResult() {
        if (currentResultIndex < 0 || currentResultIndex >= searchResults.size()) {
            return;
        }
        
        SearchResult result = searchResults.get(currentResultIndex);
        
        // 设置选择范围
        editText.setSelection(result.start, result.end);
        
        // 滚动到可见区域
        editText.requestFocus();
    }

    /**
     * 清除所有高亮
     */
    public void clearHighlights() {
        // 停止呼吸效果
        stopBreathingEffect();
        
        Spannable spannable = editText.getText();
        
        for (BackgroundColorSpan span : highlightSpans) {
            spannable.removeSpan(span);
        }
        
        highlightSpans.clear();
    }

    /**
     * 清除搜索
     */
    public void clearSearch() {
        clearHighlights();
        searchResults.clear();
        currentSearchText = "";
        currentResultIndex = -1;
        notifySearchResult(0, 0);
    }

    /**
     * 获取当前搜索文本
     */
    public String getCurrentSearchText() {
        return currentSearchText;
    }

    /**
     * 获取搜索结果数量
     */
    public int getResultCount() {
        return searchResults.size();
    }

    /**
     * 获取当前结果索引
     */
    public int getCurrentResultIndex() {
        return currentResultIndex;
    }

    /**
     * 获取指定索引的搜索结果
     */
    public SearchResult getResult(int index) {
        if (index >= 0 && index < searchResults.size()) {
            return searchResults.get(index);
        }
        return null;
    }

    /**
     * 跳转到指定的搜索结果
     */
    public boolean gotoResult(int index) {
        if (index < 0 || index >= searchResults.size()) {
            return false;
        }
        
        currentResultIndex = index;
        highlightCurrentResult();
        scrollToCurrentResult();
        notifySearchResult(searchResults.size(), currentResultIndex + 1);
        return true;
    }

    /**
     * 设置高亮颜色
     */
    public void setHighlightColor(int color) {
        this.highlightColor = color;
    }

    /**
     * 设置当前结果高亮颜色
     */
    public void setCurrentHighlightColor(int color) {
        this.currentHighlightColor = color;
    }

    /**
     * 应用流水动画效果
     */
    private void applyRippleAnimation(SearchResult result) {
        Spannable spannable = editText.getText();
        
        // 停止之前的动画
        if (rippleAnimator != null && rippleAnimator.isRunning()) {
            rippleAnimator.cancel();
        }
        
        // 创建动画背景span
        BackgroundColorSpan animationSpan = new BackgroundColorSpan(animationStartColor);
        spannable.setSpan(animationSpan, result.start, result.end, 
                         Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        highlightSpans.add(animationSpan);
        
        // 创建颜色动画
        rippleAnimator = ValueAnimator.ofObject(new ArgbEvaluator(), 
                                               animationStartColor, 
                                               currentHighlightColor);
        rippleAnimator.setDuration(600); // 600ms动画时长
        
        rippleAnimator.addUpdateListener(animation -> {
            int animatedColor = (int) animation.getAnimatedValue();
            
            // 移除旧的span
            spannable.removeSpan(animationSpan);
            
            // 创建新的span
            BackgroundColorSpan newSpan = new BackgroundColorSpan(animatedColor);
            spannable.setSpan(newSpan, result.start, result.end, 
                             Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            
            // 更新span列表
            highlightSpans.remove(animationSpan);
            highlightSpans.add(newSpan);
        });
        
        // 动画结束后添加波纹扩散效果
        rippleAnimator.addListener(new android.animation.AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(android.animation.Animator animation) {
                // 添加波纹扩散效果
                addRippleEffect(result);
            }
        });
        
        rippleAnimator.start();
    }

    /**
     * 添加波纹扩散效果
     */
    private void addRippleEffect(SearchResult result) {
        // 创建向外扩散的波纹效果
        int rippleRadius = Math.min(result.end - result.start, 10); // 最大扩散10个字符
        
        for (int i = 1; i <= rippleRadius; i++) {
            final int radius = i;
            final int delay = i * 50; // 每层延迟50ms
            
            animationHandler.postDelayed(() -> {
                createRippleLayer(result, radius);
            }, delay);
        }
    }

    /**
     * 创建波纹层
     */
    private void createRippleLayer(SearchResult result, int radius) {
        Spannable spannable = editText.getText();
        
        // 计算波纹范围
        int rippleStart = Math.max(0, result.start - radius);
        int rippleEnd = Math.min(spannable.length(), result.end + radius);
        
        // 创建渐变透明的背景色
        int alpha = Math.max(20, 100 - radius * 15); // 透明度随距离递减
        int rippleColor = (alpha << 24) | (currentHighlightColor & 0x00FFFFFF);
        
        BackgroundColorSpan rippleSpan = new BackgroundColorSpan(rippleColor);
        spannable.setSpan(rippleSpan, rippleStart, rippleEnd, 
                         Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        
        // 500ms后移除波纹层
        animationHandler.postDelayed(() -> {
            spannable.removeSpan(rippleSpan);
        }, 500);
    }

    /**
     * 设置动画颜色
     */
    public void setAnimationColors(int startColor, int endColor) {
        this.animationStartColor = startColor;
        this.animationEndColor = endColor;
    }

    /**
     * 启动呼吸效果（深浅变化）
     */
    private void startBreathingEffect(SearchResult result) {
        // 停止之前的呼吸动画
        if (breathingAnimator != null && breathingAnimator.isRunning()) {
            breathingAnimator.cancel();
        }
        
        Spannable spannable = editText.getText();
        
        // 定义浅色和深色
        int lightColor = 0x30FF8800; // 浅橙色，文字清晰可见
        int darkColor = 0x60FF8800;  // 深橙色，突出显示
        
        // 创建呼吸动画
        breathingAnimator = ValueAnimator.ofObject(new ArgbEvaluator(), lightColor, darkColor);
        breathingAnimator.setDuration(1000); // 1秒一个周期
        breathingAnimator.setRepeatCount(ValueAnimator.INFINITE);
        breathingAnimator.setRepeatMode(ValueAnimator.REVERSE);
        
        final BackgroundColorSpan[] currentSpan = new BackgroundColorSpan[1];
        
        breathingAnimator.addUpdateListener(animation -> {
            int animatedColor = (int) animation.getAnimatedValue();
            
            // 移除旧的span
            if (currentSpan[0] != null) {
                spannable.removeSpan(currentSpan[0]);
                highlightSpans.remove(currentSpan[0]);
            }
            
            // 创建新的span
            currentSpan[0] = new BackgroundColorSpan(animatedColor);
            spannable.setSpan(currentSpan[0], result.start, result.end, 
                             Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            highlightSpans.add(currentSpan[0]);
        });
        
        breathingAnimator.start();
    }

    /**
     * 停止呼吸效果
     */
    private void stopBreathingEffect() {
        if (breathingAnimator != null && breathingAnimator.isRunning()) {
            breathingAnimator.cancel();
        }
    }

    /**
     * 清理动画资源
     */
    public void cleanup() {
        if (rippleAnimator != null && rippleAnimator.isRunning()) {
            rippleAnimator.cancel();
        }
        
        if (breathingAnimator != null && breathingAnimator.isRunning()) {
            breathingAnimator.cancel();
        }
        
        if (animationHandler != null) {
            animationHandler.removeCallbacksAndMessages(null);
        }
    }

    /**
     * 搜索结果监听器
     */
    public interface OnSearchResultListener {
        void onSearchResult(int totalCount, int currentIndex);
        void onSearchError(String error);
    }

    public void setOnSearchResultListener(OnSearchResultListener listener) {
        this.searchResultListener = listener;
    }

    /**
     * 通知搜索结果
     */
    private void notifySearchResult(int totalCount, int currentIndex) {
        if (searchResultListener != null) {
            searchResultListener.onSearchResult(totalCount, currentIndex);
        }
    }

    /**
     * 通知搜索错误
     */
    private void notifySearchError(String error) {
        if (searchResultListener != null) {
            searchResultListener.onSearchError(error);
        }
    }

    /**
     * 获取调试信息
     */
    public String getDebugInfo() {
        return String.format("TextSearchManager{results=%d, current=%d, text='%s'}", 
                           searchResults.size(), currentResultIndex, currentSearchText);
    }
}