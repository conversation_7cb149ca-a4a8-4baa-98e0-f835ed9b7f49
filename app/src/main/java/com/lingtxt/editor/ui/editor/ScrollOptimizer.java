package com.lingtxt.editor.ui.editor;

import android.animation.ValueAnimator;
import android.util.Log;
import android.view.animation.DecelerateInterpolator;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * 滚动性能优化器
 * 实现平滑滚动和预加载机制，提升用户体验
 */
public class ScrollOptimizer {
    
    private static final String TAG = "ScrollOptimizer";
    
    // 滚动配置
    private static final int SMOOTH_SCROLL_DURATION = 300;      // 平滑滚动持续时间（毫秒）
    private static final int PRELOAD_THRESHOLD = 100;           // 预加载阈值（像素）
    private static final int SCROLL_DEBOUNCE_DELAY = 150;       // 滚动防抖延迟（毫秒）
    private static final float FLING_VELOCITY_THRESHOLD = 1000; // 快速滑动速度阈值
    
    // 滚动状态
    private int currentScrollY = 0;
    private int targetScrollY = 0;
    private boolean isScrolling = false;
    private boolean isSmoothScrolling = false;
    private long lastScrollTime = 0;
    
    // 动画器
    private ValueAnimator smoothScrollAnimator;
    
    // 监听器
    private ScrollListener listener;
    
    // 预加载状态
    private int lastPreloadPosition = -1;
    private boolean preloadEnabled = true;
    
    /**
     * 滚动事件监听器
     */
    public interface ScrollListener {
        /**
         * 滚动位置变化时调用
         */
        void onScrollChanged(int scrollY, int deltaY);
        
        /**
         * 滚动开始时调用
         */
        void onScrollStarted();
        
        /**
         * 滚动结束时调用
         */
        void onScrollEnded();
        
        /**
         * 需要预加载内容时调用
         */
        void onPreloadRequested(int direction); // 1: 向下, -1: 向上
        
        /**
         * 平滑滚动到指定位置
         */
        void onSmoothScrollTo(int targetY);
    }
    
    public ScrollOptimizer() {
        setupSmoothScrollAnimator();
    }
    
    /**
     * 设置监听器
     */
    public void setListener(@Nullable ScrollListener listener) {
        this.listener = listener;
    }
    
    /**
     * 设置预加载启用状态
     */
    public void setPreloadEnabled(boolean enabled) {
        this.preloadEnabled = enabled;
        Log.d(TAG, "Preload " + (enabled ? "enabled" : "disabled"));
    }
    
    /**
     * 处理滚动事件
     */
    public void onScroll(int scrollY, int deltaY) {
        long currentTime = System.currentTimeMillis();
        
        // 更新滚动状态
        boolean wasScrolling = isScrolling;
        isScrolling = true;
        lastScrollTime = currentTime;
        
        // 如果刚开始滚动，通知监听器
        if (!wasScrolling && listener != null) {
            listener.onScrollStarted();
        }
        
        // 更新当前位置
        int oldScrollY = currentScrollY;
        currentScrollY = scrollY;
        
        // 通知滚动变化
        if (listener != null) {
            listener.onScrollChanged(scrollY, deltaY);
        }
        
        // 检查预加载
        checkPreload(scrollY, deltaY);
        
        // 延迟检查滚动结束
        checkScrollEnd();
        
        Log.v(TAG, "Scroll: " + scrollY + ", delta: " + deltaY);
    }
    
    /**
     * 检查预加载需求
     */
    private void checkPreload(int scrollY, int deltaY) {
        if (!preloadEnabled || listener == null) {
            return;
        }
        
        // 计算预加载位置
        int preloadPosition = scrollY + (deltaY > 0 ? PRELOAD_THRESHOLD : -PRELOAD_THRESHOLD);
        
        // 避免重复预加载
        if (Math.abs(preloadPosition - lastPreloadPosition) < PRELOAD_THRESHOLD / 2) {
            return;
        }
        
        lastPreloadPosition = preloadPosition;
        
        // 根据滚动方向请求预加载
        int direction = deltaY > 0 ? 1 : -1;
        listener.onPreloadRequested(direction);
        
        Log.d(TAG, "Preload requested, direction: " + (direction > 0 ? "down" : "up"));
    }
    
    /**
     * 检查滚动是否结束
     */
    private void checkScrollEnd() {
        // 使用Handler延迟检查
        android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
        handler.removeCallbacksAndMessages(null);
        handler.postDelayed(() -> {
            long timeSinceLastScroll = System.currentTimeMillis() - lastScrollTime;
            if (timeSinceLastScroll >= SCROLL_DEBOUNCE_DELAY && isScrolling) {
                isScrolling = false;
                if (listener != null) {
                    listener.onScrollEnded();
                }
                Log.d(TAG, "Scroll ended");
            }
        }, SCROLL_DEBOUNCE_DELAY);
    }
    
    /**
     * 设置平滑滚动动画器
     */
    private void setupSmoothScrollAnimator() {
        smoothScrollAnimator = ValueAnimator.ofInt(0, 0);
        smoothScrollAnimator.setInterpolator(new DecelerateInterpolator());
        
        smoothScrollAnimator.addUpdateListener(animation -> {
            int animatedValue = (int) animation.getAnimatedValue();
            if (listener != null) {
                listener.onSmoothScrollTo(animatedValue);
            }
        });
        
        smoothScrollAnimator.addListener(new android.animation.AnimatorListenerAdapter() {
            @Override
            public void onAnimationStart(android.animation.Animator animation) {
                isSmoothScrolling = true;
            }
            
            @Override
            public void onAnimationEnd(android.animation.Animator animation) {
                isSmoothScrolling = false;
                currentScrollY = targetScrollY;
                Log.d(TAG, "Smooth scroll completed");
            }
            
            @Override
            public void onAnimationCancel(android.animation.Animator animation) {
                isSmoothScrolling = false;
            }
        });
    }
    
    /**
     * 停止所有滚动动画
     */
    public void stopScrolling() {
        if (smoothScrollAnimator != null && smoothScrollAnimator.isRunning()) {
            smoothScrollAnimator.cancel();
        }
        isSmoothScrolling = false;
        isScrolling = false;
        Log.d(TAG, "Scrolling stopped");
    }
    
    /**
     * 清理资源
     */
    public void cleanup() {
        stopScrolling();
        if (smoothScrollAnimator != null) {
            smoothScrollAnimator.removeAllListeners();
            smoothScrollAnimator.removeAllUpdateListeners();
        }
        listener = null;
        Log.d(TAG, "ScrollOptimizer cleaned up");
    }
}
