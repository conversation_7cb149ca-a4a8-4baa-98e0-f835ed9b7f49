package com.lingtxt.editor.ui.settings;

import androidx.annotation.DrawableRes;
import androidx.annotation.StringRes;

/**
 * 设置项数据模型
 */
public class SettingItem {
    
    public enum Type {
        CATEGORY,       // 分类标题
        SWITCH,         // 开关
        SLIDER,         // 滑块
        SELECTION,      // 选择项
        ACTION          // 操作项
    }
    
    private final Type type;
    private final String key;
    private final String title;
    private final String summary;
    private final int iconRes;
    private final Object value;
    private final Object[] options;
    private final float minValue;
    private final float maxValue;
    private final boolean enabled;
    
    private SettingItem(Builder builder) {
        this.type = builder.type;
        this.key = builder.key;
        this.title = builder.title;
        this.summary = builder.summary;
        this.iconRes = builder.iconRes;
        this.value = builder.value;
        this.options = builder.options;
        this.minValue = builder.minValue;
        this.maxValue = builder.maxValue;
        this.enabled = builder.enabled;
    }
    
    public Type getType() {
        return type;
    }
    
    public String getKey() {
        return key;
    }
    
    public String getTitle() {
        return title;
    }
    
    public String getSummary() {
        return summary;
    }
    
    public int getIconRes() {
        return iconRes;
    }
    
    public Object getValue() {
        return value;
    }
    
    public Object[] getOptions() {
        return options;
    }
    
    public float getMinValue() {
        return minValue;
    }
    
    public float getMaxValue() {
        return maxValue;
    }
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public static class Builder {
        private Type type;
        private String key;
        private String title;
        private String summary;
        private int iconRes = 0;
        private Object value;
        private Object[] options;
        private float minValue = 0f;
        private float maxValue = 100f;
        private boolean enabled = true;
        
        public Builder setType(Type type) {
            this.type = type;
            return this;
        }
        
        public Builder setKey(String key) {
            this.key = key;
            return this;
        }
        
        public Builder setTitle(String title) {
            this.title = title;
            return this;
        }
        
        public Builder setSummary(String summary) {
            this.summary = summary;
            return this;
        }
        
        public Builder setIcon(@DrawableRes int iconRes) {
            this.iconRes = iconRes;
            return this;
        }
        
        public Builder setValue(Object value) {
            this.value = value;
            return this;
        }
        
        public Builder setOptions(Object[] options) {
            this.options = options;
            return this;
        }
        
        public Builder setRange(float minValue, float maxValue) {
            this.minValue = minValue;
            this.maxValue = maxValue;
            return this;
        }
        
        public Builder setEnabled(boolean enabled) {
            this.enabled = enabled;
            return this;
        }
        
        public SettingItem build() {
            return new SettingItem(this);
        }
    }
    
    // 便捷创建方法
    public static SettingItem createCategory(String title) {
        return new Builder()
                .setType(Type.CATEGORY)
                .setTitle(title)
                .build();
    }
    
    public static SettingItem createSwitch(String key, String title, String summary, 
                                         @DrawableRes int iconRes, boolean value) {
        return new Builder()
                .setType(Type.SWITCH)
                .setKey(key)
                .setTitle(title)
                .setSummary(summary)
                .setIcon(iconRes)
                .setValue(value)
                .build();
    }
    
    public static SettingItem createSlider(String key, String title, String summary,
                                         @DrawableRes int iconRes, float value, 
                                         float minValue, float maxValue) {
        return new Builder()
                .setType(Type.SLIDER)
                .setKey(key)
                .setTitle(title)
                .setSummary(summary)
                .setIcon(iconRes)
                .setValue(value)
                .setRange(minValue, maxValue)
                .build();
    }
    
    public static SettingItem createSelection(String key, String title, String summary,
                                            @DrawableRes int iconRes, Object value, Object[] options) {
        return new Builder()
                .setType(Type.SELECTION)
                .setKey(key)
                .setTitle(title)
                .setSummary(summary)
                .setIcon(iconRes)
                .setValue(value)
                .setOptions(options)
                .build();
    }
    
    public static SettingItem createAction(String key, String title, String summary,
                                         @DrawableRes int iconRes) {
        return new Builder()
                .setType(Type.ACTION)
                .setKey(key)
                .setTitle(title)
                .setSummary(summary)
                .setIcon(iconRes)
                .build();
    }
}
