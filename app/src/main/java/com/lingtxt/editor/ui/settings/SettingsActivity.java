package com.lingtxt.editor.ui.settings;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.appbar.MaterialToolbar;
import com.lingtxt.editor.LingTxtApplication;
import com.lingtxt.editor.R;
import com.lingtxt.editor.data.model.AppLanguage;
import com.lingtxt.editor.data.model.AppSettings;
import com.lingtxt.editor.data.model.Theme;
import com.lingtxt.editor.data.repository.SettingsRepository;
import com.lingtxt.editor.utils.SettingsChangeManager;
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.disposables.CompositeDisposable;
import io.reactivex.rxjava3.schedulers.Schedulers;
import java.util.ArrayList;
import java.util.List;
import javax.inject.Inject;

/**
 * 设置Activity
 */
public class SettingsActivity extends AppCompatActivity {

    @Inject
    SettingsRepository settingsRepository;

    private MaterialToolbar toolbar;
    private RecyclerView recyclerView;
    private SettingsAdapter adapter;
    private CompositeDisposable disposables = new CompositeDisposable();

    public static Intent createIntent(Context context) {
        return new Intent(context, SettingsActivity.class);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        // 注入依赖
        ((LingTxtApplication) getApplication()).getAppComponent().inject(this);

        // 确保语言设置正确应用
        applyCurrentLanguageSettings();

        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_settings);

        initViews();
        setupToolbar();
        setupRecyclerView();
        loadSettings();
    }

    /**
     * 应用当前的语言设置到SettingsActivity
     */
    private void applyCurrentLanguageSettings() {
        try {
            settingsRepository.getLanguage()
                .subscribe(
                    language -> {
                        java.util.Locale locale;
                        switch (language) {
                            case CHINESE:
                                locale = java.util.Locale.SIMPLIFIED_CHINESE;
                                break;
                            case ENGLISH:
                                locale = java.util.Locale.ENGLISH;
                                break;
                            case SYSTEM:
                            default:
                                locale = java.util.Locale.getDefault();
                                break;
                        }

                        // 更新当前Activity的语言配置
                        android.content.res.Configuration config = new android.content.res.Configuration();
                        config.setLocale(locale);
                        getResources().updateConfiguration(config, getResources().getDisplayMetrics());
                    },
                    throwable -> {
                        // 使用系统默认语言
                    }
                );
        } catch (Exception e) {
            // 使用系统默认语言
        }
    }

    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        recyclerView = findViewById(R.id.recycler_view);
    }

    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle(R.string.settings);
        }
        
        toolbar.setNavigationOnClickListener(v -> finish());
    }

    private void setupRecyclerView() {
        adapter = new SettingsAdapter(this, new SettingsAdapter.OnSettingChangeListener() {
            @Override
            public void onFontSizeChanged(float fontSize) {
                updateFontSize(fontSize);
            }

            @Override
            public void onFontFamilyChanged(String fontFamily) {
                updateFontFamily(fontFamily);
            }

            @Override
            public void onThemeChanged(Theme theme) {
                updateTheme(theme);
            }

            @Override
            public void onLanguageChanged(AppLanguage language) {
                updateLanguage(language);
            }

            @Override
            public void onShowLineNumbersChanged(boolean show) {
                updateShowLineNumbers(show);
            }

            @Override
            public void onShowInvisibleCharsChanged(boolean show) {
                updateShowInvisibleChars(show);
            }

            @Override
            public void onSupportedFileTypesClicked() {
                showSupportedFileTypesDialog();
            }
        });
        
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        recyclerView.setAdapter(adapter);
    }

    private void loadSettings() {
        // 加载字体大小
        disposables.add(
            settingsRepository.getFontSize()
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(
                    fontSize -> adapter.updateFontSize(fontSize),
                    throwable -> { /* 处理错误 */ }
                )
        );

        // 加载字体族
        disposables.add(
            settingsRepository.getFontFamily()
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(
                    fontFamily -> adapter.updateFontFamily(fontFamily),
                    throwable -> { /* 处理错误 */ }
                )
        );

        // 加载主题
        disposables.add(
            settingsRepository.getTheme()
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(
                    theme -> adapter.updateTheme(theme),
                    throwable -> { /* 处理错误 */ }
                )
        );

        // 加载语言
        disposables.add(
            settingsRepository.getLanguage()
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(
                    language -> adapter.updateLanguage(language),
                    throwable -> { /* 处理错误 */ }
                )
        );

        // 加载行号显示设置
        disposables.add(
            settingsRepository.getShowLineNumbers()
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(
                    show -> adapter.updateShowLineNumbers(show),
                    throwable -> { /* 处理错误 */ }
                )
        );

        // 加载不可见字符显示设置
        disposables.add(
            settingsRepository.getShowInvisibleChars()
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(
                    show -> adapter.updateShowInvisibleChars(show),
                    throwable -> { /* 处理错误 */ }
                )
        );
    }

    private void updateFontSize(float fontSize) {
        disposables.add(
            settingsRepository.updateFontSize(fontSize)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(
                    () -> {
                        // 立即应用字体大小变更
                        SettingsChangeManager.getInstance().applyFontSizeChange(fontSize);
                        showMessage("字体大小已更新");
                        setResult(RESULT_OK);
                    },
                    throwable -> showMessage("更新失败: " + throwable.getMessage())
                )
        );
    }

    private void updateFontFamily(String fontFamily) {
        disposables.add(
            settingsRepository.updateFontFamily(fontFamily)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(
                    () -> {
                        // 立即应用字体族变更
                        SettingsChangeManager.getInstance().applyFontFamilyChange(fontFamily);
                        showMessage("字体族已更新");
                        setResult(RESULT_OK);
                    },
                    throwable -> showMessage("更新失败: " + throwable.getMessage())
                )
        );
    }

    private void updateTheme(Theme theme) {
        disposables.add(
            settingsRepository.updateTheme(theme)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(
                    () -> {
                        // 立即应用主题变更
                        applyThemeImmediately(theme);
                        showMessage("主题已更新");
                        setResult(RESULT_OK);
                    },
                    throwable -> showMessage("更新失败: " + throwable.getMessage())
                )
        );
    }

    private void updateLanguage(AppLanguage language) {
        disposables.add(
            settingsRepository.updateLanguage(language)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(
                    () -> {
                        // 立即应用语言变更
                        applyLanguageImmediately(language);
                        showMessage("语言已更新");
                        setResult(RESULT_OK);
                    },
                    throwable -> showMessage("更新失败: " + throwable.getMessage())
                )
        );
    }

    /**
     * 立即应用主题变更
     */
    private void applyThemeImmediately(Theme theme) {
        SettingsChangeManager.getInstance().applyThemeChange(theme);
    }

    /**
     * 立即应用语言变更
     */
    private void applyLanguageImmediately(AppLanguage language) {
        android.util.Log.d("SettingsActivity", "Applying language change immediately: " + language);

        // 先应用语言变更到Application和当前Activity
        SettingsChangeManager.getInstance().applyLanguageChange(this, language);

        // 延迟重新创建Activity，确保语言配置已完全应用
        android.os.Handler mainHandler = new android.os.Handler(android.os.Looper.getMainLooper());
        mainHandler.postDelayed(() -> {
            android.util.Log.d("SettingsActivity", "Recreating SettingsActivity for language change");
            recreate();
        }, 150); // 延迟150ms，确保语言配置和监听器通知都已完成
    }

    private void updateShowLineNumbers(boolean show) {
        disposables.add(
            settingsRepository.updateShowLineNumbers(show)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(
                    () -> {
                        // 立即应用行号显示设置变更
                        SettingsChangeManager.getInstance().applyOtherSettingChange("show_line_numbers", show);
                        showMessage("行号显示设置已更新");
                        setResult(RESULT_OK);
                    },
                    throwable -> showMessage("更新失败: " + throwable.getMessage())
                )
        );
    }

    private void updateShowInvisibleChars(boolean show) {
        disposables.add(
            settingsRepository.updateShowInvisibleChars(show)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(
                    () -> {
                        // 立即应用不可见字符显示设置变更
                        SettingsChangeManager.getInstance().applyOtherSettingChange("show_invisible_chars", show);
                        showMessage("不可见字符显示设置已更新");
                        setResult(RESULT_OK);
                    },
                    throwable -> showMessage("更新失败: " + throwable.getMessage())
                )
        );
    }

    private void showMessage(String message) {
        com.google.android.material.snackbar.Snackbar.make(
            recyclerView,
            message,
            com.google.android.material.snackbar.Snackbar.LENGTH_SHORT
        ).show();
    }

    /**
     * 显示支持的文件类型对话框
     */
    private void showSupportedFileTypesDialog() {
        // 创建文件类型管理对话框
        com.lingtxt.editor.ui.dialog.SupportedFileTypesDialog dialog =
            new com.lingtxt.editor.ui.dialog.SupportedFileTypesDialog(this);
        dialog.show();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        disposables.clear();
    }
}
