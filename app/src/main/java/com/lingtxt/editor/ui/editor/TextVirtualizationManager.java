package com.lingtxt.editor.ui.editor;

import android.text.Layout;
import android.text.SpannableStringBuilder;
import android.util.Log;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 文本虚拟化管理器
 * 负责管理大文件的虚拟化渲染逻辑，只渲染可见区域的文本内容
 */
public class TextVirtualizationManager {
    
    private static final String TAG = "TextVirtualization";
    
    // 虚拟化配置
    private static final int DEFAULT_VISIBLE_LINES = 100;  // 默认可见行数
    private static final int BUFFER_LINES = 20;            // 缓冲行数（上下各20行）
    private static final int MAX_CACHE_SIZE = 500;         // 最大缓存行数
    private static final long LARGE_FILE_THRESHOLD = 1024 * 1024; // 1MB阈值
    
    // 文本数据
    private List<String> allLines;                         // 所有文本行
    private boolean isVirtualizationEnabled = false;      // 是否启用虚拟化
    private long totalFileSize = 0;                        // 文件总大小
    
    // 可见区域管理
    private int visibleStartLine = 0;                      // 可见区域开始行
    private int visibleEndLine = 0;                        // 可见区域结束行
    private int totalLines = 0;                            // 总行数
    
    // 缓存管理 (LRU)
    private final LinkedHashMap<Integer, String> lineCache = new LinkedHashMap<Integer, String>(MAX_CACHE_SIZE, 0.75f, true) {
        @Override
        protected boolean removeEldestEntry(Map.Entry<Integer, String> eldest) {
            return size() > MAX_CACHE_SIZE;
        }
    };
    
    // 监听器
    private VirtualizationListener listener;
    
    /**
     * 虚拟化事件监听器
     */
    public interface VirtualizationListener {
        /**
         * 可见区域变化时调用
         */
        void onVisibleRangeChanged(int startLine, int endLine);
        
        /**
         * 需要更新显示内容时调用
         */
        void onContentUpdateRequired(SpannableStringBuilder visibleContent);
        
        /**
         * 虚拟化状态变化时调用
         */
        void onVirtualizationStateChanged(boolean enabled);
    }
    
    public TextVirtualizationManager() {
        this.allLines = new ArrayList<>();
    }
    
    /**
     * 设置监听器
     */
    public void setListener(@Nullable VirtualizationListener listener) {
        this.listener = listener;
    }
    
    /**
     * 设置文本内容
     */
    public void setText(@NonNull String text, long fileSize) {
        this.totalFileSize = fileSize;
        
        // 分割文本为行
        String[] lines = text.split("\n", -1);
        this.allLines.clear();
        for (String line : lines) {
            this.allLines.add(line);
        }
        this.totalLines = this.allLines.size();
        
        // 判断是否需要启用虚拟化
        boolean shouldVirtualize = shouldEnableVirtualization(fileSize, totalLines);
        setVirtualizationEnabled(shouldVirtualize);
        
        // 清空缓存
        lineCache.clear();
        
        // 初始化可见区域
        updateVisibleRange(0, Math.min(DEFAULT_VISIBLE_LINES, totalLines));
        
        Log.d(TAG, "Text set: " + totalLines + " lines, " + fileSize + " bytes, virtualization: " + isVirtualizationEnabled);
    }
    
    /**
     * 判断是否应该启用虚拟化
     */
    private boolean shouldEnableVirtualization(long fileSize, int lineCount) {
        // 文件大小超过阈值或行数过多时启用虚拟化
        return fileSize > LARGE_FILE_THRESHOLD || lineCount > 1000;
    }
    
    /**
     * 设置虚拟化启用状态
     */
    public void setVirtualizationEnabled(boolean enabled) {
        if (this.isVirtualizationEnabled != enabled) {
            this.isVirtualizationEnabled = enabled;
            if (listener != null) {
                listener.onVirtualizationStateChanged(enabled);
            }
            Log.d(TAG, "Virtualization " + (enabled ? "enabled" : "disabled"));
        }
    }
    
    /**
     * 更新可见区域
     */
    public void updateVisibleRange(int startLine, int endLine) {
        if (!isVirtualizationEnabled) {
            return;
        }
        
        // 确保范围有效
        startLine = Math.max(0, startLine);
        endLine = Math.min(totalLines, endLine);
        
        // 添加缓冲区
        int bufferedStart = Math.max(0, startLine - BUFFER_LINES);
        int bufferedEnd = Math.min(totalLines, endLine + BUFFER_LINES);
        
        if (this.visibleStartLine != bufferedStart || this.visibleEndLine != bufferedEnd) {
            this.visibleStartLine = bufferedStart;
            this.visibleEndLine = bufferedEnd;
            
            if (listener != null) {
                listener.onVisibleRangeChanged(bufferedStart, bufferedEnd);
            }
            
            // 更新显示内容
            updateVisibleContent();
            
            Log.d(TAG, "Visible range updated: " + bufferedStart + " - " + bufferedEnd);
        }
    }
    
    /**
     * 根据滚动位置更新可见区域
     */
    public void updateVisibleRangeByScroll(Layout layout, int scrollY, int viewHeight) {
        if (!isVirtualizationEnabled || layout == null) {
            return;
        }
        
        // 计算可见的行范围
        int firstVisibleLine = layout.getLineForVertical(scrollY);
        int lastVisibleLine = layout.getLineForVertical(scrollY + viewHeight);
        
        // 确保范围有效
        firstVisibleLine = Math.max(0, firstVisibleLine);
        lastVisibleLine = Math.min(layout.getLineCount() - 1, lastVisibleLine);
        
        updateVisibleRange(firstVisibleLine, lastVisibleLine + 1);
    }
    
    /**
     * 更新可见内容
     */
    private void updateVisibleContent() {
        if (!isVirtualizationEnabled || listener == null) {
            return;
        }
        
        SpannableStringBuilder visibleContent = new SpannableStringBuilder();
        
        for (int i = visibleStartLine; i < visibleEndLine && i < totalLines; i++) {
            String line = getLine(i);
            if (line != null) {
                visibleContent.append(line);
                if (i < visibleEndLine - 1 && i < totalLines - 1) {
                    visibleContent.append('\n');
                }
            }
        }
        
        listener.onContentUpdateRequired(visibleContent);
    }
    
    /**
     * 获取指定行的内容（带缓存）
     */
    @Nullable
    private String getLine(int lineNumber) {
        if (lineNumber < 0 || lineNumber >= totalLines) {
            return null;
        }
        
        // 先从缓存中查找
        String cachedLine = lineCache.get(lineNumber);
        if (cachedLine != null) {
            return cachedLine;
        }
        
        // 从原始数据中获取
        String line = allLines.get(lineNumber);
        
        // 添加到缓存
        lineCache.put(lineNumber, line);
        
        return line;
    }
    
    /**
     * 获取完整文本内容（非虚拟化模式）
     */
    @NonNull
    public String getFullText() {
        if (allLines.isEmpty()) {
            return "";
        }
        
        StringBuilder fullText = new StringBuilder();
        for (int i = 0; i < allLines.size(); i++) {
            fullText.append(allLines.get(i));
            if (i < allLines.size() - 1) {
                fullText.append('\n');
            }
        }
        return fullText.toString();
    }
    
    /**
     * 获取当前可见内容
     */
    @NonNull
    public String getVisibleText() {
        if (!isVirtualizationEnabled) {
            return getFullText();
        }
        
        StringBuilder visibleText = new StringBuilder();
        for (int i = visibleStartLine; i < visibleEndLine && i < totalLines; i++) {
            String line = getLine(i);
            if (line != null) {
                visibleText.append(line);
                if (i < visibleEndLine - 1 && i < totalLines - 1) {
                    visibleText.append('\n');
                }
            }
        }
        return visibleText.toString();
    }

    
    /**
     * 清理资源
     */
    public void cleanup() {
        allLines.clear();
        lineCache.clear();
        listener = null;
        Log.d(TAG, "TextVirtualizationManager cleaned up");
    }
}
