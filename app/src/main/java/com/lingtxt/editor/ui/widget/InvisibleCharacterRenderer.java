package com.lingtxt.editor.ui.widget;

import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Typeface;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.style.ReplacementSpan;
import android.widget.TextView;

/**
 * 不可见字符渲染器
 * 显示空格、制表符、换行符等不可见字符
 */
public class InvisibleCharacterRenderer {

    // 不可见字符的显示符号
    private static final String SPACE_SYMBOL = "·";      // 空格符号
    private static final String TAB_SYMBOL = "→";        // 制表符符号
    private static final String NEWLINE_SYMBOL = "¶";    // 换行符符号
    private static final String CR_SYMBOL = "←";         // 回车符号
    
    // 颜色配置
    private int invisibleCharColor = 0x40808080; // 半透明灰色
    
    private boolean showSpaces = true;
    private boolean showTabs = true;
    private boolean showNewlines = true;
    private boolean showCarriageReturns = true;

    /**
     * 渲染不可见字符到TextView
     */
    public void renderInvisibleCharacters(TextView textView, String originalText) {
        if (textView == null || originalText == null) return;
        
        SpannableStringBuilder builder = new SpannableStringBuilder(originalText);
        
        // 处理各种不可见字符
        if (showSpaces) {
            replaceInvisibleChar(builder, ' ', SPACE_SYMBOL);
        }
        
        if (showTabs) {
            replaceInvisibleChar(builder, '\t', TAB_SYMBOL);
        }
        
        if (showNewlines) {
            replaceInvisibleChar(builder, '\n', NEWLINE_SYMBOL);
        }
        
        if (showCarriageReturns) {
            replaceInvisibleChar(builder, '\r', CR_SYMBOL);
        }
        
        textView.setText(builder);
    }

    /**
     * 替换不可见字符为可见符号
     */
    private void replaceInvisibleChar(SpannableStringBuilder builder, char invisibleChar, String symbol) {
        String text = builder.toString();
        int index = 0;
        
        while ((index = text.indexOf(invisibleChar, index)) != -1) {
            // 创建自定义Span来显示不可见字符
            InvisibleCharSpan span = new InvisibleCharSpan(symbol, invisibleCharColor);
            builder.setSpan(span, index, index + 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            index++;
        }
    }

    /**
     * 设置是否显示空格
     */
    public void setShowSpaces(boolean show) {
        this.showSpaces = show;
    }

    /**
     * 设置是否显示制表符
     */
    public void setShowTabs(boolean show) {
        this.showTabs = show;
    }

    /**
     * 设置是否显示换行符
     */
    public void setShowNewlines(boolean show) {
        this.showNewlines = show;
    }

    /**
     * 设置是否显示回车符
     */
    public void setShowCarriageReturns(boolean show) {
        this.showCarriageReturns = show;
    }

    /**
     * 设置不可见字符颜色
     */
    public void setInvisibleCharColor(int color) {
        this.invisibleCharColor = color;
    }

    /**
     * 检查是否显示空格
     */
    public boolean isShowSpaces() {
        return showSpaces;
    }

    /**
     * 检查是否显示制表符
     */
    public boolean isShowTabs() {
        return showTabs;
    }

    /**
     * 检查是否显示换行符
     */
    public boolean isShowNewlines() {
        return showNewlines;
    }

    /**
     * 检查是否显示回车符
     */
    public boolean isShowCarriageReturns() {
        return showCarriageReturns;
    }

    /**
     * 自定义Span用于渲染不可见字符
     */
    private static class InvisibleCharSpan extends ReplacementSpan {
        
        private final String symbol;
        private final int color;
        private final Paint paint;

        public InvisibleCharSpan(String symbol, int color) {
            this.symbol = symbol;
            this.color = color;
            this.paint = new Paint(Paint.ANTI_ALIAS_FLAG);
            this.paint.setColor(color);
            this.paint.setTypeface(Typeface.MONOSPACE);
        }

        @Override
        public int getSize(Paint paint, CharSequence text, int start, int end, Paint.FontMetricsInt fm) {
            // 返回符号的宽度
            this.paint.setTextSize(paint.getTextSize() * 0.8f); // 稍小一些
            return (int) this.paint.measureText(symbol);
        }

        @Override
        public void draw(Canvas canvas, CharSequence text, int start, int end, 
                        float x, int top, int y, int bottom, Paint paint) {
            // 绘制不可见字符符号
            this.paint.setTextSize(paint.getTextSize() * 0.8f);
            
            // 计算垂直居中位置
            Paint.FontMetrics fm = this.paint.getFontMetrics();
            float textHeight = fm.descent - fm.ascent;
            float textY = y - (textHeight / 2) + (bottom - top) / 2;
            
            canvas.drawText(symbol, x, textY, this.paint);
        }
    }

    /**
     * 创建不可见字符显示的配置
     */
    public static class Config {
        public boolean showSpaces = true;
        public boolean showTabs = true;
        public boolean showNewlines = false; // 换行符通常不需要显示
        public boolean showCarriageReturns = false; // 回车符通常不需要显示
        public int color = 0x40808080; // 半透明灰色
        
        public Config showSpaces(boolean show) {
            this.showSpaces = show;
            return this;
        }
        
        public Config showTabs(boolean show) {
            this.showTabs = show;
            return this;
        }
        
        public Config showNewlines(boolean show) {
            this.showNewlines = show;
            return this;
        }
        
        public Config showCarriageReturns(boolean show) {
            this.showCarriageReturns = show;
            return this;
        }
        
        public Config color(int color) {
            this.color = color;
            return this;
        }
    }

    /**
     * 使用配置创建渲染器
     */
    public static InvisibleCharacterRenderer create(Config config) {
        InvisibleCharacterRenderer renderer = new InvisibleCharacterRenderer();
        renderer.setShowSpaces(config.showSpaces);
        renderer.setShowTabs(config.showTabs);
        renderer.setShowNewlines(config.showNewlines);
        renderer.setShowCarriageReturns(config.showCarriageReturns);
        renderer.setInvisibleCharColor(config.color);
        return renderer;
    }

    /**
     * 创建默认配置的渲染器
     */
    public static InvisibleCharacterRenderer createDefault() {
        return create(new Config());
    }
}