package com.lingtxt.editor.ui.widget;

import android.app.Activity;
import android.os.Handler;
import android.os.Looper;
import android.view.View;
import android.view.ViewGroup;
import java.util.ArrayList;
import java.util.List;

/**
 * 专注模式管理器
 * 管理沉浸式阅读的状态和UI隐藏/显示
 */
public class FocusModeManager {

    public interface OnFocusModeChangeListener {
        void onEnterFocusMode();
        void onExitFocusMode();
        void onUserInteraction();
    }

    private final Activity activity;
    private final ImmersiveReadingManager immersiveManager;
    private final ReadingProgressIndicator progressIndicator;
    
    private OnFocusModeChangeListener listener;
    private boolean isFocusMode = false;
    private boolean isEnabled = true;

    public FocusModeManager(Activity activity) {
        this.activity = activity;
        this.immersiveManager = new ImmersiveReadingManager();
        this.progressIndicator = new ReadingProgressIndicator(activity);
        
        setupImmersiveManager();
        setupProgressIndicator();
    }

    private void setupImmersiveManager() {
        immersiveManager.setOnImmersiveModeChangeListener(new ImmersiveReadingManager.OnImmersiveModeChangeListener() {
            @Override
            public void onEnterImmersiveMode() {
                isFocusMode = true;
                if (listener != null) {
                    listener.onEnterFocusMode();
                }
            }

            @Override
            public void onExitImmersiveMode() {
                isFocusMode = false;
                if (listener != null) {
                    listener.onExitFocusMode();
                }
            }
        });
    }

    private void setupProgressIndicator() {
        // 配置进度指示器
        progressIndicator.setProgressVisible(true);
        
        // 将进度指示器添加到沉浸式管理器的可隐藏视图中
        immersiveManager.addHidableView(progressIndicator);
    }

    /**
     * 添加可隐藏的视图
     */
    public void addHidableView(View view) {
        immersiveManager.addHidableView(view);
    }

    /**
     * 移除可隐藏的视图
     */
    public void removeHidableView(View view) {
        immersiveManager.removeHidableView(view);
    }

    /**
     * 设置目标编辑器，用于进度计算
     */
    public void setTargetEditor(View editorView) {
        if (editorView == null) return;

        // 监听编辑器滚动事件
        if (editorView instanceof android.widget.TextView) {
            android.widget.TextView textView = (android.widget.TextView) editorView;

            // 添加滚动监听器
            textView.getViewTreeObserver().addOnScrollChangedListener(() -> {
                // 在主线程中更新进度
                if (activity != null && !activity.isFinishing()) {
                    progressIndicator.updateProgressFromEditor(textView);
                }
            });

            // 初始化进度
            progressIndicator.updateProgressFromEditor(textView);
        }
    }

    /**
     * 处理用户交互
     */
    public void onUserInteraction() {
        if (!isEnabled) return;
        
        immersiveManager.onUserInteraction();
        
        if (listener != null) {
            listener.onUserInteraction();
        }
    }

    /**
     * 处理触摸事件
     */
    public boolean onTouchEvent(android.view.MotionEvent event, View parentView) {
        if (!isEnabled) return false;
        
        return immersiveManager.onTouchEvent(event, parentView);
    }

    /**
     * 手动进入专注模式
     */
    public void enterFocusMode() {
        if (isEnabled) {
            immersiveManager.enterImmersiveMode();
        }
    }

    /**
     * 手动退出专注模式
     */
    public void exitFocusMode() {
        immersiveManager.exitImmersiveMode();
    }

    /**
     * 检查是否在专注模式
     */
    public boolean isFocusMode() {
        return isFocusMode;
    }

    /**
     * 启用或禁用专注模式
     */
    public void setEnabled(boolean enabled) {
        this.isEnabled = enabled;
        
        if (!enabled && isFocusMode) {
            // 如果禁用专注模式且当前在专注模式，强制退出
            immersiveManager.forceExitImmersiveMode();
        }
    }

    /**
     * 检查专注模式是否启用
     */
    public boolean isEnabled() {
        return isEnabled;
    }

    /**
     * 设置专注模式变化监听器
     */
    public void setOnFocusModeChangeListener(OnFocusModeChangeListener listener) {
        this.listener = listener;
    }

    /**
     * 获取阅读进度指示器
     */
    public ReadingProgressIndicator getProgressIndicator() {
        return progressIndicator;
    }

    /**
     * 将进度指示器添加到父视图
     */
    public void attachProgressIndicatorToParent(ViewGroup parent) {
        if (parent != null && progressIndicator.getParent() == null) {
            android.widget.FrameLayout.LayoutParams params = new android.widget.FrameLayout.LayoutParams(
                android.widget.FrameLayout.LayoutParams.MATCH_PARENT,
                android.widget.FrameLayout.LayoutParams.MATCH_PARENT
            );
            parent.addView(progressIndicator, params);
        }
    }

    /**
     * 启动自动隐藏计时器
     */
    public void startAutoHideTimer() {
        if (isEnabled) {
            immersiveManager.startAutoHideTimer();
        }
    }

    /**
     * 停止自动隐藏计时器
     */
    public void stopAutoHideTimer() {
        immersiveManager.stopAutoHideTimer();
    }

    /**
     * 清理资源
     */
    public void destroy() {
        immersiveManager.destroy();
        
        if (progressIndicator.getParent() instanceof ViewGroup) {
            ((ViewGroup) progressIndicator.getParent()).removeView(progressIndicator);
        }
    }
}