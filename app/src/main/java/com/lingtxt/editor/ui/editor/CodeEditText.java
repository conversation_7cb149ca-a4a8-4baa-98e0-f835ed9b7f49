package com.lingtxt.editor.ui.editor;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.Typeface;
import android.text.Editable;
import android.text.Layout;
import android.text.SpannableStringBuilder;
import android.text.TextWatcher;
import android.util.AttributeSet;
import android.util.Log;
import android.util.TypedValue;
import android.view.HapticFeedbackConstants;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.ScaleGestureDetector;
import android.view.inputmethod.InputMethodManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatEditText;
import com.lingtxt.editor.syntax.LanguageDetector;
import com.lingtxt.editor.syntax.SyntaxHighlightManager;
import com.lingtxt.editor.ui.widget.CursorMagnifier;

/**
 * 自定义代码编辑器组件
 * 支持行号显示、语法高亮、自动缩进等功能
 */
public class CodeEditText extends AppCompatEditText {

    private Paint lineNumberPaint;
    private Paint lineHighlightPaint;
    private Rect textBounds;
    
    // 配置选项
    private boolean showLineNumbers = true;
    private boolean highlightCurrentLine = true;
    private boolean enableAutoIndent = true;
    private int tabSize = 4;
    private boolean useSpacesForTabs = true;
    
    // 行号相关
    private int lineNumberWidth = 0;
    private int lineNumberPadding = 12;
    private int currentLine = 1;
    
    // 颜色配置
    private int lineNumberColor;
    private int lineHighlightColor;
    private int lineNumberBackgroundColor;
    
    // 语法高亮
    private SyntaxHighlightManager syntaxHighlightManager;
    private String currentFileName;
    
    // 双模式支持
    public enum ViewMode {
        VIEW_ONLY,    // 查看模式：只读，不显示键盘
        EDIT_MODE     // 编辑模式：可编辑，显示键盘
    }
    
    private ViewMode currentMode = ViewMode.VIEW_ONLY;
    private OnModeChangeListener modeChangeListener;
    
    // 沉浸式阅读功能
    private com.lingtxt.editor.ui.widget.InvisibleCharacterRenderer invisibleCharRenderer;
    private boolean showInvisibleChars = false;
    private com.lingtxt.editor.ui.widget.FocusModeManager focusModeManager;
    
    // 手势检测
    private ScaleGestureDetector scaleGestureDetector;
    private float baseFontSize = 12f;
    private float currentFontSize = 12f;
    private static final float MIN_FONT_SIZE = 8f;
    private static final float MAX_FONT_SIZE = 32f;
    
    // 磁吸效果
    private boolean enableMagneticSnap = true;
    private static final int MAGNETIC_SNAP_THRESHOLD = 50; // 磁吸阈值（像素）
    
    // 放大镜功能
    private CursorMagnifier cursorMagnifier;
    private boolean enableCursorMagnifier = true;

    // 大文件优化组件
    private TextVirtualizationManager virtualizationManager;
    private LazyHighlightManager lazyHighlightManager;
    private MemoryManager memoryManager;
    private ScrollOptimizer scrollOptimizer;
    private LargeFileStrategy.FileInfo currentFileInfo;
    private boolean isLargeFileMode = false;

    public boolean isLargeFileMode(){
        return isLargeFileMode;
    }

    // 边缘滑动检测已移除（与系统手势冲突）
    
    public CodeEditText(@NonNull Context context) {
        super(context);
        init();
    }

    public CodeEditText(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public CodeEditText(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        // 初始化主题颜色
        initThemeColors();

        // 初始化画笔
        lineNumberPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        lineNumberPaint.setColor(lineNumberColor);
        lineNumberPaint.setTextSize(getTextSize() * 0.85f);
        lineNumberPaint.setTextAlign(Paint.Align.RIGHT);

        lineHighlightPaint = new Paint();
        lineHighlightPaint.setColor(lineHighlightColor);

        textBounds = new Rect();
        
        // 设置字体为等宽字体
        setTypeface(Typeface.MONOSPACE);

        // 设置光标颜色为鲜活色
        setupCursorColor();

        // 初始化语法高亮管理器
        syntaxHighlightManager = new SyntaxHighlightManager(this);
        
        // 初始化放大镜
        setupCursorMagnifier();
        
        // 边缘滑动检测器已移除（与系统手势冲突）
        
        // 初始化手势检测器
        setupGestureDetectors();
        
        // 添加文本变化监听器
        addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                updateLineNumbers();
            }

            @Override
            public void afterTextChanged(Editable s) {
                if (enableAutoIndent && s.length() > 0) {
                    handleAutoIndent(s);
                }
            }
        });
        
        // 设置选择变化监听器
        setOnSelectionChangedListener((selStart, selEnd) -> {
            updateCurrentLine();
            invalidate();
        });
        
        updateLineNumbers();
        
        // 初始化沉浸式阅读功能
        setupImmersiveReading();

        // 初始化大文件优化组件
        setupLargeFileOptimization();

        // 初始化为查看模式
        setViewMode(ViewMode.VIEW_ONLY);
    }

    /**
     * 设置光标颜色（选择句柄颜色通过主题设置）
     */
    private void setupCursorColor() {
        try {
            // 获取主题的主色调作为光标颜色
            int primaryColor = getPrimaryColor();

            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.Q) {
                // API 29+ 可以直接设置光标颜色
                setTextCursorDrawable(createCursorDrawable(primaryColor));
            } else {
                // 低版本通过反射设置光标颜色
                setCursorColorReflection(primaryColor);
            }

            // 选择句柄颜色通过主题中的 colorControlActivated 自动设置

        } catch (Exception e) {
            // 如果设置失败，使用默认颜色
            android.util.Log.w("CodeEditText", "Failed to set cursor color", e);
        }
    }

    /**
     * 获取主题的主色调
     */
    private int getPrimaryColor() {
        Context context = getContext();
        TypedValue typedValue = new TypedValue();
        android.content.res.Resources.Theme theme = context.getTheme();

        // 尝试获取 colorPrimary
        if (theme.resolveAttribute(com.google.android.material.R.attr.colorPrimary, typedValue, true)) {
            return typedValue.data;
        }

        // 回退到默认的主色调
        return 0xFF6750A4; // Material Design 3 默认主色调
    }

    /**
     * 创建光标drawable
     */
    private android.graphics.drawable.Drawable createCursorDrawable(int color) {
        android.graphics.drawable.GradientDrawable drawable = new android.graphics.drawable.GradientDrawable();
        drawable.setShape(android.graphics.drawable.GradientDrawable.RECTANGLE);
        drawable.setSize(dpToPx(2), dpToPx(20)); // 2dp宽，20dp高
        drawable.setColor(color);
        drawable.setCornerRadius(dpToPx(1)); // 1dp圆角
        return drawable;
    }



    /**
     * 通过反射设置光标颜色（低版本兼容）
     */
    private void setCursorColorReflection(int color) {
        try {
            // 创建光标drawable
            android.graphics.drawable.Drawable cursorDrawable = createCursorDrawable(color);

            // 通过反射设置光标drawable
            java.lang.reflect.Field field = android.widget.TextView.class.getDeclaredField("mCursorDrawableRes");
            field.setAccessible(true);
            field.set(this, 0); // 清除默认资源

            // 设置自定义drawable
            java.lang.reflect.Field drawableField = android.widget.TextView.class.getDeclaredField("mCursorDrawable");
            drawableField.setAccessible(true);
            drawableField.set(this, new android.graphics.drawable.Drawable[]{cursorDrawable, cursorDrawable});
        } catch (Exception e) {
            // 反射失败，忽略
            android.util.Log.w("CodeEditText", "Reflection failed for cursor color", e);
        }
    }



    /**
     * dp转px
     */
    private int dpToPx(int dp) {
        return (int) (dp * getContext().getResources().getDisplayMetrics().density);
    }

    /**
     * 初始化主题颜色
     */
    private void initThemeColors() {
        Context context = getContext();
        TypedValue typedValue = new TypedValue();
        android.content.res.Resources.Theme theme = context.getTheme();

        // 获取主题颜色
        // 行号颜色 - 使用 colorOnSurfaceVariant
        if (theme.resolveAttribute(com.google.android.material.R.attr.colorOnSurfaceVariant, typedValue, true)) {
            lineNumberColor = typedValue.data;
        } else {
            // 回退颜色
            lineNumberColor = 0xFF666666;
        }

        // 行高亮颜色 - 使用 colorSurfaceVariant 的半透明版本
        if (theme.resolveAttribute(com.google.android.material.R.attr.colorSurfaceVariant, typedValue, true)) {
            int surfaceVariant = typedValue.data;
            lineHighlightColor = (surfaceVariant & 0x00FFFFFF) | 0x1A000000; // 10% 透明度
        } else {
            // 回退颜色
            lineHighlightColor = 0x1A000000;
        }

        // 行号背景色 - 使用 colorSurface
        if (theme.resolveAttribute(com.google.android.material.R.attr.colorSurface, typedValue, true)) {
            lineNumberBackgroundColor = typedValue.data;
        } else {
            // 回退颜色
            lineNumberBackgroundColor = 0xFFF5F5F5;
        }
    }

    /**
     * 更新主题颜色（当主题变更时调用）
     */
    public void updateThemeColors() {
        initThemeColors();

        // 更新画笔颜色
        if (lineNumberPaint != null) {
            lineNumberPaint.setColor(lineNumberColor);
        }
        if (lineHighlightPaint != null) {
            lineHighlightPaint.setColor(lineHighlightColor);
        }

        // 重新绘制
        invalidate();
    }

    /**
     * 初始化沉浸式阅读功能
     */
    private void setupImmersiveReading() {
        // 初始化不可见字符渲染器
        invisibleCharRenderer = com.lingtxt.editor.ui.widget.InvisibleCharacterRenderer.createDefault();

        // 注意：FocusModeManager需要Activity上下文，在Activity中初始化
    }

    /**
     * 初始化大文件优化组件
     */
    private void setupLargeFileOptimization() {
        // 初始化虚拟化管理器
        virtualizationManager = new TextVirtualizationManager();
        virtualizationManager.setListener(new TextVirtualizationManager.VirtualizationListener() {
            @Override
            public void onVisibleRangeChanged(int startLine, int endLine) {
                // 可见区域变化时，请求语法高亮
                if (lazyHighlightManager != null && isLargeFileMode) {
                    requestLazyHighlight(startLine, endLine);
                }
            }

            @Override
            public void onContentUpdateRequired(SpannableStringBuilder visibleContent) {
                // 更新显示内容（在虚拟化模式下）
                if (isLargeFileMode) {
                    post(() -> updateVirtualizedContent(visibleContent));
                }
            }

            @Override
            public void onVirtualizationStateChanged(boolean enabled) {
                isLargeFileMode = enabled;
                Log.d("CodeEditText", "Large file mode: " + enabled);
            }
        });

        // 初始化懒加载语法高亮管理器
        if (syntaxHighlightManager != null && syntaxHighlightManager.getSyntaxHighlighter() != null) {
            lazyHighlightManager = new LazyHighlightManager(syntaxHighlightManager.getSyntaxHighlighter());
            lazyHighlightManager.setListener(new LazyHighlightManager.HighlightListener() {
                @Override
                public void onHighlightCompleted(int startLine, int endLine, SpannableStringBuilder highlightedText) {
                    // 高亮完成后更新显示
                    post(() -> applyHighlightedContent(startLine, endLine, highlightedText));
                }

                @Override
                public void onHighlightProgress(int completedChunks, int totalChunks) {
                    // 可以在这里显示进度
                }
            });
        }

        // 初始化内存管理器
        memoryManager = new MemoryManager(getContext());
        memoryManager.addMemoryListener(new MemoryManager.MemoryListener() {
            @Override
            public void onMemoryPressureChanged(MemoryManager.MemoryPressure pressure) {
                // 根据内存压力调整策略
                adjustPerformanceStrategy(pressure);
            }

            @Override
            public void onMemoryReleaseRequested(int targetReleasePercent) {
                // 释放内存
                releaseMemory(targetReleasePercent);
            }
        });

        // 初始化滚动优化器
        scrollOptimizer = new ScrollOptimizer();
        scrollOptimizer.setListener(new ScrollOptimizer.ScrollListener() {
            @Override
            public void onScrollChanged(int scrollY, int deltaY) {
                // 滚动时更新虚拟化区域
                if (isLargeFileMode && virtualizationManager != null) {
                    updateVirtualizedRange(scrollY);
                }
            }

            @Override
            public void onScrollStarted() {
                // 滚动开始时暂停一些操作
            }

            @Override
            public void onScrollEnded() {
                // 滚动结束时恢复操作
            }

            @Override
            public void onPreloadRequested(int direction) {
                // 预加载内容
                preloadContent(direction);
            }

            @Override
            public void onSmoothScrollTo(int targetY) {
                // 执行平滑滚动
                scrollTo(0, targetY);
            }
        });
    }

    @Override
    protected void onDraw(@NonNull Canvas canvas) {
        if (showLineNumbers) {
            drawLineNumbers(canvas);
        }

        if (highlightCurrentLine) {
            drawCurrentLineHighlight(canvas);
        }

        super.onDraw(canvas);
    }

    @Override
    protected void onScrollChanged(int horiz, int vert, int oldHoriz, int oldVert) {
        super.onScrollChanged(horiz, vert, oldHoriz, oldVert);

        // 通知滚动优化器
        if (scrollOptimizer != null) {
            int deltaY = vert - oldVert;
            scrollOptimizer.onScroll(vert, deltaY);
        }
    }

    /**
     * 绘制行号
     */
    private void drawLineNumbers(Canvas canvas) {
        Layout layout = getLayout();
        if (layout == null) return;
        
        int lineCount = getLineCount();
        if (lineCount <= 0) return;
        
        // 计算真实的文本行数（基于换行符）
        String text = getText() != null ? getText().toString() : "";
        int realLineCount = calculateTotalRealLines(text);
        
        // 根据真实行数计算行号宽度
        String maxLineNumber = String.valueOf(realLineCount);
        lineNumberPaint.getTextBounds(maxLineNumber, 0, maxLineNumber.length(), textBounds);
        int newLineNumberWidth = textBounds.width() + lineNumberPadding * 2;
        
        // 如果行号宽度变化，更新padding
        if (newLineNumberWidth != lineNumberWidth) {
            lineNumberWidth = newLineNumberWidth;
            updatePadding();
        }
        
        // 绘制行号背景 - 修复：背景应该跟随滚动
        int scrollY = getScrollY();
        canvas.drawRect(0, scrollY, lineNumberWidth, scrollY + getHeight(), 
                       createBackgroundPaint());
        
        // 计算可见行范围 - 使用安全的方式计算
        int firstVisibleLine = Math.max(0, layout.getLineForVertical(scrollY));
        int lastVisibleLine = Math.min(lineCount - 1, layout.getLineForVertical(scrollY + getHeight()));
        
        // 绘制行号文本 - 只为真实的文本行显示行号
        int realLineNumber = 1;
        
        for (int i = firstVisibleLine; i <= lastVisibleLine; i++) {
            if (i < 0 || i >= lineCount) continue;
            
            // 检查这是否是真实的文本行（不是因为屏幕宽度不够而换行的）
            boolean isRealLine = isRealTextLine(layout, text, i);
            
            if (isRealLine) {
                // 计算真实的行号
                realLineNumber = calculateRealLineNumber(text, layout.getLineStart(i));
                
                int lineBaseline = layout.getLineBaseline(i);
                canvas.drawText(String.valueOf(realLineNumber), 
                              lineNumberWidth - lineNumberPadding, 
                              lineBaseline, 
                              lineNumberPaint);
            }
        }
    }



    /**
     * 绘制当前行高亮
     */
    private void drawCurrentLineHighlight(Canvas canvas) {
        Layout layout = getLayout();
        if (layout == null) return;
        
        int selectionStart = getSelectionStart();
        if (selectionStart >= 0 && selectionStart < getText().length()) {
            int line = layout.getLineForOffset(selectionStart);
            if (line >= 0 && line < getLineCount()) {
                int lineTop = layout.getLineTop(line);
                int lineBottom = layout.getLineBottom(line);
                
                float left = showLineNumbers ? lineNumberWidth : 0;
                canvas.drawRect(left, lineTop - getScrollY(), 
                              getWidth(), lineBottom - getScrollY(), 
                              lineHighlightPaint);
            }
        }
    }

    /**
     * 创建行号背景画笔
     */
    private Paint createBackgroundPaint() {
        Paint paint = new Paint();
        paint.setColor(lineNumberBackgroundColor);
        return paint;
    }

    /**
     * 更新行号显示
     */
    private void updateLineNumbers() {
        // 根据真实行数计算初始宽度
        if (lineNumberWidth == 0) {
            String text = getText() != null ? getText().toString() : "";
            int realLineCount = Math.max(calculateTotalRealLines(text), 1);
            String maxLineNumber = String.valueOf(realLineCount);
            lineNumberPaint.getTextBounds(maxLineNumber, 0, maxLineNumber.length(), textBounds);
            lineNumberWidth = textBounds.width() + lineNumberPadding * 2;
        }
        
        updatePadding();
        invalidate();
    }

    /**
     * 更新padding - 确保行号和文本对齐
     */
    private void updatePadding() {
        if (showLineNumbers) {
            // 左边距为行号宽度，顶部和底部padding为0确保对齐
            int leftPadding = lineNumberWidth + 12; // 减少间距，更紧凑
            setPadding(leftPadding, 0, 6, 0); // 顶部padding为0，右边距也减少
        } else {
            setPadding(12, 0, 6, 0); // 减少padding，更紧凑
        }
    }

    /**
     * 更新当前行
     */
    private void updateCurrentLine() {
        android.text.Layout layout = getLayout();
        if (layout == null) return;
        
        int selectionStart = getSelectionStart();
        if (selectionStart >= 0 && selectionStart < getText().length()) {
            // 计算真实的行号（基于换行符）
            String text = getText() != null ? getText().toString() : "";
            currentLine = calculateRealLineNumber(text, selectionStart);
        }
    }

    /**
     * 处理自动缩进
     */
    private void handleAutoIndent(Editable s) {
        int selectionStart = getSelectionStart();
        if (selectionStart > 0 && s.charAt(selectionStart - 1) == '\n') {
            // 获取上一行的缩进
            String indent = getPreviousLineIndent(s, selectionStart - 1);
            if (!indent.isEmpty()) {
                s.insert(selectionStart, indent);
            }
        }
    }

    /**
     * 获取上一行的缩进
     */
    private String getPreviousLineIndent(Editable s, int position) {
        // 找到上一行的开始
        int lineStart = position;
        while (lineStart > 0 && s.charAt(lineStart - 1) != '\n') {
            lineStart--;
        }
        
        // 计算缩进
        StringBuilder indent = new StringBuilder();
        for (int i = lineStart; i < s.length() && i < position; i++) {
            char c = s.charAt(i);
            if (c == ' ' || c == '\t') {
                indent.append(c);
            } else {
                break;
            }
        }
        
        return indent.toString();
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_TAB) {
            insertTab();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    /**
     * 插入Tab字符或空格
     */
    private void insertTab() {
        int selectionStart = getSelectionStart();
        int selectionEnd = getSelectionEnd();
        
        String tabString = useSpacesForTabs ? 
                          generateSpaces(tabSize) : "\t";
        
        Editable editable = getText();
        if (editable != null) {
            editable.replace(selectionStart, selectionEnd, tabString);
        }
    }

    /**
     * 生成指定数量的空格
     */
    private String generateSpaces(int count) {
        StringBuilder spaces = new StringBuilder();
        for (int i = 0; i < count; i++) {
            spaces.append(' ');
        }
        return spaces.toString();
    }

    // Getter和Setter方法
    public boolean isShowLineNumbers() {
        return showLineNumbers;
    }

    public void setShowLineNumbers(boolean showLineNumbers) {
        this.showLineNumbers = showLineNumbers;
        updateLineNumbers();
    }

    public void setHighlightCurrentLine(boolean highlightCurrentLine) {
        this.highlightCurrentLine = highlightCurrentLine;
        invalidate();
    }

    public int getCurrentLine() {
        return currentLine;
    }

    /**
     * 跳转到指定行（真实行号）
     */
    public void gotoLine(int lineNumber) {
        android.text.Layout layout = getLayout();
        if (layout == null) return;
        
        String text = getText() != null ? getText().toString() : "";
        if (text.isEmpty()) return;
        
        // 找到真实行号对应的文本偏移
        int offset = findOffsetForRealLine(text, lineNumber);
        if (offset >= 0) {
            setSelection(offset);
            
            // 滚动到该行
            int visualLine = layout.getLineForOffset(offset);
            int lineTop = layout.getLineTop(visualLine);
            scrollTo(0, Math.max(0, lineTop - getHeight() / 2));
        }
    }

    /**
     * 找到真实行号对应的文本偏移
     */
    private int findOffsetForRealLine(String text, int targetLineNumber) {
        if (targetLineNumber <= 1) return 0;
        
        int currentLine = 1;
        for (int i = 0; i < text.length(); i++) {
            if (text.charAt(i) == '\n') {
                currentLine++;
                if (currentLine == targetLineNumber) {
                    return Math.min(i + 1, text.length());
                }
            }
        }
        
        // 如果没找到，返回文本末尾
        return text.length();
    }

    /**
     * 计算文本的真实行数（基于换行符）
     */
    private int calculateTotalRealLines(String text) {
        if (text == null || text.isEmpty()) return 1;
        
        int lineCount = 1;
        for (int i = 0; i < text.length(); i++) {
            if (text.charAt(i) == '\n') {
                lineCount++;
            }
        }
        return lineCount;
    }

    /**
     * 判断是否是真实的文本行（不是因为屏幕宽度不够而换行的）
     */
    private boolean isRealTextLine(android.text.Layout layout, String text, int lineIndex) {
        if (lineIndex == 0) return true; // 第一行总是真实行
        
        int lineStart = layout.getLineStart(lineIndex);
        if (lineStart == 0) return true;
        
        // 检查前一个字符是否是换行符
        char prevChar = text.charAt(lineStart - 1);
        return prevChar == '\n' || prevChar == '\r';
    }

    /**
     * 计算真实的行号（基于换行符的行数）
     */
    private int calculateRealLineNumber(String text, int offset) {
        int lineNumber = 1;
        for (int i = 0; i < offset && i < text.length(); i++) {
            if (text.charAt(i) == '\n') {
                lineNumber++;
            }
        }
        return lineNumber;
    }

    /**
     * 初始化光标放大镜
     */
    private void setupCursorMagnifier() {
        cursorMagnifier = new CursorMagnifier(getContext());
        cursorMagnifier.setTargetEditText(this);
        
        // 设置放大镜的初始配置
        cursorMagnifier.setMagnificationScale(1.5f);
        cursorMagnifier.setAutoHideDelay(2000);
    }

    // 边缘滑动检测器已移除（与系统手势冲突）

    /**
     * 设置手势检测器
     */
    private void setupGestureDetectors() {
        // 缩放手势检测器
        scaleGestureDetector = new ScaleGestureDetector(getContext(), new ScaleGestureDetector.SimpleOnScaleGestureListener() {
            @Override
            public boolean onScale(ScaleGestureDetector detector) {
                float scaleFactor = detector.getScaleFactor();
                float newFontSize = currentFontSize * scaleFactor;
                
                // 限制字体大小范围
                newFontSize = Math.max(MIN_FONT_SIZE, Math.min(newFontSize, MAX_FONT_SIZE));
                
                if (newFontSize != currentFontSize) {
                    setFontSize(newFontSize);
                }
                
                return true;
            }
            
            @Override
            public boolean onScaleBegin(ScaleGestureDetector detector) {
                return true;
            }
            
            @Override
            public void onScaleEnd(ScaleGestureDetector detector) {
                // 缩放结束后确保所有元素都正确更新
                post(() -> {
                    updateLineNumbers();
                    requestLayout();
                    invalidate();
                });
            }
        });
    }

    /**
     * 设置字体大小
     */
    public void setFontSize(float fontSize) {
        currentFontSize = Math.max(MIN_FONT_SIZE, Math.min(fontSize, MAX_FONT_SIZE));
        setTextSize(TypedValue.COMPLEX_UNIT_SP, currentFontSize);
        
        // 更新行号画笔的字体大小 - 确保与主文本成比例
        if (lineNumberPaint != null) {
            lineNumberPaint.setTextSize(currentFontSize * getResources().getDisplayMetrics().scaledDensity * 0.85f);
        }
        
        // 重新计算行号宽度 - 强制重新计算
        lineNumberWidth = 0;
        
        // 延迟更新，确保文本大小变化已生效
        post(() -> {
            updateLineNumbers();
            // 强制重新布局和重绘，确保所有元素同步更新
            requestLayout();
            invalidate();
        });
        
        // 通知字体大小变化
        if (onFontSizeChangeListener != null) {
            onFontSizeChangeListener.onFontSizeChanged(currentFontSize);
        }
    }

    /**
     * 设置字体族
     */
    public void setFontFamily(String fontFamily) {
        Typeface typeface;
        switch (fontFamily.toLowerCase()) {
            case "serif":
                typeface = Typeface.SERIF;
                break;
            case "sans-serif":
                typeface = Typeface.SANS_SERIF;
                break;
            case "monospace":
            default:
                typeface = Typeface.MONOSPACE;
                break;
        }
        setTypeface(typeface);

        // 更新行号画笔的字体
        if (lineNumberPaint != null) {
            lineNumberPaint.setTypeface(typeface);
        }

        // 重新计算和绘制
        post(() -> {
            updateLineNumbers();
            requestLayout();
            invalidate();
        });
    }

    // 触摸事件处理已移到双模式支持部分

    /**
     * 处理光标放大镜显示逻辑
     */
    private void handleCursorMagnifier(MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                // 长按开始显示放大镜
                postDelayed(() -> {
                    if (cursorMagnifier != null) {
                        cursorMagnifier.show(event.getX(), event.getY());
                    }
                }, 500); // 500ms后显示放大镜
                break;
                
            case MotionEvent.ACTION_MOVE:
                // 移动时更新放大镜位置
                if (cursorMagnifier != null && cursorMagnifier.getVisibility() == VISIBLE) {
                    cursorMagnifier.show(event.getX(), event.getY());
                }
                break;
                
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                // 抬起手指时隐藏放大镜
                if (cursorMagnifier != null) {
                    cursorMagnifier.hide();
                }
                break;
        }
    }

    /**
     * 应用磁吸效果
     */
    private void applyMagneticSnap(float x, float y) {
        android.text.Layout layout = getLayout();
        if (layout == null) return;
        
        try {
            // 获取触摸位置对应的字符偏移
            int offset = getOffsetForPosition(x, y);
            if (offset < 0 || offset >= getText().length()) return;
            
            String text = getText().toString();
            
            // 查找最近的单词边界
            int wordStart = findWordBoundary(text, offset, true);
            int wordEnd = findWordBoundary(text, offset, false);
            
            // 计算到单词开始和结束的距离
            int distanceToStart = Math.abs(offset - wordStart);
            int distanceToEnd = Math.abs(offset - wordEnd);
            
            // 选择更近的边界
            int targetOffset;
            if (distanceToStart < distanceToEnd) {
                targetOffset = wordStart;
            } else {
                targetOffset = wordEnd;
            }
            
            // 检查是否在磁吸阈值内
            float targetX = layout.getPrimaryHorizontal(targetOffset);
            float distance = Math.abs(x - targetX);
            
            if (distance <= MAGNETIC_SNAP_THRESHOLD) {
                // 应用磁吸效果
                setSelection(targetOffset);
                
                // 可选：添加触觉反馈
                performHapticFeedback(HapticFeedbackConstants.KEYBOARD_TAP);
            }
            
        } catch (Exception e) {
            // 忽略异常，避免崩溃
        }
    }

    /**
     * 查找单词边界
     */
    private int findWordBoundary(String text, int offset, boolean findStart) {
        if (findStart) {
            // 查找单词开始
            int pos = offset;
            while (pos > 0 && isWordCharacter(text.charAt(pos - 1))) {
                pos--;
            }
            return pos;
        } else {
            // 查找单词结束
            int pos = offset;
            while (pos < text.length() && isWordCharacter(text.charAt(pos))) {
                pos++;
            }
            return pos;
        }
    }

    /**
     * 判断是否为单词字符
     */
    private boolean isWordCharacter(char c) {
        return Character.isLetterOrDigit(c) || c == '_';
    }

    /**
     * 启用或禁用磁吸效果
     */
    public void setMagneticSnapEnabled(boolean enabled) {
        this.enableMagneticSnap = enabled;
    }

    /**
     * 启用或禁用光标放大镜
     */
    public void setCursorMagnifierEnabled(boolean enabled) {
        this.enableCursorMagnifier = enabled;
        if (!enabled && cursorMagnifier != null) {
            cursorMagnifier.hide();
        }
    }

    /**
     * 字体大小变化监听器
     */
    public interface OnFontSizeChangeListener {
        void onFontSizeChanged(float fontSize);
    }

    private OnFontSizeChangeListener onFontSizeChangeListener;

    public void setOnFontSizeChangeListener(OnFontSizeChangeListener listener) {
        this.onFontSizeChangeListener = listener;
    }

    /**
     * 设置文件名并自动检测语言
     */
    public void setFileName(String fileName) {
        this.currentFileName = fileName;
        if (syntaxHighlightManager != null) {
            syntaxHighlightManager.autoDetectLanguage(fileName);
        }
    }

    /**
     * 获取当前文件名
     */
    public String getFileName() {
        return currentFileName;
    }

    /**
     * 设置语法高亮语言
     */
    public void setSyntaxLanguage(LanguageDetector.Language language) {
        if (syntaxHighlightManager != null) {
            syntaxHighlightManager.setLanguage(language);
        }
    }

    /**
     * 获取当前语法高亮语言
     */
    public LanguageDetector.Language getSyntaxLanguage() {
        return syntaxHighlightManager != null ? 
               syntaxHighlightManager.getCurrentLanguage() : 
               LanguageDetector.Language.PLAIN_TEXT;
    }

    /**
     * 启用或禁用语法高亮
     */
    public void setSyntaxHighlightEnabled(boolean enabled) {
        if (syntaxHighlightManager != null) {
            syntaxHighlightManager.setHighlightEnabled(enabled);
        }
    }

    /**
     * 获取支持的语言列表
     */
    public LanguageDetector.Language[] getSupportedLanguages() {
        return LanguageDetector.getSupportedLanguages();
    }

    /**
     * 释放资源
     */
    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();

        // 停止键盘监听
        removeCallbacks(keyboardCheckRunnable);

        // 清理语法高亮资源
        if (syntaxHighlightManager != null) {
            syntaxHighlightManager.destroy();
        }

        // 清理大文件优化资源
        cleanupLargeFileOptimization();
    }

    /**
     * 设置选择变化监听器
     */
    public interface OnSelectionChangedListener {
        void onSelectionChanged(int selStart, int selEnd);
    }

    private OnSelectionChangedListener selectionChangedListener;

    public void setOnSelectionChangedListener(OnSelectionChangedListener listener) {
        this.selectionChangedListener = listener;
    }

    @Override
    protected void onSelectionChanged(int selStart, int selEnd) {
        super.onSelectionChanged(selStart, selEnd);
        
        // 更新当前行并重绘
        updateCurrentLine();
        invalidate();
        
        if (selectionChangedListener != null) {
            selectionChangedListener.onSelectionChanged(selStart, selEnd);
        }
    }

    // ==================== 双模式支持 ====================

    /**
     * 模式变化监听器
     */
    public interface OnModeChangeListener {
        void onModeChanged(ViewMode newMode);
        void onRequestEdit(); // 请求进入编辑模式
        void onRequestView(); // 请求进入查看模式
    }

    /**
     * 设置查看模式
     */
    public void setViewMode(ViewMode mode) {
        if (currentMode == mode) return;
        
        ViewMode oldMode = currentMode;
        currentMode = mode;
        
        switch (mode) {
            case VIEW_ONLY:
                setupViewOnlyMode();
                break;
            case EDIT_MODE:
                setupEditMode();
                break;
        }
        
        // 通知模式变化
        if (modeChangeListener != null) {
            modeChangeListener.onModeChanged(mode);
        }
    }

    /**
     * 设置为查看模式
     */
    private void setupViewOnlyMode() {
        // 设置为不可编辑
        setFocusable(false);
        setFocusableInTouchMode(false);
        setCursorVisible(false);
        
        // 隐藏键盘
        hideKeyboard();
        
        // 清除选择
        clearFocus();
        
        // 禁用长按菜单（可选）
        setLongClickable(false);
        
        // 更新当前行高亮（查看模式下可能不需要）
        setHighlightCurrentLine(false);
    }

    /**
     * 设置为编辑模式
     */
    private void setupEditMode() {
        // 设置为可编辑
        setFocusable(true);
        setFocusableInTouchMode(true);
        setCursorVisible(true);
        
        // 启用长按菜单
        setLongClickable(true);
        
        // 启用当前行高亮
        setHighlightCurrentLine(true);
        
        // 请求焦点（但不立即显示键盘）
        requestFocus();
    }

    /**
     * 隐藏键盘
     */
    private void hideKeyboard() {
        InputMethodManager imm =
            (InputMethodManager) getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
        if (imm != null && getWindowToken() != null) {
            // 检查键盘是否真的显示，避免不必要的调用
            if (imm.isActive(this)) {
                imm.hideSoftInputFromWindow(getWindowToken(), InputMethodManager.HIDE_NOT_ALWAYS);
            }
        }
    }

    /**
     * 显示键盘
     */
    private void showKeyboard() {
        InputMethodManager imm =
            (InputMethodManager) getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
        if (imm != null) {
            imm.showSoftInput(this, InputMethodManager.SHOW_IMPLICIT);
        }
    }

    /**
     * 获取当前模式
     */
    public ViewMode getCurrentMode() {
        return currentMode;
    }

    /**
     * 是否为查看模式
     */
    public boolean isViewOnlyMode() {
        return currentMode == ViewMode.VIEW_ONLY;
    }

    /**
     * 是否为编辑模式
     */
    public boolean isEditMode() {
        return currentMode == ViewMode.EDIT_MODE;
    }

    /**
     * 切换到编辑模式（带键盘显示选项）
     */
    public void enterEditMode(boolean showKeyboard) {
        setViewMode(ViewMode.EDIT_MODE);
        if (showKeyboard) {
            post(() -> showKeyboard());
        }
    }

    /**
     * 切换到查看模式
     */
    public void enterViewMode() {
        setViewMode(ViewMode.VIEW_ONLY);
    }

    /**
     * 设置模式变化监听器
     */
    public void setOnModeChangeListener(OnModeChangeListener listener) {
        this.modeChangeListener = listener;
    }

    /**
     * 双击检测相关
     */
    private long lastClickTime = 0;
    private static final long DOUBLE_CLICK_TIME_DELTA = 300; // 双击间隔

    // 触摸事件处理相关
    private float lastTouchX = 0;
    private float lastTouchY = 0;
    private static final float TOUCH_SLOP = 10f; // 触摸滑动阈值

    /**
     * 重写焦点变化处理
     */
    @Override
    protected void onFocusChanged(boolean focused, int direction, Rect previouslyFocusedRect) {
        // 在查看模式下，阻止焦点变化导致的键盘显示
        if (currentMode == ViewMode.VIEW_ONLY) {
            if (focused) {
                // 查看模式下获得焦点时，立即清除焦点并隐藏键盘
                post(() -> {
                    clearFocus();
                    hideKeyboard();
                });
                // 不调用super，避免系统默认的焦点处理
                return;
            }
        }

        super.onFocusChanged(focused, direction, previouslyFocusedRect);

        // 编辑模式下失去焦点时的处理（可选：自动退出编辑模式）
        if (currentMode == ViewMode.EDIT_MODE && !focused && autoExitEditOnFocusLoss) {
            post(() -> {
                if (modeChangeListener != null) {
                    modeChangeListener.onRequestView();
                } else {
                    enterViewMode();
                }
            });
        }
    }

    // 配置选项：失去焦点时是否自动退出编辑模式
    private boolean autoExitEditOnFocusLoss = false;

    /**
     * 设置是否在失去焦点时自动退出编辑模式
     */
    public void setAutoExitEditOnFocusLoss(boolean autoExit) {
        this.autoExitEditOnFocusLoss = autoExit;
    }

    /**
     * 智能键盘管理 - 在缩放时临时隐藏键盘
     */
    private boolean wasKeyboardVisible = false;

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        // 处理缩放手势
        boolean scaleHandled = scaleGestureDetector.onTouchEvent(event);
        
        // 缩放开始时隐藏键盘
        if (scaleGestureDetector.isInProgress()) {
            if (currentMode == ViewMode.EDIT_MODE) {
                // 记录键盘状态并隐藏
                wasKeyboardVisible = isKeyboardVisible();
                if (wasKeyboardVisible) {
                    hideKeyboard();
                }
            }
            return true;
        }

        // 缩放结束后恢复键盘（如果之前是显示的）
        if (event.getAction() == MotionEvent.ACTION_UP && wasKeyboardVisible && currentMode == ViewMode.EDIT_MODE) {
            post(() -> showKeyboard());
            wasKeyboardVisible = false;
        }

        // 在查看模式下处理双击进入编辑模式
        if (currentMode == ViewMode.VIEW_ONLY) {
            return handleViewModeTouch(event);
        }

        // 在编辑模式下处理放大镜显示
        if (currentMode == ViewMode.EDIT_MODE && enableCursorMagnifier && cursorMagnifier != null) {
            handleCursorMagnifier(event);
        }

        // 在编辑模式下处理磁吸效果
        if (currentMode == ViewMode.EDIT_MODE && event.getAction() == MotionEvent.ACTION_UP && enableMagneticSnap) {
            applyMagneticSnap(event.getX(), event.getY());
        }

        // 处理其他触摸事件
        return super.onTouchEvent(event) || scaleHandled;
    }

    /**
     * 处理查看模式下的触摸事件
     */
    private boolean handleViewModeTouch(MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                lastTouchX = event.getX();
                lastTouchY = event.getY();

                long clickTime = System.currentTimeMillis();
                if (clickTime - lastClickTime < DOUBLE_CLICK_TIME_DELTA) {
                    // 双击检测到，请求进入编辑模式
                    if (modeChangeListener != null) {
                        modeChangeListener.onRequestEdit();
                    } else {
                        // 如果没有监听器，直接进入编辑模式
                        enterEditMode(true);
                    }
                    return true;
                }
                lastClickTime = clickTime;
                break;

            case MotionEvent.ACTION_MOVE:
                // 检查是否为滑动操作
                float deltaX = Math.abs(event.getX() - lastTouchX);
                float deltaY = Math.abs(event.getY() - lastTouchY);

                if (deltaX > TOUCH_SLOP || deltaY > TOUCH_SLOP) {
                    // 这是滑动操作，不是点击，重置点击时间
                    lastClickTime = 0;
                }
                break;

            case MotionEvent.ACTION_UP:
                // 单击不做任何操作，避免唤起软键盘
                break;
        }

        // 让父类处理滚动等其他操作，但不处理焦点相关的操作
        return super.onTouchEvent(event);
    }

    /**
     * 检查键盘是否可见
     */
    private boolean isKeyboardVisible() {
        android.view.inputmethod.InputMethodManager imm = 
            (android.view.inputmethod.InputMethodManager) getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
        return imm != null && imm.isActive(this);
    }

    /**
     * 更准确的键盘可见性检测
     */
    private boolean isKeyboardReallyVisible() {
        // 获取根视图
        android.view.View rootView = getRootView();
        if (rootView == null) return false;
        
        // 计算可见区域
        android.graphics.Rect rect = new android.graphics.Rect();
        rootView.getWindowVisibleDisplayFrame(rect);
        
        // 计算屏幕高度和可见高度的差值
        int screenHeight = rootView.getHeight();
        int visibleHeight = rect.height();
        int heightDiff = screenHeight - visibleHeight;
        
        // 如果差值大于屏幕高度的1/4，认为键盘是显示的
        return heightDiff > screenHeight * 0.25;
    }

    // 配置选项：键盘隐藏时是否自动退出编辑模式
    private boolean autoExitEditOnKeyboardHide = false;

    /**
     * 设置是否在键盘隐藏时自动退出编辑模式
     */
    public void setAutoExitEditOnKeyboardHide(boolean autoExit) {
        this.autoExitEditOnKeyboardHide = autoExit;
    }

    // 键盘状态监听
    private boolean lastKeyboardState = false;

    /**
     * 监听键盘状态变化
     */
    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        
        // 检测键盘状态变化
        if (currentMode == ViewMode.EDIT_MODE && autoExitEditOnKeyboardHide) {
            post(() -> checkKeyboardStateChange());
        }
    }

    /**
     * 检查键盘状态变化
     */
    private void checkKeyboardStateChange() {
        boolean currentKeyboardState = isKeyboardReallyVisible();
        
        // 如果键盘从显示变为隐藏
        if (lastKeyboardState && !currentKeyboardState && currentMode == ViewMode.EDIT_MODE) {
            // 延迟执行，避免误判
            postDelayed(() -> {
                if (!isKeyboardReallyVisible() && currentMode == ViewMode.EDIT_MODE) {
                    if (modeChangeListener != null) {
                        modeChangeListener.onRequestView();
                    } else {
                        enterViewMode();
                    }
                }
            }, 300);
        }
        
        lastKeyboardState = currentKeyboardState;
    }

    /**
     * 重写onAttachedToWindow，开始监听键盘状态
     */
    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        // 开始监听键盘状态
        post(() -> {
            lastKeyboardState = isKeyboardReallyVisible();
            startKeyboardMonitoring();
        });
    }

    /**
     * 开始键盘状态监听
     */
    private void startKeyboardMonitoring() {
        if (autoExitEditOnKeyboardHide) {
            // 每500ms检查一次键盘状态
            postDelayed(keyboardCheckRunnable, 500);
        }
    }

    private final Runnable keyboardCheckRunnable = new Runnable() {
        @Override
        public void run() {
            if (autoExitEditOnKeyboardHide && currentMode == ViewMode.EDIT_MODE) {
                checkKeyboardStateChange();
                // 继续监听
                postDelayed(this, 500);
            }
        }
    };

    /**
     * 添加双击空白区域退出编辑模式
     */
    private boolean enableDoubleClickExitEdit = false;

    /**
     * 设置是否启用双击空白区域退出编辑模式
     */
    public void setDoubleClickExitEditEnabled(boolean enabled) {
        this.enableDoubleClickExitEdit = enabled;
    }

    // ==================== 沉浸式阅读功能 ====================

    /**
     * 设置是否显示不可见字符
     */
    public void setShowInvisibleChars(boolean show) {
        if (this.showInvisibleChars != show) {
            this.showInvisibleChars = show;
            refreshTextDisplay();
        }
    }

    /**
     * 刷新文本显示（应用不可见字符渲染）
     */
    private void refreshTextDisplay() {
        if (showInvisibleChars && invisibleCharRenderer != null) {
            String originalText = getText() != null ? getText().toString() : "";
            invisibleCharRenderer.renderInvisibleCharacters(this, originalText);
        }
    }
    /**
     * 设置专注模式管理器（需要在Activity中调用）
     */
    public void setFocusModeManager(com.lingtxt.editor.ui.widget.FocusModeManager manager) {
        this.focusModeManager = manager;
        
        if (manager != null) {
            // 设置当前编辑器为目标
            manager.setTargetEditor(this);
        }
    }

    /**
     * 获取专注模式管理器
     */
    public com.lingtxt.editor.ui.widget.FocusModeManager getFocusModeManager() {
        return focusModeManager;
    }


    // ==================== 大文件优化相关方法 ====================

    @Override
    public void setText(CharSequence text, BufferType type) {
        // 检测是否需要大文件优化
        if (text != null && text.length() > LargeFileStrategy.MEDIUM_FILE_THRESHOLD) {
            // 自动启用大文件优化
            String textStr = text.toString();
            long estimatedSize = textStr.getBytes().length;

            // 创建简单的文件信息
            LargeFileStrategy.FileStrategy strategy = estimatedSize > LargeFileStrategy.LARGE_FILE_THRESHOLD ?
                LargeFileStrategy.FileStrategy.VIRTUALIZED_LOAD : LargeFileStrategy.FileStrategy.PREVIEW_LOAD;

            LargeFileStrategy.FileInfo fileInfo = new LargeFileStrategy.FileInfo(
                estimatedSize, -1, strategy, "自动检测的大文件");

            setTextWithOptimization(textStr, fileInfo);
        } else {
            // 普通文本，直接设置
            disableLargeFileMode();
            super.setText(text, type);
        }
    }

    /**
     * 设置文件内容（支持大文件优化）
     */
    public void setTextWithOptimization(@NonNull String text, @Nullable LargeFileStrategy.FileInfo fileInfo) {
        this.currentFileInfo = fileInfo;

        if (fileInfo != null && fileInfo.getStrategy() != LargeFileStrategy.FileStrategy.FULL_LOAD) {
            // 启用大文件优化模式
            enableLargeFileMode(text, fileInfo);
        } else {
            // 普通模式
            disableLargeFileMode();
            setText(text);
        }
    }

    /**
     * 启用大文件模式
     */
    private void enableLargeFileMode(@NonNull String text, @NonNull LargeFileStrategy.FileInfo fileInfo) {
        isLargeFileMode = true;

        // 设置虚拟化管理器
        if (virtualizationManager != null) {
            virtualizationManager.setText(text, fileInfo.getFileSize());
        }

        // 配置懒加载语法高亮（所有策略都支持语法高亮）
        if (lazyHighlightManager != null) {
            lazyHighlightManager.setEnabled(true);
            lazyHighlightManager.setFileExtension(currentFileName != null ?
                getFileExtension(currentFileName) : null);
        }

        // 调整内存策略
        if (memoryManager != null) {
            memoryManager.checkMemoryPressure();
        }

        // 初始显示内容
        if (virtualizationManager != null) {
            String visibleText = virtualizationManager.getVisibleText();
            super.setText(visibleText);
        }

        Log.d("CodeEditText", "Large file mode enabled: " + fileInfo.getStrategy());
    }

    /**
     * 禁用大文件模式
     */
    private void disableLargeFileMode() {
        isLargeFileMode = false;

        if (lazyHighlightManager != null) {
            lazyHighlightManager.setEnabled(false);
        }

        if (scrollOptimizer != null) {
            scrollOptimizer.setPreloadEnabled(false);
        }

        Log.d("CodeEditText", "Large file mode disabled");
    }

    /**
     * 更新虚拟化范围
     */
    private void updateVirtualizedRange(int scrollY) {
        if (!isLargeFileMode || virtualizationManager == null) {
            return;
        }

        android.text.Layout layout = getLayout();
        if (layout != null) {
            int viewHeight = getHeight();
            virtualizationManager.updateVisibleRangeByScroll(layout, scrollY, viewHeight);
        }
    }

    /**
     * 请求懒加载语法高亮
     */
    private void requestLazyHighlight(int startLine, int endLine) {
        if (!isLargeFileMode || lazyHighlightManager == null || virtualizationManager == null) {
            return;
        }

        // 获取文本行
        String fullText = virtualizationManager.getFullText();
        String[] lines = fullText.split("\n", -1);

        lazyHighlightManager.requestHighlight(lines, startLine, endLine);
    }

    /**
     * 更新虚拟化内容
     */
    private void updateVirtualizedContent(SpannableStringBuilder visibleContent) {
        if (!isLargeFileMode) {
            return;
        }

        // 保存当前光标位置
        int selectionStart = getSelectionStart();
        int selectionEnd = getSelectionEnd();

        // 更新文本内容
        super.setText(visibleContent);

        // 恢复光标位置（需要调整到虚拟化范围内）
        if (selectionStart >= 0 && selectionEnd >= 0) {
            int newStart = Math.min(selectionStart, visibleContent.length());
            int newEnd = Math.min(selectionEnd, visibleContent.length());
            setSelection(newStart, newEnd);
        }
    }

    /**
     * 应用高亮内容
     */
    private void applyHighlightedContent(int startLine, int endLine, SpannableStringBuilder highlightedText) {
        if (!isLargeFileMode) {
            return;
        }

        // 这里可以实现更精细的高亮应用逻辑
        // 目前简化处理，直接替换内容
        updateVirtualizedContent(highlightedText);
    }

    /**
     * 根据内存压力调整性能策略
     */
    private void adjustPerformanceStrategy(MemoryManager.MemoryPressure pressure) {
        switch (pressure) {
            case CRITICAL:
                // 临界内存：禁用语法高亮，减少缓存
                if (lazyHighlightManager != null) {
                    lazyHighlightManager.setEnabled(false);
                }
                if (scrollOptimizer != null) {
                    scrollOptimizer.setPreloadEnabled(false);
                }
                break;
            case LOW:
                // 低内存：减少预加载
                if (scrollOptimizer != null) {
                    scrollOptimizer.setPreloadEnabled(false);
                }
                break;
            case NORMAL:
                // 正常：恢复所有功能
                if (lazyHighlightManager != null) {
                    lazyHighlightManager.setEnabled(true);
                }
                if (scrollOptimizer != null) {
                    scrollOptimizer.setPreloadEnabled(true);
                }
                break;
        }
    }

    /**
     * 释放内存
     */
    private void releaseMemory(int targetReleasePercent) {
        if (lazyHighlightManager != null) {
            lazyHighlightManager.clearCache();
        }

        if (memoryManager != null) {
            memoryManager.clearAllCache();
        }

        // 强制垃圾回收
        if (targetReleasePercent > 50) {
            System.gc();
        }
    }

    /**
     * 预加载内容
     */
    private void preloadContent(int direction) {
        // 这里可以实现内容预加载逻辑
        // 目前简化处理
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        if (fileName == null) return null;
        int lastDot = fileName.lastIndexOf('.');
        return lastDot > 0 ? fileName.substring(lastDot) : null;
    }

    /**
     * 获取内存使用信息
     */
    public String getMemoryInfo() {
        if (memoryManager != null) {
            return memoryManager.getMemoryInfo();
        }
        return "Memory info not available";
    }

    /**
     * 清理大文件优化资源
     */
    private void cleanupLargeFileOptimization() {
        if (virtualizationManager != null) {
            virtualizationManager.cleanup();
        }

        if (lazyHighlightManager != null) {
            lazyHighlightManager.cleanup();
        }

        if (memoryManager != null) {
            memoryManager.cleanup();
        }

        if (scrollOptimizer != null) {
            scrollOptimizer.cleanup();
        }
    }
}