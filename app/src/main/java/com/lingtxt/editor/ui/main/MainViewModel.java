package com.lingtxt.editor.ui.main;

import android.content.Context;
import android.net.Uri;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import com.lingtxt.editor.R;
import com.lingtxt.editor.base.BaseViewModel;
import com.lingtxt.editor.data.model.FileContent;
import com.lingtxt.editor.data.repository.FileRepository;
import com.lingtxt.editor.data.repository.SettingsRepository;
import com.lingtxt.editor.ui.editor.LargeFileStrategy;
import com.lingtxt.editor.utils.EncodingDetector;
import com.lingtxt.editor.utils.FileOperationException;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.schedulers.Schedulers;
import javax.inject.Inject;

/**
 * 主界面ViewModel，处理文件加载和UI状态管理
 */
public class MainViewModel extends BaseViewModel {

    private final FileRepository fileRepository;
    private final SettingsRepository settingsRepository;
    private final Context context;

    // UI状态
    private final MutableLiveData<Boolean> fileLoadingState = new MutableLiveData<>(false);
    private final MutableLiveData<String> errorMessage = new MutableLiveData<>();
    private final MutableLiveData<String> fileContent = new MutableLiveData<>();
    private final MutableLiveData<String> fileName = new MutableLiveData<>();
    private final MutableLiveData<Float> fontSize = new MutableLiveData<>(12f);

    // 大文件策略相关
    private final MutableLiveData<LargeFileStrategy.FileInfo> fileStrategy = new MutableLiveData<>();
    private final MutableLiveData<String> fileWarning = new MutableLiveData<>();
    private final MutableLiveData<Boolean> showLineNumbers = new MutableLiveData<>(true);
    private final MutableLiveData<String> fileEncoding = new MutableLiveData<>("UTF-8");
    private final MutableLiveData<Long> fileSize = new MutableLiveData<>(0L);
    
    // 当前文件状态
    private Uri currentFileUri;
    private boolean isFileModified = false;

    @Inject
    public MainViewModel(FileRepository fileRepository, SettingsRepository settingsRepository, Context context) {
        this.fileRepository = fileRepository;
        this.settingsRepository = settingsRepository;
        this.context = context;
    }

    // Getters for LiveData
    public LiveData<Boolean> getFileLoadingState() {
        return fileLoadingState;
    }

    public LiveData<String> getErrorMessage() {
        return errorMessage;
    }

    public LiveData<String> getFileContent() {
        return fileContent;
    }

    public LiveData<String> getFileName() {
        return fileName;
    }

    public LiveData<Float> getFontSize() {
        return fontSize;
    }

    public LiveData<Boolean> getShowLineNumbers() {
        return showLineNumbers;
    }

    public LiveData<String> getFileEncoding() {
        return fileEncoding;
    }

    public LiveData<Long> getFileSize() {
        return fileSize;
    }

    /**
     * 加载文件
     */
    @SuppressWarnings("ResultOfMethodCallIgnored")
    public void loadFile(Uri uri) {
        fileLoadingState.setValue(true);

        // 首先分析文件策略
        addDisposable(
            LargeFileStrategy.analyzeFile(context, uri, "UTF-8")
                .observeOn(AndroidSchedulers.mainThread()) // 切换到主线程处理策略信息
                .flatMapObservable(fileInfo -> {
                    // 保存文件策略信息（现在在主线程中）
                    fileStrategy.setValue(fileInfo);

                    // 检查是否需要显示警告
                    String warning = LargeFileStrategy.getWarningMessage(fileInfo);
                    fileWarning.setValue(warning);

                    // 如果文件被拒绝加载，直接返回错误
                    if (fileInfo.getStrategy() == LargeFileStrategy.FileStrategy.REJECT_LOAD) {
                        return Observable.error(new RuntimeException(warning));
                    }

                    // 检查文件是否可访问（切换回IO线程）
                    return fileRepository.isFileAccessible(uri)
                        .subscribeOn(Schedulers.io())
                        .flatMapObservable(isAccessible -> {
                            if (!isAccessible) {
                                return Observable.error(new RuntimeException("文件不存在或无法访问"));
                            }

                            // 根据策略加载文件
                            switch (fileInfo.getStrategy()) {
                                case PREVIEW_LOAD:
                                    // 预览模式：只加载部分内容
                                    int previewLines = LargeFileStrategy.getRecommendedPreviewLines(
                                        fileInfo.getStrategy(), fileInfo.getFileSize());
                                    return loadFilePreview(uri, previewLines);
                                case VIRTUALIZED_LOAD:
                                case STREAMING_LOAD:
                                    // 虚拟化模式：加载完整内容但标记为大文件
                                    return fileRepository.openFile(uri);
                                default:
                                    // 完全加载
                                    return fileRepository.openFile(uri);
                            }
                        });
                })
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(
                    fileContentData -> {
                        fileLoadingState.setValue(false);
                        fileContent.setValue(fileContentData.getContent());
                        currentFileUri = uri;
                        isFileModified = false;
                        // 从文件信息获取文件名和其他信息
                        loadFileInfo(uri);
                    },
                    throwable -> {
                        fileLoadingState.setValue(false);
                        String errorMsg = getErrorMessage(throwable);
                        errorMessage.setValue(errorMsg);
                    }
                )
        );
    }

    /**
     * 加载文件预览
     */
    private Observable<FileContent> loadFilePreview(Uri uri, int maxLines) {
        return Observable.fromCallable(() -> {
            // 使用LargeFileHandler获取预览内容
            String encoding = EncodingDetector.detectEncoding(context, uri);
            String previewContent = com.lingtxt.editor.utils.LargeFileHandler
                .getFilePreview(context, uri, encoding, maxLines)
                .blockingGet();

            return new FileContent(previewContent, encoding,
                previewContent.getBytes().length, System.currentTimeMillis());
        });
    }

    /**
     * 加载文件信息
     */
    @SuppressWarnings("ResultOfMethodCallIgnored")
    private void loadFileInfo(Uri uri) {
        addDisposable(
            fileRepository.getFileInfoOnly(uri)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(
                    fileInfo -> {
                        fileName.setValue(fileInfo.getFileName());
                        fileEncoding.setValue(fileInfo.getEncoding());
                        fileSize.setValue(fileInfo.getFileSize());
                    },
                    throwable -> {
                        fileName.setValue(getFileNameFromUri(uri));
                        fileEncoding.setValue("UTF-8");
                        fileSize.setValue(0L);
                    }
                )
        );
    }

    /**
     * 获取用户友好的错误信息
     */
    private String getErrorMessage(Throwable throwable) {
        if (throwable instanceof FileOperationException) {
            FileOperationException fileException =
                (FileOperationException) throwable;
            return fileException.getUserFriendlyMessage();
        } else {
            return context.getString(R.string.file_load_failed, throwable.getMessage());
        }
    }

    /**
     * 打开搜索功能
     */
    public void openSearch() {
        // TODO: 实现搜索功能
    }

    /**
     * 打开跳转到行功能
     */
    public void openGotoLine() {
        // TODO: 实现跳转到行功能
    }

    /**
     * 打开设置
     */
    public void openSettings() {
        // TODO: 实现设置功能
    }

    /**
     * 保存当前文件
     */
    @SuppressWarnings("ResultOfMethodCallIgnored")
    public void saveCurrentFile() {
        if (currentFileUri == null || fileContent.getValue() == null) {
            errorMessage.setValue(context.getString(R.string.no_file_to_save));
            return;
        }

        fileLoadingState.setValue(true);
        
        addDisposable(
            fileRepository.saveFile(currentFileUri, fileContent.getValue())
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(
                    () -> {
                        fileLoadingState.setValue(false);
                        isFileModified = false;
                        // 可以显示保存成功的提示
                    },
                    throwable -> {
                        fileLoadingState.setValue(false);
                        String errorMsg = getErrorMessage(throwable);
                        errorMessage.setValue(context.getString(R.string.save_failed, errorMsg));
                    }
                )
        );
    }

    /**
     * 打开最近文件
     */
    public void openRecentFiles() {
        // 通过Intent启动最近文件Activity
        // 这个方法会被MainActivity调用
    }

    /**
     * 显示文件信息
     */
    @SuppressWarnings("ResultOfMethodCallIgnored")
    public void showFileInfo() {
        if (currentFileUri == null) {
            return;
        }
        
        addDisposable(
            fileRepository.getFileInfoOnly(currentFileUri)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(
                    fileInfo -> {
                        // TODO: 显示文件信息对话框
                    },
                    throwable -> {
                        errorMessage.setValue("获取文件信息失败: " + throwable.getMessage());
                    }
                )
        );
    }

    /**
     * 设置字体大小
     */
    public void setFontSize(float size) {
        fontSize.setValue(size);
        // TODO: 保存到设置中
    }

    /**
     * 切换行号显示
     */
    public void toggleLineNumbers() {
        Boolean current = showLineNumbers.getValue();
        showLineNumbers.setValue(current == null ? true : !current);
        // TODO: 保存到设置中
    }

    /**
     * 标记文件已修改
     */
    public void markFileModified() {
        isFileModified = true;
    }

    /**
     * 检查文件是否已修改
     */
    public boolean isFileModified() {
        return isFileModified;
    }

    /**
     * 增大字体
     */
    public void increaseFontSize() {
        Float current = fontSize.getValue();
        if (current != null && current < 32f) {
            fontSize.setValue(current + 2f);
            // TODO: 保存到设置中
        }
    }

    /**
     * 减小字体
     */
    public void decreaseFontSize() {
        Float current = fontSize.getValue();
        if (current != null && current > 8f) {
            fontSize.setValue(current - 2f);
            // TODO: 保存到设置中
        }
    }

    /**
     * 从URI获取文件名
     */
    private String getFileNameFromUri(Uri uri) {
        String path = uri.getPath();
        if (path != null) {
            int lastSlash = path.lastIndexOf('/');
            if (lastSlash != -1 && lastSlash < path.length() - 1) {
                return path.substring(lastSlash + 1);
            }
        }
        return context.getString(R.string.unknown_file);
    }

    // ==================== 大文件策略相关Getter方法 ====================

    /**
     * 获取文件策略信息
     */
    public LiveData<LargeFileStrategy.FileInfo> getFileStrategy() {
        return fileStrategy;
    }

    /**
     * 获取文件警告信息
     */
    public LiveData<String> getFileWarning() {
        return fileWarning;
    }
}