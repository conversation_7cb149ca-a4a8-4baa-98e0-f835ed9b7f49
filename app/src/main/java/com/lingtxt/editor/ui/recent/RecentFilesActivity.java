package com.lingtxt.editor.ui.recent;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.lingtxt.editor.R;
import com.lingtxt.editor.data.model.RecentFile;
import com.lingtxt.editor.data.repository.RecentFileRepository;
import com.lingtxt.editor.ui.main.MainActivity;
import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.dialog.MaterialAlertDialogBuilder;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.google.android.material.snackbar.Snackbar;
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.disposables.CompositeDisposable;
import io.reactivex.rxjava3.schedulers.Schedulers;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * 最近文件Activity
 */
public class RecentFilesActivity extends AppCompatActivity implements RecentFilesAdapter.OnFileActionListener {

    private static final int REQUEST_CODE_OPEN_FILE = 1001;
    
    private RecyclerView rvRecentFiles;
    private View layoutEmptyState;
    private View progressBar;
    private FloatingActionButton fabOpenFile;
    private MaterialToolbar toolbar;
    
    private RecentFilesAdapter adapter;
    private RecentFileRepository recentFileRepository;
    private CompositeDisposable disposables = new CompositeDisposable();
    
    private List<RecentFile> recentFiles = new ArrayList<>();
    private SortType currentSortType = SortType.BY_TIME;
    
    public enum SortType {
        BY_TIME, BY_NAME, BY_SIZE
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_recent_files);
        
        initViews();
        setupToolbar();
        setupRecyclerView();
        setupFab();
        
        recentFileRepository = new RecentFileRepository(this);
        loadRecentFiles();
    }

    private void initViews() {
        rvRecentFiles = findViewById(R.id.rv_recent_files);
        layoutEmptyState = findViewById(R.id.layout_empty_state);
        progressBar = findViewById(R.id.progress_bar);
        fabOpenFile = findViewById(R.id.fab_open_file);
        toolbar = findViewById(R.id.toolbar);
    }

    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }
        
        toolbar.setOnMenuItemClickListener(this::onMenuItemClick);
    }

    private void setupRecyclerView() {
        adapter = new RecentFilesAdapter(this);
        rvRecentFiles.setLayoutManager(new LinearLayoutManager(this));
        rvRecentFiles.setAdapter(adapter);
    }

    private void setupFab() {
        fabOpenFile.setOnClickListener(v -> openFileChooser());
    }

    private void loadRecentFiles() {
        showLoading(true);
        
        disposables.add(
            recentFileRepository.getRecentFiles()
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(
                    files -> {
                        showLoading(false);
                        this.recentFiles = new ArrayList<>(files);
                        sortFiles(currentSortType);
                        updateUI();
                    },
                    throwable -> {
                        showLoading(false);
                        showError(getString(R.string.recent_files_load_failed, throwable.getMessage()));
                    }
                )
        );
    }

    private void sortFiles(SortType sortType) {
        currentSortType = sortType;
        
        Comparator<RecentFile> comparator;
        switch (sortType) {
            case BY_NAME:
                comparator = (a, b) -> a.getFileName().compareToIgnoreCase(b.getFileName());
                break;
            case BY_SIZE:
                comparator = (a, b) -> Long.compare(b.getFileSize(), a.getFileSize());
                break;
            case BY_TIME:
            default:
                comparator = (a, b) -> Long.compare(b.getLastAccessed(), a.getLastAccessed());
                break;
        }
        
        Collections.sort(recentFiles, comparator);
        adapter.updateFiles(recentFiles);
    }

    private void updateUI() {
        if (recentFiles.isEmpty()) {
            rvRecentFiles.setVisibility(View.GONE);
            layoutEmptyState.setVisibility(View.VISIBLE);
        } else {
            rvRecentFiles.setVisibility(View.VISIBLE);
            layoutEmptyState.setVisibility(View.GONE);
            adapter.updateFiles(recentFiles);
        }
    }

    private void showLoading(boolean show) {
        progressBar.setVisibility(show ? View.VISIBLE : View.GONE);
        rvRecentFiles.setVisibility(show ? View.GONE : View.VISIBLE);
        layoutEmptyState.setVisibility(View.GONE);
    }

    private void showError(String message) {
        Snackbar.make(findViewById(android.R.id.content), message, Snackbar.LENGTH_LONG).show();
    }

    private boolean onMenuItemClick(MenuItem item) {
        int itemId = item.getItemId();
        
        if (itemId == R.id.action_clear_all) {
            showClearAllDialog();
            return true;
        } else if (itemId == R.id.action_sort_by_time) {
            sortFiles(SortType.BY_TIME);
            item.setChecked(true);
            return true;
        } else if (itemId == R.id.action_sort_by_name) {
            sortFiles(SortType.BY_NAME);
            item.setChecked(true);
            return true;
        } else if (itemId == R.id.action_sort_by_size) {
            sortFiles(SortType.BY_SIZE);
            item.setChecked(true);
            return true;
        }
        
        return false;
    }

    private void showClearAllDialog() {
        new MaterialAlertDialogBuilder(this)
            .setTitle("清空最近文件")
            .setMessage("确定要清空所有最近文件记录吗？")
            .setPositiveButton("清空", (dialog, which) -> clearAllFiles())
            .setNegativeButton("取消", null)
            .show();
    }

    private void clearAllFiles() {
        disposables.add(
            recentFileRepository.clearRecentFiles()
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(
                    () -> {
                        recentFiles.clear();
                        updateUI();
                        Snackbar.make(findViewById(android.R.id.content), 
                                    "已清空最近文件", Snackbar.LENGTH_SHORT).show();
                    },
                    throwable -> showError("清空失败: " + throwable.getMessage())
                )
        );
    }

    private void openFileChooser() {
        Intent intent = new Intent(Intent.ACTION_OPEN_DOCUMENT);
        intent.addCategory(Intent.CATEGORY_OPENABLE);

        // 临时测试：允许所有文件类型
        intent.setType("*/*");

        // 也添加我们支持的 MIME 类型作为提示
        String[] mimeTypes = com.lingtxt.editor.config.SupportedFileTypes.getSupportedMimeTypes();
        intent.putExtra(Intent.EXTRA_MIME_TYPES, mimeTypes);

        try {
            startActivityForResult(intent, REQUEST_CODE_OPEN_FILE);
        } catch (android.content.ActivityNotFoundException e) {
            showError(getString(R.string.file_manager_not_found));
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        
        if (requestCode == REQUEST_CODE_OPEN_FILE && resultCode == RESULT_OK) {
            if (data != null && data.getData() != null) {
                Uri uri = data.getData();
                openFileInMainActivity(uri);
            }
        }
    }

    private void openFileInMainActivity(Uri uri) {
        Intent intent = new Intent(this, MainActivity.class);
        intent.setAction(Intent.ACTION_VIEW);
        intent.setData(uri);
        intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
        startActivity(intent);
        finish();
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        disposables.clear();
    }

    // RecentFilesAdapter.OnFileActionListener 实现
    @Override
    public void onFileClick(RecentFile file) {
        openFileInMainActivity(file.getUriObject());
    }

    @Override
    public void onFileRemove(RecentFile file) {
        disposables.add(
            recentFileRepository.removeRecentFile(file.getUri())
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(
                    () -> {
                        recentFiles.remove(file);
                        updateUI();
                        Snackbar.make(findViewById(android.R.id.content), 
                                    "已移除 " + file.getFileName(), Snackbar.LENGTH_SHORT).show();
                    },
                    throwable -> showError("移除失败: " + throwable.getMessage())
                )
        );
    }
}