package com.lingtxt.editor.ui.dialog;

import android.content.Context;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.chip.Chip;
import com.google.android.material.chip.ChipGroup;
import com.google.android.material.dialog.MaterialAlertDialogBuilder;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.lingtxt.editor.R;
import com.lingtxt.editor.config.SupportedFileTypes;
import java.util.*;

/**
 * 支持的文件类型管理对话框
 */
public class SupportedFileTypesDialog {
    
    private final Context context;
    private AlertDialog dialog;
    private RecyclerView recyclerView;
    private FileTypeCategoryAdapter adapter;
    private ChipGroup customExtensionsChipGroup;
    private FloatingActionButton fabAdd;
    
    public SupportedFileTypesDialog(Context context) {
        this.context = context;
        createDialog();
    }
    
    private void createDialog() {
        View dialogView = LayoutInflater.from(context).inflate(R.layout.dialog_supported_file_types, null);
        
        // 初始化视图
        recyclerView = dialogView.findViewById(R.id.rv_file_types);
        customExtensionsChipGroup = dialogView.findViewById(R.id.chip_group_custom);
        fabAdd = dialogView.findViewById(R.id.fab_add);
        
        // 设置RecyclerView
        setupRecyclerView();
        
        // 设置自定义扩展名
        setupCustomExtensions();
        
        // 设置添加按钮
        setupAddButton();
        
        // 创建对话框
        dialog = new MaterialAlertDialogBuilder(context)
            .setTitle("支持的文件类型")
            .setView(dialogView)
            .setPositiveButton("确定", null)
            .setNeutralButton("重置", (d, which) -> showResetConfirmDialog())
            .create();
    }
    
    private void setupRecyclerView() {
        adapter = new FileTypeCategoryAdapter();
        recyclerView.setLayoutManager(new LinearLayoutManager(context));
        recyclerView.setAdapter(adapter);
    }
    
    private void setupCustomExtensions() {
        refreshCustomExtensions();
    }
    
    private void refreshCustomExtensions() {
        customExtensionsChipGroup.removeAllViews();
        
        Set<String> customExtensions = SupportedFileTypes.getCustomExtensions(context);
        for (String extension : customExtensions) {
            Chip chip = new Chip(context);
            chip.setText(extension);
            chip.setCloseIconVisible(true);
            chip.setOnCloseIconClickListener(v -> {
                SupportedFileTypes.removeCustomExtension(context, extension);
                refreshCustomExtensions();
            });
            customExtensionsChipGroup.addView(chip);
        }
    }
    
    private void setupAddButton() {
        fabAdd.setOnClickListener(v -> showAddExtensionDialog());
    }
    
    private void showAddExtensionDialog() {
        EditText editText = new EditText(context);
        editText.setHint("输入文件扩展名（不含点号）");
        
        new MaterialAlertDialogBuilder(context)
            .setTitle("添加自定义扩展名")
            .setView(editText)
            .setPositiveButton("添加", (d, which) -> {
                String extension = editText.getText().toString().trim().toLowerCase();
                if (!extension.isEmpty() && isValidExtension(extension)) {
                    SupportedFileTypes.addCustomExtension(context, extension);
                    refreshCustomExtensions();
                } else {
                    showError("请输入有效的扩展名");
                }
            })
            .setNegativeButton("取消", null)
            .show();
    }
    
    private boolean isValidExtension(String extension) {
        // 检查是否为有效的扩展名格式
        return extension.matches("[a-zA-Z0-9]+") && extension.length() <= 10;
    }
    
    private void showResetConfirmDialog() {
        new MaterialAlertDialogBuilder(context)
            .setTitle("重置确认")
            .setMessage("确定要清除所有自定义扩展名吗？")
            .setPositiveButton("确定", (d, which) -> {
                // 清除所有自定义扩展名
                Set<String> customExtensions = SupportedFileTypes.getCustomExtensions(context);
                for (String ext : new HashSet<>(customExtensions)) {
                    SupportedFileTypes.removeCustomExtension(context, ext);
                }
                refreshCustomExtensions();
            })
            .setNegativeButton("取消", null)
            .show();
    }
    
    private void showError(String message) {
        new MaterialAlertDialogBuilder(context)
            .setTitle("错误")
            .setMessage(message)
            .setPositiveButton("确定", null)
            .show();
    }
    
    public void show() {
        if (dialog != null) {
            dialog.show();
        }
    }
    
    /**
     * 文件类型分类适配器
     */
    private class FileTypeCategoryAdapter extends RecyclerView.Adapter<FileTypeCategoryAdapter.ViewHolder> {
        
        private final List<CategoryItem> categories = new ArrayList<>();
        
        public FileTypeCategoryAdapter() {
            loadCategories();
        }
        
        private void loadCategories() {
            categories.clear();
            
            // 添加内置分类
            for (Map.Entry<String, String[]> entry : SupportedFileTypes.BUILTIN_EXTENSIONS.entrySet()) {
                String categoryName = entry.getKey();
                String[] extensions = entry.getValue();
                String extensionsList = String.join(", ", extensions);
                categories.add(new CategoryItem(categoryName, extensionsList, extensions.length));
            }
            
            // 添加用户自定义分类
            Set<String> customExtensions = SupportedFileTypes.getCustomExtensions(context);
            if (!customExtensions.isEmpty()) {
                String extensionsList = String.join(", ", customExtensions);
                categories.add(new CategoryItem("用户自定义", extensionsList, customExtensions.size()));
            }
        }
        
        @NonNull
        @Override
        public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(context).inflate(R.layout.item_file_type_category, parent, false);
            return new ViewHolder(view);
        }
        
        @Override
        public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
            CategoryItem item = categories.get(position);
            holder.bind(item);
        }
        
        @Override
        public int getItemCount() {
            return categories.size();
        }
        
        class ViewHolder extends RecyclerView.ViewHolder {
            TextView categoryName;
            TextView extensionsList;
            TextView extensionsCount;
            
            ViewHolder(@NonNull View itemView) {
                super(itemView);
                categoryName = itemView.findViewById(R.id.tv_category_name);
                extensionsList = itemView.findViewById(R.id.tv_extensions_list);
                extensionsCount = itemView.findViewById(R.id.tv_extensions_count);
            }
            
            void bind(CategoryItem item) {
                categoryName.setText(item.categoryName);
                extensionsList.setText(item.extensionsList);
                extensionsCount.setText(String.valueOf(item.count));
            }
        }
    }
    
    /**
     * 分类项数据类
     */
    private static class CategoryItem {
        final String categoryName;
        final String extensionsList;
        final int count;
        
        CategoryItem(String categoryName, String extensionsList, int count) {
            this.categoryName = categoryName;
            this.extensionsList = extensionsList;
            this.count = count;
        }
    }
}
