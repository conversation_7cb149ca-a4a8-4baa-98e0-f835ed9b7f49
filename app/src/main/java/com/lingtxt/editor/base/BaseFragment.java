package com.lingtxt.editor.base;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider;

/**
 * 基础Fragment类，提供MVVM架构的通用功能
 */
public abstract class BaseFragment<T extends ViewDataBinding, V extends ViewModel> extends Fragment {

    protected T binding;
    protected V viewModel;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        // 初始化DataBinding
        binding = DataBindingUtil.inflate(inflater, getLayoutId(), container, false);
        binding.setLifecycleOwner(this);
        
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        // 初始化ViewModel
        viewModel = new ViewModelProvider(this).get(getViewModelClass());
        
        // 设置ViewModel到binding
        setViewModel();
        
        // 初始化UI
        initializeUI();
        
        // 观察数据
        observeData();
    }

    /**
     * 获取布局ID
     */
    protected abstract int getLayoutId();

    /**
     * 获取ViewModel类
     */
    protected abstract Class<V> getViewModelClass();

    /**
     * 设置ViewModel到DataBinding
     */
    protected abstract void setViewModel();

    /**
     * 初始化UI组件
     */
    protected abstract void initializeUI();

    /**
     * 观察数据变化
     */
    protected abstract void observeData();

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (binding != null) {
            binding.unbind();
            binding = null;
        }
    }
}