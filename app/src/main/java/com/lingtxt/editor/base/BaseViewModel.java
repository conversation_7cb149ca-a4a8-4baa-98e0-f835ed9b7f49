package com.lingtxt.editor.base;

import androidx.lifecycle.ViewModel;
import io.reactivex.rxjava3.disposables.CompositeDisposable;
import io.reactivex.rxjava3.disposables.Disposable;

/**
 * 基础ViewModel类，提供通用功能
 */
public abstract class BaseViewModel extends ViewModel {

    protected CompositeDisposable compositeDisposable = new CompositeDisposable();

    /**
     * 添加Disposable到CompositeDisposable中统一管理
     *
     * @param disposable 需要管理的Disposable对象
     */
    @SuppressWarnings("UnusedReturnValue")
    protected void addDisposable(Disposable disposable) {
        compositeDisposable.add(disposable);
    }

    @Override
    protected void onCleared() {
        super.onCleared();
        // 清理所有订阅，防止内存泄漏
        if (compositeDisposable != null && !compositeDisposable.isDisposed()) {
            compositeDisposable.clear();
        }
    }
}