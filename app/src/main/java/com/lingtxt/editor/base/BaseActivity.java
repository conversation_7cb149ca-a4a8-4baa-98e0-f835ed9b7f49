package com.lingtxt.editor.base;

import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider;

/**
 * 基础Activity类，提供MVVM架构的通用功能
 */
public abstract class BaseActivity<T extends ViewDataBinding, V extends ViewModel> extends AppCompatActivity {

    protected T binding;
    protected V viewModel;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 初始化DataBinding
        binding = DataBindingUtil.setContentView(this, getLayoutId());
        binding.setLifecycleOwner(this);
        
        // 设置ViewModel到binding（由子类实现具体的ViewModel创建逻辑）
        setViewModel();
        
        // 初始化UI
        initializeUI();
        
        // 观察数据
        observeData();
    }

    /**
     * 获取布局ID
     */
    protected abstract int getLayoutId();

    /**
     * 获取ViewModel类
     */
    protected abstract Class<V> getViewModelClass();

    /**
     * 设置ViewModel到DataBinding
     */
    protected abstract void setViewModel();

    /**
     * 初始化UI组件
     */
    protected abstract void initializeUI();

    /**
     * 观察数据变化
     */
    protected abstract void observeData();

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (binding != null) {
            binding.unbind();
        }
    }
}