package com.lingtxt.editor.config;

import android.content.Context;
import android.content.SharedPreferences;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 支持的文件类型统一配置
 * 统一管理所有支持的文件扩展名、MIME类型和用户自定义扩展名
 */
public class SupportedFileTypes {

    private static final String PREFS_NAME = "supported_file_types";
    private static final String KEY_CUSTOM_EXTENSIONS = "custom_extensions";

    // 内置的文本文件扩展名（按分类组织）
    public static final Map<String, String[]> BUILTIN_EXTENSIONS = new LinkedHashMap<String, String[]>() {{
        // 纯文本文件
        put("纯文本", new String[]{
            "txt", "text", "log", "readme", "license", "changelog", "authors", "contributors",
            "todo", "note", "notes", "info", "doc", "rtf"
        });

        // 标记语言
        put("标记语言", new String[]{
            "md", "markdown", "mdown", "mkd", "rst", "asciidoc", "adoc", "textile", "wiki"
        });

        // 数据格式
        put("数据格式", new String[]{
            "json", "xml", "yaml", "yml", "toml", "ini", "conf", "config", "properties",
            "csv", "tsv", "dat", "data"
        });

        // Web技术
        put("Web技术", new String[]{
            "html", "htm", "xhtml", "css", "scss", "sass", "less", "js", "jsx", "ts", "tsx",
            "vue", "svelte", "php", "asp", "jsp", "erb", "ejs", "hbs", "mustache"
        });

        // 编程语言 - 主流语言
        put("编程语言", new String[]{
            "java", "kt", "scala", "groovy", "py", "pyw", "rb", "php", "c", "cpp", "cc", "cxx",
            "h", "hpp", "cs", "vb", "fs", "go", "rs", "swift", "m", "mm", "pl", "pm", "r"
        });

        // 脚本语言
        put("脚本语言", new String[]{
            "sh", "bash", "zsh", "fish", "csh", "tcsh", "bat", "cmd", "ps1", "psm1", "psd1",
            "lua", "tcl", "awk", "sed"
        });

        // 配置文件
        put("配置文件", new String[]{
            "cfg", "conf", "config", "ini", "env", "envrc", "profile", "bashrc", "zshrc", "vimrc",
            "gitignore", "gitattributes", "gitconfig", "editorconfig", "eslintrc", "prettierrc",
            "babelrc", "npmrc", "yarnrc", "dockerignore", "dockerfile", "makefile"
        });

        // 查询语言
        put("查询语言", new String[]{
            "sql", "ddl", "dml", "plsql", "psql"
        });

        // 特殊格式
        put("特殊格式", new String[]{
            "diff", "patch", "rej", "orig", "bak", "tmp", "temp", "swp", "swo",
            "env", "envrc", "profile", "bashrc", "zshrc", "vimrc", "emacs"
        });

        // 新兴技术
        put("新兴技术", new String[]{
            "dart", "kotlin", "nim", "zig", "v", "crystal", "elixir", "elm", "haskell", "clojure",
            "julia", "matlab", "octave", "sage", "mathematica"
        });
    }};

    // 对应的MIME类型（只包含文本类型）
    private static final String[] MIME_TYPES = {
        "text/plain",
        "text/markdown",
        "application/json",
        "text/xml",
        "application/xml",
        "text/html",
        "text/css",
        "text/javascript",
        "application/javascript",
        "text/x-java-source",
        "text/x-python",
        "text/x-c",
        "text/x-c++",
        "text/x-php",
        "text/x-ruby",
        "text/x-go",
        "text/x-rust",
        "text/x-swift",
        "application/x-sh",
        "application/x-bat",
        "application/x-msdos-program",
        "text/x-bat",
        "application/bat",
        "application/x-msdownload",
        "text/x-sql",
        "text/csv",
        "application/x-yaml",
        "text/yaml",
        "text/x-ini",
        "text/x-properties",
        "text/x-diff",
        "text/x-patch",
        "application/x-httpd-php",
        "text/x-script",
        "application/x-perl",
        "text/x-log",
        "application/octet-stream"
        // 添加了 .bat 文件的各种可能 MIME 类型
    };

    // 主要文件类型（用于AndroidManifest中的intent-filter）
    private static final String[] PRIMARY_EXTENSIONS = {
        "txt", "md", "xml", "yaml", "yml", "java", "py", "go",
        "html", "css", "js", "json", "sql", "sh", "bat", "log",
        "c", "cpp", "h", "cs", "php", "rb", "kt", "swift"
    };


    /**
     * 获取用户自定义扩展名
     */
    @NonNull
    public static Set<String> getCustomExtensions(@NonNull Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        String customExtStr = prefs.getString(KEY_CUSTOM_EXTENSIONS, "");

        Set<String> customExtensions = new HashSet<>();
        if (!customExtStr.isEmpty()) {
            String[] extensions = customExtStr.split(",");
            for (String ext : extensions) {
                String trimmed = ext.trim().toLowerCase();
                if (!trimmed.isEmpty()) {
                    customExtensions.add(trimmed);
                }
            }
        }
        return customExtensions;
    }

    /**
     * 添加用户自定义扩展名
     */
    public static void addCustomExtension(@NonNull Context context, @NonNull String extension) {
        Set<String> customExtensions = getCustomExtensions(context);
        customExtensions.add(extension.toLowerCase().trim());
        saveCustomExtensions(context, customExtensions);
    }

    /**
     * 移除用户自定义扩展名
     */
    public static void removeCustomExtension(@NonNull Context context, @NonNull String extension) {
        Set<String> customExtensions = getCustomExtensions(context);
        customExtensions.remove(extension.toLowerCase().trim());
        saveCustomExtensions(context, customExtensions);
    }

    /**
     * 保存用户自定义扩展名
     */
    private static void saveCustomExtensions(@NonNull Context context, @NonNull Set<String> extensions) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        String extensionsStr = String.join(",", extensions);
        prefs.edit().putString(KEY_CUSTOM_EXTENSIONS, extensionsStr).apply();
    }

    /**
     * 检查文件是否被支持（只检查内置扩展名）
     */
    public static boolean isSupportedFile(@NonNull String fileName) {
        String extension = getFileExtension(fileName);
        return extension != null && isSupportedExtension(extension);
    }

    /**
     * 检查扩展名是否被支持（只检查内置扩展名）
     */
    public static boolean isSupportedExtension(@NonNull String extension) {
        String lowerExt = extension.toLowerCase();

        // 检查内置扩展名
        for (String[] extensions : BUILTIN_EXTENSIONS.values()) {
            for (String supportedExt : extensions) {
                if (supportedExt.equals(lowerExt)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 获取文件扩展名
     */
    @Nullable
    public static String getFileExtension(@NonNull String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex + 1).toLowerCase();
        }
        return null;
    }

    /**
     * 获取支持的MIME类型
     */
    @NonNull
    public static String[] getSupportedMimeTypes() {
        return MIME_TYPES.clone();
    }

    /**
     * 获取文件类型的显示名称（通过文件名）
     */
    @NonNull
    public static String getFileTypeDisplayName(@NonNull String fileName) {
        String extension = getFileExtension(fileName);
        if (extension == null) {
            return "文件";
        }
        return getExtensionDisplayName(extension);
    }

    /**
     * 获取扩展名的显示名称
     */
    @NonNull
    public static String getExtensionDisplayName(@NonNull String extension) {
        switch (extension.toLowerCase()) {
            case "txt": return "文本文件";
            case "md": case "markdown": return "Markdown文档";
            case "json": return "JSON数据";
            case "xml": return "XML文档";
            case "html": case "htm": return "HTML网页";
            case "css": return "CSS样式表";
            case "js": return "JavaScript脚本";
            case "java": return "Java源码";
            case "py": return "Python脚本";
            case "c": return "C源码";
            case "cpp": case "cc": case "cxx": return "C++源码";
            case "h": case "hpp": return "C/C++头文件";
            case "php": return "PHP脚本";
            case "sql": return "SQL脚本";
            case "yaml": case "yml": return "YAML配置";
            case "ini": return "INI配置";
            case "conf": case "config": return "配置文件";
            case "log": return "日志文件";
            case "csv": return "CSV数据";
            case "sh": case "bash": return "Shell脚本";
            case "bat": case "cmd": return "批处理文件";
            case "rs": return "Rust源码";
            case "go": return "Go源码";
            case "swift": return "Swift源码";
            case "kt": return "Kotlin源码";
            case "dart": return "Dart源码";
            default: return extension.toUpperCase() + "文件";
        }
    }

    /**
     * 获取扩展名分类
     */
    @NonNull
    public static String getExtensionCategory(@NonNull String extension) {
        String lowerExt = extension.toLowerCase();

        for (Map.Entry<String, String[]> entry : BUILTIN_EXTENSIONS.entrySet()) {
            for (String ext : entry.getValue()) {
                if (ext.equals(lowerExt)) {
                    return entry.getKey();
                }
            }
        }

        return "用户自定义";
    }

    /**
     * 获取主要扩展名（用于AndroidManifest）
     */
    @NonNull
    public static String[] getPrimaryExtensions() {
        return PRIMARY_EXTENSIONS.clone();
    }


    /**
     * 判断是否为编程语言文件
     */
    public static boolean isProgrammingFile(@NonNull String fileName) {
        String extension = getFileExtension(fileName);
        if (extension == null) return false;

        String category = getExtensionCategory(extension);
        return "编程语言".equals(category) || "脚本语言".equals(category) || "Web技术".equals(category);
    }
    
    public static boolean isConfigFile(@NonNull String fileName) {
        String extension = getFileExtension(fileName);
        if (extension == null) return false;
        
        Set<String> configExtensions = new HashSet<>(Arrays.asList(
            "ini", "cfg", "conf", "config", "properties", "toml", 
            "yaml", "yml", "json", "xml", "plist"
        ));
        
        return configExtensions.contains(extension);
    }

    /**
     * 判断是否为Web文件
     */
    public static boolean isWebFile(@NonNull String fileName) {
        String extension = getFileExtension(fileName);
        if (extension == null) return false;
        
        Set<String> webExtensions = new HashSet<>(Arrays.asList(
            "html", "htm", "xhtml", "css", "scss", "sass", "less", "styl"
        ));
        
        return webExtensions.contains(extension);
    }

    /**
     * 判断是否为脚本文件
     */
    public static boolean isScriptFile(@NonNull String fileName) {
        String extension = getFileExtension(fileName);
        if (extension == null) return false;
        
        Set<String> scriptExtensions = new HashSet<>(Arrays.asList(
            "sh", "bash", "zsh", "fish", "bat", "cmd", "ps1", "psm1"
        ));
        
        return scriptExtensions.contains(extension);
    }
}