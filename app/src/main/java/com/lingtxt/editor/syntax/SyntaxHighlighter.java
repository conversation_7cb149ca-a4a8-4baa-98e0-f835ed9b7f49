package com.lingtxt.editor.syntax;

import android.graphics.Color;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.style.ForegroundColorSpan;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 语法高亮引擎
 * 为不同编程语言提供语法高亮功能
 */
public class SyntaxHighlighter {

    /**
     * 语法元素类型
     */
    public enum TokenType {
        KEYWORD,        // 关键字
        STRING,         // 字符串
        COMMENT,        // 注释
        NUMBER,         // 数字
        OPERATOR,       // 操作符
        IDENTIFIER,     // 标识符
        TYPE,           // 类型
        FUNCTION,       // 函数
        ANNOTATION,     // 注解
        TAG,            // HTML标签
        ATTRIBUTE,      // 属性
        NORMAL          // 普通文本
    }

    /**
     * 语法规则
     */
    public static class SyntaxRule {
        public final TokenType type;
        public final Pattern pattern;
        public final int color;

        public SyntaxRule(TokenType type, String regex, int color) {
            this.type = type;
            this.pattern = Pattern.compile(regex);
            this.color = color;
        }

        public SyntaxRule(TokenType type, Pattern pattern, int color) {
            this.type = type;
            this.pattern = pattern;
            this.color = color;
        }
    }

    /**
     * 默认颜色主题（深色主题）
     */
    public static class DefaultColors {
        public static final int KEYWORD = Color.parseColor("#569CD6");      // 蓝色
        public static final int STRING = Color.parseColor("#CE9178");       // 橙色
        public static final int COMMENT = Color.parseColor("#6A9955");      // 绿色
        public static final int NUMBER = Color.parseColor("#B5CEA8");       // 浅绿
        public static final int OPERATOR = Color.parseColor("#D4D4D4");     // 灰白
        public static final int TYPE = Color.parseColor("#4EC9B0");        // 青色
        public static final int FUNCTION = Color.parseColor("#DCDCAA");     // 黄色
        public static final int ANNOTATION = Color.parseColor("#9CDCFE");   // 浅蓝
        public static final int TAG = Color.parseColor("#569CD6");         // 蓝色
        public static final int ATTRIBUTE = Color.parseColor("#92C5F8");   // 浅蓝
        public static final int NORMAL = Color.parseColor("#D4D4D4");      // 默认白色
    }

    private final LanguageDetector.Language language;
    private final List<SyntaxRule> rules;

    public SyntaxHighlighter(LanguageDetector.Language language) {
        this.language = language;
        this.rules = createRulesForLanguage(language);
    }

    /**
     * 为指定语言创建语法规则
     */
    private List<SyntaxRule> createRulesForLanguage(LanguageDetector.Language language) {
        List<SyntaxRule> rules = new ArrayList<>();
        
        switch (language) {
            case JAVA:
                addJavaRules(rules);
                break;
            case JAVASCRIPT:
                addJavaScriptRules(rules);
                break;
            case PYTHON:
                addPythonRules(rules);
                break;
            case HTML:
                addHtmlRules(rules);
                break;
            case CSS:
                addCssRules(rules);
                break;
            case XML:
                addXmlRules(rules);
                break;
            case JSON:
                addJsonRules(rules);
                break;
            case MARKDOWN:
                addMarkdownRules(rules);
                break;
            case YAML:
                addYamlRules(rules);
                break;
            case PROPERTIES:
                addPropertiesRules(rules);
                break;
            case SHELL:
                addShellRules(rules);
                break;
            case SQL:
                addSqlRules(rules);
                break;
            case C:
                addCRules(rules);
                break;
            case CPP:
                addCppRules(rules);
                break;
            case GO:
                addGoRules(rules);
                break;
            case KOTLIN:
                addKotlinRules(rules);
                break;
            case SWIFT:
                addSwiftRules(rules);
                break;
            case RUST:
                addRustRules(rules);
                break;
            case PHP:
                addPhpRules(rules);
                break;
            case RUBY:
                addRubyRules(rules);
                break;
            case TYPESCRIPT:
                addTypeScriptRules(rules);
                break;
            case CSHARP:
                addCSharpRules(rules);
                break;
            case DART:
                addDartRules(rules);
                break;
            case SCALA:
                addScalaRules(rules);
                break;
            case PERL:
                addPerlRules(rules);
                break;
            case LUA:
                addLuaRules(rules);
                break;
            case R:
                addRRules(rules);
                break;
            case MATLAB:
                addMatlabRules(rules);
                break;
            case OBJECTIVE_C:
                addObjectiveCRules(rules);
                break;
            case ASSEMBLY:
                addAssemblyRules(rules);
                break;
            case BASH:
                addBashRules(rules);
                break;
            case POWERSHELL:
                addPowerShellRules(rules);
                break;
            case BATCH:
                addBatchRules(rules);
                break;
            case VB:
                addVBRules(rules);
                break;
            case HASKELL:
                addHaskellRules(rules);
                break;
            case GROOVY:
                addGroovyRules(rules);
                break;
            case DOCKERFILE:
                addDockerfileRules(rules);
                break;
            case INI:
                addIniRules(rules);
                break;
            case TOML:
                addTomlRules(rules);
                break;
            case MAKEFILE:
                addMakefileRules(rules);
                break;
            default:
                // 对于不支持的语言，添加基本的通用规则
                addGenericRules(rules);
                break;
        }
        
        return rules;
    }

    /**
     * Java语法规则
     */
    private void addJavaRules(List<SyntaxRule> rules) {
        // 注释（必须在字符串之前）
        rules.add(new SyntaxRule(TokenType.COMMENT, Pattern.compile("//.*$", Pattern.MULTILINE), DefaultColors.COMMENT));
        rules.add(new SyntaxRule(TokenType.COMMENT, "/\\*[\\s\\S]*?\\*/", DefaultColors.COMMENT));
        
        // 字符串
        rules.add(new SyntaxRule(TokenType.STRING, "\"(?:[^\"\\\\]|\\\\.)*\"", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "'(?:[^'\\\\]|\\\\.)*'", DefaultColors.STRING));
        
        // 注解
        rules.add(new SyntaxRule(TokenType.ANNOTATION, "@\\w+", DefaultColors.ANNOTATION));
        
        // 关键字
        String javaKeywords = "\\b(abstract|assert|boolean|break|byte|case|catch|char|class|const|" +
                             "continue|default|do|double|else|enum|extends|final|finally|float|for|" +
                             "goto|if|implements|import|instanceof|int|interface|long|native|new|" +
                             "package|private|protected|public|return|short|static|strictfp|super|" +
                             "switch|synchronized|this|throw|throws|transient|try|void|volatile|while)\\b";
        rules.add(new SyntaxRule(TokenType.KEYWORD, javaKeywords, DefaultColors.KEYWORD));
        
        // 基本类型和常用类
        String javaTypes = "\\b(String|Integer|Long|Double|Float|Boolean|Character|Byte|Short|" +
                          "Object|List|Map|Set|ArrayList|HashMap|HashSet)\\b";
        rules.add(new SyntaxRule(TokenType.TYPE, javaTypes, DefaultColors.TYPE));
        
        // 数字
        rules.add(new SyntaxRule(TokenType.NUMBER, "\\b\\d+(\\.\\d+)?[fFdDlL]?\\b", DefaultColors.NUMBER));
        rules.add(new SyntaxRule(TokenType.NUMBER, "\\b0[xX][0-9a-fA-F]+[lL]?\\b", DefaultColors.NUMBER));
        
        // 函数调用
        rules.add(new SyntaxRule(TokenType.FUNCTION, "\\b\\w+(?=\\s*\\()", DefaultColors.FUNCTION));
    }

    /**
     * JavaScript语法规则
     */
    private void addJavaScriptRules(List<SyntaxRule> rules) {
        // 注释
        rules.add(new SyntaxRule(TokenType.COMMENT, Pattern.compile("//.*$", Pattern.MULTILINE), DefaultColors.COMMENT));
        rules.add(new SyntaxRule(TokenType.COMMENT, "/\\*[\\s\\S]*?\\*/", DefaultColors.COMMENT));
        
        // 字符串
        rules.add(new SyntaxRule(TokenType.STRING, "\"(?:[^\"\\\\]|\\\\.)*\"", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "'(?:[^'\\\\]|\\\\.)*'", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "`(?:[^`\\\\]|\\\\.)*`", DefaultColors.STRING));
        
        // 关键字
        String jsKeywords = "\\b(async|await|break|case|catch|class|const|continue|debugger|" +
                           "default|delete|do|else|export|extends|finally|for|function|if|" +
                           "import|in|instanceof|let|new|return|super|switch|this|throw|" +
                           "try|typeof|var|void|while|with|yield)\\b";
        rules.add(new SyntaxRule(TokenType.KEYWORD, jsKeywords, DefaultColors.KEYWORD));
        
        // 数字
        rules.add(new SyntaxRule(TokenType.NUMBER, "\\b\\d+(\\.\\d+)?\\b", DefaultColors.NUMBER));
        
        // 函数
        rules.add(new SyntaxRule(TokenType.FUNCTION, "\\b\\w+(?=\\s*\\()", DefaultColors.FUNCTION));
    }

    /**
     * Python语法规则
     */
    private void addPythonRules(List<SyntaxRule> rules) {
        // 注释
        rules.add(new SyntaxRule(TokenType.COMMENT, Pattern.compile("#.*$", Pattern.MULTILINE), DefaultColors.COMMENT));
        
        // 字符串
        rules.add(new SyntaxRule(TokenType.STRING, "\"\"\"[\\s\\S]*?\"\"\"", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "'''[\\s\\S]*?'''", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "\"(?:[^\"\\\\]|\\\\.)*\"", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "'(?:[^'\\\\]|\\\\.)*'", DefaultColors.STRING));
        
        // 关键字
        String pythonKeywords = "\\b(and|as|assert|break|class|continue|def|del|elif|else|" +
                               "except|exec|finally|for|from|global|if|import|in|is|" +
                               "lambda|not|or|pass|print|raise|return|try|while|with|yield)\\b";
        rules.add(new SyntaxRule(TokenType.KEYWORD, pythonKeywords, DefaultColors.KEYWORD));
        
        // 内置函数
        String pythonBuiltins = "\\b(abs|all|any|bin|bool|chr|dict|dir|enumerate|eval|" +
                               "filter|float|format|hex|int|len|list|map|max|min|" +
                               "open|ord|range|repr|round|set|sorted|str|sum|tuple|type)\\b";
        rules.add(new SyntaxRule(TokenType.FUNCTION, pythonBuiltins, DefaultColors.FUNCTION));
        
        // 数字
        rules.add(new SyntaxRule(TokenType.NUMBER, "\\b\\d+(\\.\\d+)?\\b", DefaultColors.NUMBER));
        
        // 装饰器
        rules.add(new SyntaxRule(TokenType.ANNOTATION, "@\\w+", DefaultColors.ANNOTATION));
    }

    /**
     * HTML语法规则
     */
    private void addHtmlRules(List<SyntaxRule> rules) {
        // HTML注释
        rules.add(new SyntaxRule(TokenType.COMMENT, "<!--[\\s\\S]*?-->", DefaultColors.COMMENT));
        
        // 标签
        rules.add(new SyntaxRule(TokenType.TAG, "</?\\b[\\w-]+\\b", DefaultColors.TAG));
        rules.add(new SyntaxRule(TokenType.OPERATOR, "[<>/?]", DefaultColors.OPERATOR));
        
        // 属性
        rules.add(new SyntaxRule(TokenType.ATTRIBUTE, "\\b[\\w-]+(?=\\s*=)", DefaultColors.ATTRIBUTE));
        
        // 字符串（属性值）
        rules.add(new SyntaxRule(TokenType.STRING, "\"[^\"]*\"", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "'[^']*'", DefaultColors.STRING));
    }

    /**
     * CSS语法规则
     */
    private void addCssRules(List<SyntaxRule> rules) {
        // CSS注释
        rules.add(new SyntaxRule(TokenType.COMMENT, "/\\*[\\s\\S]*?\\*/", DefaultColors.COMMENT));
        
        // 选择器
        rules.add(new SyntaxRule(TokenType.TAG, "[.#]?[\\w-]+(?=\\s*\\{)", DefaultColors.TAG));
        
        // 属性名
        rules.add(new SyntaxRule(TokenType.ATTRIBUTE, "[\\w-]+(?=\\s*:)", DefaultColors.ATTRIBUTE));
        
        // 字符串
        rules.add(new SyntaxRule(TokenType.STRING, "\"[^\"]*\"", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "'[^']*'", DefaultColors.STRING));
        
        // 数字和单位
        rules.add(new SyntaxRule(TokenType.NUMBER, "\\b\\d+(\\.\\d+)?(px|em|rem|%|vh|vw|pt)?\\b", DefaultColors.NUMBER));
    }

    /**
     * XML语法规则
     */
    private void addXmlRules(List<SyntaxRule> rules) {
        // XML声明和注释
        rules.add(new SyntaxRule(TokenType.COMMENT, "<!--[\\s\\S]*?-->", DefaultColors.COMMENT));
        rules.add(new SyntaxRule(TokenType.COMMENT, "<\\?[\\s\\S]*?\\?>", DefaultColors.COMMENT));
        
        // 标签
        rules.add(new SyntaxRule(TokenType.TAG, "</?\\b[\\w:-]+\\b", DefaultColors.TAG));
        
        // 属性
        rules.add(new SyntaxRule(TokenType.ATTRIBUTE, "\\b[\\w:-]+(?=\\s*=)", DefaultColors.ATTRIBUTE));
        
        // 字符串
        rules.add(new SyntaxRule(TokenType.STRING, "\"[^\"]*\"", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "'[^']*'", DefaultColors.STRING));
    }

    /**
     * JSON语法规则
     */
    private void addJsonRules(List<SyntaxRule> rules) {
        // 字符串（键和值）
        rules.add(new SyntaxRule(TokenType.STRING, "\"[^\"]*\"", DefaultColors.STRING));
        
        // 数字
        rules.add(new SyntaxRule(TokenType.NUMBER, "-?\\b\\d+(\\.\\d+)?([eE][+-]?\\d+)?\\b", DefaultColors.NUMBER));
        
        // 布尔值和null
        rules.add(new SyntaxRule(TokenType.KEYWORD, "\\b(true|false|null)\\b", DefaultColors.KEYWORD));
    }

    /**
     * Markdown语法规则
     */
    private void addMarkdownRules(List<SyntaxRule> rules) {
        // 标题 - 支持 # ## ### #### ##### ######
        rules.add(new SyntaxRule(TokenType.TAG, Pattern.compile("^#{1,6}\\s*.*$", Pattern.MULTILINE), DefaultColors.TAG));

        // 代码块
        rules.add(new SyntaxRule(TokenType.STRING, "```[\\s\\S]*?```", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "`[^`]*`", DefaultColors.STRING));

        // 链接
        rules.add(new SyntaxRule(TokenType.FUNCTION, "\\[[^\\]]*\\]\\([^\\)]*\\)", DefaultColors.FUNCTION));

        // 粗体和斜体
        rules.add(new SyntaxRule(TokenType.KEYWORD, "\\*\\*[^*]*\\*\\*", DefaultColors.KEYWORD));
        rules.add(new SyntaxRule(TokenType.ANNOTATION, "\\*[^*]*\\*", DefaultColors.ANNOTATION));

        // 引用块
        rules.add(new SyntaxRule(TokenType.COMMENT, Pattern.compile("^>.*$", Pattern.MULTILINE), DefaultColors.COMMENT));

        // 列表项
        rules.add(new SyntaxRule(TokenType.OPERATOR, Pattern.compile("^\\s*[-*+]\\s", Pattern.MULTILINE), DefaultColors.OPERATOR));
        rules.add(new SyntaxRule(TokenType.OPERATOR, Pattern.compile("^\\s*\\d+\\.\\s", Pattern.MULTILINE), DefaultColors.OPERATOR));

        // 水平分割线
        rules.add(new SyntaxRule(TokenType.OPERATOR, Pattern.compile("^\\s*[-*_]{3,}\\s*$", Pattern.MULTILINE), DefaultColors.OPERATOR));

        // 表格分隔符
        rules.add(new SyntaxRule(TokenType.OPERATOR, "\\|", DefaultColors.OPERATOR));

        // 删除线
        rules.add(new SyntaxRule(TokenType.COMMENT, "~~[^~]*~~", DefaultColors.COMMENT));
    }

    /**
     * YAML语法规则
     */
    private void addYamlRules(List<SyntaxRule> rules) {
        // 注释
        rules.add(new SyntaxRule(TokenType.COMMENT, Pattern.compile("#.*$", Pattern.MULTILINE), DefaultColors.COMMENT));

        // 键（冒号前的部分）
        rules.add(new SyntaxRule(TokenType.ATTRIBUTE, "^\\s*[\\w-]+(?=\\s*:)", DefaultColors.ATTRIBUTE));

        // 字符串
        rules.add(new SyntaxRule(TokenType.STRING, "\"[^\"]*\"", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "'[^']*'", DefaultColors.STRING));

        // 布尔值和null
        rules.add(new SyntaxRule(TokenType.KEYWORD, "\\b(true|false|null|yes|no|on|off)\\b", DefaultColors.KEYWORD));

        // 数字
        rules.add(new SyntaxRule(TokenType.NUMBER, "\\b\\d+(\\.\\d+)?\\b", DefaultColors.NUMBER));

        // YAML特殊符号
        rules.add(new SyntaxRule(TokenType.OPERATOR, "[\\[\\]{}|>-]", DefaultColors.OPERATOR));
    }

    /**
     * Properties语法规则
     */
    private void addPropertiesRules(List<SyntaxRule> rules) {
        // 注释
        rules.add(new SyntaxRule(TokenType.COMMENT, Pattern.compile("#.*$", Pattern.MULTILINE), DefaultColors.COMMENT));
        rules.add(new SyntaxRule(TokenType.COMMENT, Pattern.compile("!.*$", Pattern.MULTILINE), DefaultColors.COMMENT));

        // 键（等号或冒号前的部分）
        rules.add(new SyntaxRule(TokenType.ATTRIBUTE, "^\\s*[\\w.-]+(?=\\s*[=:])", DefaultColors.ATTRIBUTE));

        // 字符串值
        rules.add(new SyntaxRule(TokenType.STRING, "=\\s*.*$", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, ":\\s*.*$", DefaultColors.STRING));
    }

    /**
     * Shell脚本语法规则
     */
    private void addShellRules(List<SyntaxRule> rules) {
        // 注释
        rules.add(new SyntaxRule(TokenType.COMMENT, Pattern.compile("#.*$", Pattern.MULTILINE), DefaultColors.COMMENT));

        // Shebang
        rules.add(new SyntaxRule(TokenType.COMMENT, "^#!/.*$", DefaultColors.COMMENT));

        // 关键字
        String shellKeywords = "\\b(if|then|else|elif|fi|for|while|do|done|case|esac|function|" +
                              "return|exit|break|continue|local|export|readonly|unset|shift|" +
                              "test|echo|printf|read|cd|pwd|ls|cp|mv|rm|mkdir|chmod|chown)\\b";
        rules.add(new SyntaxRule(TokenType.KEYWORD, shellKeywords, DefaultColors.KEYWORD));

        // 变量
        rules.add(new SyntaxRule(TokenType.FUNCTION, "\\$\\{?\\w+\\}?", DefaultColors.FUNCTION));

        // 字符串
        rules.add(new SyntaxRule(TokenType.STRING, "\"(?:[^\"\\\\]|\\\\.)*\"", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "'[^']*'", DefaultColors.STRING));

        // 数字
        rules.add(new SyntaxRule(TokenType.NUMBER, "\\b\\d+\\b", DefaultColors.NUMBER));

        // 操作符
        rules.add(new SyntaxRule(TokenType.OPERATOR, "[|&;()<>{}\\[\\]]", DefaultColors.OPERATOR));
    }

    /**
     * SQL语法规则
     */
    private void addSqlRules(List<SyntaxRule> rules) {
        // 注释
        rules.add(new SyntaxRule(TokenType.COMMENT, Pattern.compile("--.*$", Pattern.MULTILINE), DefaultColors.COMMENT));
        rules.add(new SyntaxRule(TokenType.COMMENT, "/\\*[\\s\\S]*?\\*/", DefaultColors.COMMENT));

        // SQL关键字
        String sqlKeywords = "\\b(?i)(SELECT|FROM|WHERE|INSERT|UPDATE|DELETE|CREATE|DROP|ALTER|" +
                            "TABLE|INDEX|VIEW|DATABASE|SCHEMA|PRIMARY|KEY|FOREIGN|REFERENCES|" +
                            "CONSTRAINT|NOT|NULL|UNIQUE|DEFAULT|AUTO_INCREMENT|" +
                            "JOIN|INNER|LEFT|RIGHT|OUTER|ON|GROUP|BY|ORDER|HAVING|" +
                            "UNION|DISTINCT|AS|CASE|WHEN|THEN|ELSE|END|" +
                            "AND|OR|IN|EXISTS|BETWEEN|LIKE|IS|" +
                            "COUNT|SUM|AVG|MAX|MIN|LIMIT|OFFSET)\\b";
        rules.add(new SyntaxRule(TokenType.KEYWORD, sqlKeywords, DefaultColors.KEYWORD));

        // 数据类型
        String sqlTypes = "\\b(?i)(INT|INTEGER|BIGINT|SMALLINT|TINYINT|" +
                         "VARCHAR|CHAR|TEXT|LONGTEXT|" +
                         "DECIMAL|NUMERIC|FLOAT|DOUBLE|REAL|" +
                         "DATE|TIME|DATETIME|TIMESTAMP|YEAR|" +
                         "BOOLEAN|BOOL|BIT)\\b";
        rules.add(new SyntaxRule(TokenType.TYPE, sqlTypes, DefaultColors.TYPE));

        // 字符串
        rules.add(new SyntaxRule(TokenType.STRING, "'(?:[^'\\\\]|\\\\.)*'", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "\"(?:[^\"\\\\]|\\\\.)*\"", DefaultColors.STRING));

        // 数字
        rules.add(new SyntaxRule(TokenType.NUMBER, "\\b\\d+(\\.\\d+)?\\b", DefaultColors.NUMBER));

        // 函数
        rules.add(new SyntaxRule(TokenType.FUNCTION, "\\b\\w+(?=\\s*\\()", DefaultColors.FUNCTION));
    }

    /**
     * C语法规则
     */
    private void addCRules(List<SyntaxRule> rules) {
        // 注释
        rules.add(new SyntaxRule(TokenType.COMMENT, Pattern.compile("//.*$", Pattern.MULTILINE), DefaultColors.COMMENT));
        rules.add(new SyntaxRule(TokenType.COMMENT, "/\\*[\\s\\S]*?\\*/", DefaultColors.COMMENT));

        // 预处理指令
        rules.add(new SyntaxRule(TokenType.ANNOTATION, Pattern.compile("^\\s*#\\w+.*$", Pattern.MULTILINE), DefaultColors.ANNOTATION));

        // 字符串和字符
        rules.add(new SyntaxRule(TokenType.STRING, "\"(?:[^\"\\\\]|\\\\.)*\"", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "'(?:[^'\\\\]|\\\\.)*'", DefaultColors.STRING));

        // C关键字
        String cKeywords = "\\b(auto|break|case|char|const|continue|default|do|double|else|enum|" +
                          "extern|float|for|goto|if|int|long|register|return|short|signed|" +
                          "sizeof|static|struct|switch|typedef|union|unsigned|void|volatile|while)\\b";
        rules.add(new SyntaxRule(TokenType.KEYWORD, cKeywords, DefaultColors.KEYWORD));

        // 数据类型
        String cTypes = "\\b(int|char|float|double|void|short|long|signed|unsigned|" +
                       "size_t|FILE|NULL|bool|true|false)\\b";
        rules.add(new SyntaxRule(TokenType.TYPE, cTypes, DefaultColors.TYPE));

        // 数字
        rules.add(new SyntaxRule(TokenType.NUMBER, "\\b\\d+(\\.\\d+)?[fFlL]?\\b", DefaultColors.NUMBER));
        rules.add(new SyntaxRule(TokenType.NUMBER, "\\b0[xX][0-9a-fA-F]+[lL]?\\b", DefaultColors.NUMBER));

        // 函数调用
        rules.add(new SyntaxRule(TokenType.FUNCTION, "\\b\\w+(?=\\s*\\()", DefaultColors.FUNCTION));

        // 操作符
        rules.add(new SyntaxRule(TokenType.OPERATOR, "[+\\-*/%=<>!&|^~?:;,.]", DefaultColors.OPERATOR));
    }

    /**
     * C++语法规则
     */
    private void addCppRules(List<SyntaxRule> rules) {
        // 先添加C的规则
        addCRules(rules);

        // C++特有关键字
        String cppKeywords = "\\b(class|public|private|protected|virtual|override|final|" +
                            "new|delete|this|friend|inline|template|typename|namespace|" +
                            "using|try|catch|throw|operator|explicit|mutable|" +
                            "constexpr|decltype|auto|nullptr|static_assert)\\b";
        rules.add(new SyntaxRule(TokenType.KEYWORD, cppKeywords, DefaultColors.KEYWORD));

        // C++标准库类型
        String cppTypes = "\\b(string|vector|list|map|set|pair|shared_ptr|unique_ptr|" +
                         "iostream|istream|ostream|ifstream|ofstream|stringstream)\\b";
        rules.add(new SyntaxRule(TokenType.TYPE, cppTypes, DefaultColors.TYPE));

        // 命名空间
        rules.add(new SyntaxRule(TokenType.ANNOTATION, "\\b\\w+::", DefaultColors.ANNOTATION));
    }

    /**
     * Go语法规则
     */
    private void addGoRules(List<SyntaxRule> rules) {
        // 注释
        rules.add(new SyntaxRule(TokenType.COMMENT, Pattern.compile("//.*$", Pattern.MULTILINE), DefaultColors.COMMENT));
        rules.add(new SyntaxRule(TokenType.COMMENT, "/\\*[\\s\\S]*?\\*/", DefaultColors.COMMENT));

        // 字符串
        rules.add(new SyntaxRule(TokenType.STRING, "\"(?:[^\"\\\\]|\\\\.)*\"", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "'(?:[^'\\\\]|\\\\.)*'", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "`[^`]*`", DefaultColors.STRING)); // 原始字符串

        // Go关键字
        String goKeywords = "\\b(break|case|chan|const|continue|default|defer|else|fallthrough|" +
                           "for|func|go|goto|if|import|interface|map|package|range|return|" +
                           "select|struct|switch|type|var)\\b";
        rules.add(new SyntaxRule(TokenType.KEYWORD, goKeywords, DefaultColors.KEYWORD));

        // Go内置类型
        String goTypes = "\\b(bool|byte|complex64|complex128|error|float32|float64|" +
                        "int|int8|int16|int32|int64|rune|string|" +
                        "uint|uint8|uint16|uint32|uint64|uintptr)\\b";
        rules.add(new SyntaxRule(TokenType.TYPE, goTypes, DefaultColors.TYPE));

        // 内置函数
        String goBuiltins = "\\b(append|cap|close|complex|copy|delete|imag|len|make|new|" +
                           "panic|print|println|real|recover)\\b";
        rules.add(new SyntaxRule(TokenType.FUNCTION, goBuiltins, DefaultColors.FUNCTION));

        // 数字
        rules.add(new SyntaxRule(TokenType.NUMBER, "\\b\\d+(\\.\\d+)?([eE][+-]?\\d+)?\\b", DefaultColors.NUMBER));
        rules.add(new SyntaxRule(TokenType.NUMBER, "\\b0[xX][0-9a-fA-F]+\\b", DefaultColors.NUMBER));

        // 函数调用
        rules.add(new SyntaxRule(TokenType.FUNCTION, "\\b\\w+(?=\\s*\\()", DefaultColors.FUNCTION));
    }

    /**
     * Kotlin语法规则
     */
    private void addKotlinRules(List<SyntaxRule> rules) {
        // 注释
        rules.add(new SyntaxRule(TokenType.COMMENT, Pattern.compile("//.*$", Pattern.MULTILINE), DefaultColors.COMMENT));
        rules.add(new SyntaxRule(TokenType.COMMENT, "/\\*[\\s\\S]*?\\*/", DefaultColors.COMMENT));

        // 字符串
        rules.add(new SyntaxRule(TokenType.STRING, "\"(?:[^\"\\\\]|\\\\.)*\"", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "'(?:[^'\\\\]|\\\\.)*'", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "\"\"\"[\\s\\S]*?\"\"\"", DefaultColors.STRING)); // 多行字符串

        // Kotlin关键字
        String kotlinKeywords = "\\b(abstract|actual|annotation|as|break|by|catch|class|companion|" +
                               "const|constructor|continue|crossinline|data|do|dynamic|else|enum|" +
                               "expect|external|final|finally|for|fun|get|if|import|in|infix|" +
                               "init|inline|inner|interface|internal|is|lateinit|noinline|null|" +
                               "object|open|operator|out|override|package|private|protected|" +
                               "public|reified|return|sealed|set|super|suspend|tailrec|this|" +
                               "throw|try|typealias|typeof|val|var|vararg|when|where|while)\\b";
        rules.add(new SyntaxRule(TokenType.KEYWORD, kotlinKeywords, DefaultColors.KEYWORD));

        // 注解
        rules.add(new SyntaxRule(TokenType.ANNOTATION, "@\\w+", DefaultColors.ANNOTATION));

        // 数字
        rules.add(new SyntaxRule(TokenType.NUMBER, "\\b\\d+(\\.\\d+)?[fFdDlL]?\\b", DefaultColors.NUMBER));
        rules.add(new SyntaxRule(TokenType.NUMBER, "\\b0[xX][0-9a-fA-F]+[lL]?\\b", DefaultColors.NUMBER));

        // 函数调用
        rules.add(new SyntaxRule(TokenType.FUNCTION, "\\b\\w+(?=\\s*\\()", DefaultColors.FUNCTION));
    }

    /**
     * Swift语法规则
     */
    private void addSwiftRules(List<SyntaxRule> rules) {
        // 注释
        rules.add(new SyntaxRule(TokenType.COMMENT, Pattern.compile("//.*$", Pattern.MULTILINE), DefaultColors.COMMENT));
        rules.add(new SyntaxRule(TokenType.COMMENT, "/\\*[\\s\\S]*?\\*/", DefaultColors.COMMENT));

        // 字符串
        rules.add(new SyntaxRule(TokenType.STRING, "\"(?:[^\"\\\\]|\\\\.)*\"", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "\"\"\"[\\s\\S]*?\"\"\"", DefaultColors.STRING)); // 多行字符串

        // Swift关键字
        String swiftKeywords = "\\b(associatedtype|class|deinit|enum|extension|fileprivate|func|" +
                              "import|init|inout|internal|let|open|operator|private|protocol|" +
                              "public|static|struct|subscript|typealias|var|break|case|continue|" +
                              "default|defer|do|else|fallthrough|for|guard|if|in|repeat|return|" +
                              "switch|where|while|as|catch|false|is|nil|rethrows|super|self|" +
                              "Self|throw|throws|true|try|_)\\b";
        rules.add(new SyntaxRule(TokenType.KEYWORD, swiftKeywords, DefaultColors.KEYWORD));

        // Swift类型
        String swiftTypes = "\\b(Int|Int8|Int16|Int32|Int64|UInt|UInt8|UInt16|UInt32|UInt64|" +
                           "Float|Double|Bool|String|Character|Array|Dictionary|Set|Optional)\\b";
        rules.add(new SyntaxRule(TokenType.TYPE, swiftTypes, DefaultColors.TYPE));

        // 属性包装器和注解
        rules.add(new SyntaxRule(TokenType.ANNOTATION, "@\\w+", DefaultColors.ANNOTATION));

        // 数字
        rules.add(new SyntaxRule(TokenType.NUMBER, "\\b\\d+(\\.\\d+)?\\b", DefaultColors.NUMBER));
        rules.add(new SyntaxRule(TokenType.NUMBER, "\\b0[xX][0-9a-fA-F]+\\b", DefaultColors.NUMBER));

        // 函数调用
        rules.add(new SyntaxRule(TokenType.FUNCTION, "\\b\\w+(?=\\s*\\()", DefaultColors.FUNCTION));
    }

    /**
     * Rust语法规则
     */
    private void addRustRules(List<SyntaxRule> rules) {
        // 注释
        rules.add(new SyntaxRule(TokenType.COMMENT, Pattern.compile("//.*$", Pattern.MULTILINE), DefaultColors.COMMENT));
        rules.add(new SyntaxRule(TokenType.COMMENT, "/\\*[\\s\\S]*?\\*/", DefaultColors.COMMENT));

        // 字符串
        rules.add(new SyntaxRule(TokenType.STRING, "\"(?:[^\"\\\\]|\\\\.)*\"", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "'(?:[^'\\\\]|\\\\.)*'", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "r#\"[\\s\\S]*?\"#", DefaultColors.STRING)); // 原始字符串

        // Rust关键字
        String rustKeywords = "\\b(as|async|await|break|const|continue|crate|dyn|else|enum|extern|" +
                             "false|fn|for|if|impl|in|let|loop|match|mod|move|mut|pub|ref|" +
                             "return|self|Self|static|struct|super|trait|true|type|unsafe|" +
                             "use|where|while|abstract|become|box|do|final|macro|override|" +
                             "priv|typeof|unsized|virtual|yield)\\b";
        rules.add(new SyntaxRule(TokenType.KEYWORD, rustKeywords, DefaultColors.KEYWORD));

        // Rust类型
        String rustTypes = "\\b(i8|i16|i32|i64|i128|isize|u8|u16|u32|u64|u128|usize|" +
                          "f32|f64|bool|char|str|String|Vec|HashMap|HashSet|Option|Result)\\b";
        rules.add(new SyntaxRule(TokenType.TYPE, rustTypes, DefaultColors.TYPE));

        // 宏调用
        rules.add(new SyntaxRule(TokenType.FUNCTION, "\\b\\w+!", DefaultColors.FUNCTION));

        // 属性
        rules.add(new SyntaxRule(TokenType.ANNOTATION, "#\\[.*?\\]", DefaultColors.ANNOTATION));

        // 数字
        rules.add(new SyntaxRule(TokenType.NUMBER, "\\b\\d+(\\.\\d+)?([eE][+-]?\\d+)?[fFdDlL]?\\b", DefaultColors.NUMBER));
        rules.add(new SyntaxRule(TokenType.NUMBER, "\\b0[xX][0-9a-fA-F]+\\b", DefaultColors.NUMBER));

        // 函数调用
        rules.add(new SyntaxRule(TokenType.FUNCTION, "\\b\\w+(?=\\s*\\()", DefaultColors.FUNCTION));
    }

    /**
     * PHP语法规则
     */
    private void addPhpRules(List<SyntaxRule> rules) {
        // 注释
        rules.add(new SyntaxRule(TokenType.COMMENT, Pattern.compile("//.*$", Pattern.MULTILINE), DefaultColors.COMMENT));
        rules.add(new SyntaxRule(TokenType.COMMENT, Pattern.compile("#.*$", Pattern.MULTILINE), DefaultColors.COMMENT));
        rules.add(new SyntaxRule(TokenType.COMMENT, "/\\*[\\s\\S]*?\\*/", DefaultColors.COMMENT));

        // PHP标签
        rules.add(new SyntaxRule(TokenType.ANNOTATION, "<\\?php", DefaultColors.ANNOTATION));
        rules.add(new SyntaxRule(TokenType.ANNOTATION, "\\?>", DefaultColors.ANNOTATION));

        // 字符串
        rules.add(new SyntaxRule(TokenType.STRING, "\"(?:[^\"\\\\]|\\\\.)*\"", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "'(?:[^'\\\\]|\\\\.)*'", DefaultColors.STRING));

        // PHP关键字
        String phpKeywords = "\\b(abstract|and|array|as|break|callable|case|catch|class|clone|" +
                            "const|continue|declare|default|die|do|echo|else|elseif|empty|" +
                            "enddeclare|endfor|endforeach|endif|endswitch|endwhile|eval|exit|" +
                            "extends|final|finally|for|foreach|function|global|goto|if|" +
                            "implements|include|include_once|instanceof|insteadof|interface|" +
                            "isset|list|namespace|new|or|print|private|protected|public|" +
                            "require|require_once|return|static|switch|throw|trait|try|" +
                            "unset|use|var|while|xor|yield)\\b";
        rules.add(new SyntaxRule(TokenType.KEYWORD, phpKeywords, DefaultColors.KEYWORD));

        // 变量
        rules.add(new SyntaxRule(TokenType.FUNCTION, "\\$\\w+", DefaultColors.FUNCTION));

        // 数字
        rules.add(new SyntaxRule(TokenType.NUMBER, "\\b\\d+(\\.\\d+)?\\b", DefaultColors.NUMBER));

        // 函数调用
        rules.add(new SyntaxRule(TokenType.FUNCTION, "\\b\\w+(?=\\s*\\()", DefaultColors.FUNCTION));
    }

    /**
     * Ruby语法规则
     */
    private void addRubyRules(List<SyntaxRule> rules) {
        // 注释
        rules.add(new SyntaxRule(TokenType.COMMENT, Pattern.compile("#.*$", Pattern.MULTILINE), DefaultColors.COMMENT));

        // 字符串
        rules.add(new SyntaxRule(TokenType.STRING, "\"(?:[^\"\\\\]|\\\\.)*\"", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "'(?:[^'\\\\]|\\\\.)*'", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "%[qQrwWxis]?[^\\w\\s][\\s\\S]*?[^\\w\\s]", DefaultColors.STRING));

        // Ruby关键字
        String rubyKeywords = "\\b(alias|and|begin|break|case|class|def|defined|do|else|elsif|" +
                             "end|ensure|false|for|if|in|module|next|nil|not|or|redo|rescue|" +
                             "retry|return|self|super|then|true|undef|unless|until|when|while|" +
                             "yield|__FILE__|__LINE__|BEGIN|END)\\b";
        rules.add(new SyntaxRule(TokenType.KEYWORD, rubyKeywords, DefaultColors.KEYWORD));

        // 实例变量和类变量
        rules.add(new SyntaxRule(TokenType.FUNCTION, "@\\w+", DefaultColors.FUNCTION));
        rules.add(new SyntaxRule(TokenType.FUNCTION, "@@\\w+", DefaultColors.FUNCTION));

        // 全局变量
        rules.add(new SyntaxRule(TokenType.FUNCTION, "\\$\\w+", DefaultColors.FUNCTION));

        // 符号
        rules.add(new SyntaxRule(TokenType.ANNOTATION, ":\\w+", DefaultColors.ANNOTATION));

        // 数字
        rules.add(new SyntaxRule(TokenType.NUMBER, "\\b\\d+(\\.\\d+)?\\b", DefaultColors.NUMBER));

        // 函数调用
        rules.add(new SyntaxRule(TokenType.FUNCTION, "\\b\\w+(?=\\s*\\()", DefaultColors.FUNCTION));
    }

    /**
     * TypeScript语法规则
     */
    private void addTypeScriptRules(List<SyntaxRule> rules) {
        // 先添加JavaScript的规则
        addJavaScriptRules(rules);

        // TypeScript特有关键字
        String tsKeywords = "\\b(abstract|any|as|asserts|bigint|boolean|constructor|declare|" +
                           "enum|implements|infer|interface|is|keyof|namespace|never|" +
                           "number|object|private|protected|public|readonly|require|" +
                           "string|symbol|type|undefined|unique|unknown|void)\\b";
        rules.add(new SyntaxRule(TokenType.KEYWORD, tsKeywords, DefaultColors.KEYWORD));

        // 类型注解
        rules.add(new SyntaxRule(TokenType.TYPE, ":\\s*\\w+", DefaultColors.TYPE));

        // 泛型
        rules.add(new SyntaxRule(TokenType.TYPE, "<[\\w,\\s]+>", DefaultColors.TYPE));

        // 装饰器
        rules.add(new SyntaxRule(TokenType.ANNOTATION, "@\\w+", DefaultColors.ANNOTATION));
    }

    /**
     * C#语法规则
     */
    private void addCSharpRules(List<SyntaxRule> rules) {
        // 注释
        rules.add(new SyntaxRule(TokenType.COMMENT, Pattern.compile("//.*$", Pattern.MULTILINE), DefaultColors.COMMENT));
        rules.add(new SyntaxRule(TokenType.COMMENT, "/\\*[\\s\\S]*?\\*/", DefaultColors.COMMENT));

        // 字符串
        rules.add(new SyntaxRule(TokenType.STRING, "\"(?:[^\"\\\\]|\\\\.)*\"", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "'(?:[^'\\\\]|\\\\.)*'", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "@\"[^\"]*\"", DefaultColors.STRING)); // 逐字字符串

        // C#关键字
        String csharpKeywords = "\\b(abstract|as|base|bool|break|byte|case|catch|char|checked|" +
                               "class|const|continue|decimal|default|delegate|do|double|else|" +
                               "enum|event|explicit|extern|false|finally|fixed|float|for|" +
                               "foreach|goto|if|implicit|in|int|interface|internal|is|lock|" +
                               "long|namespace|new|null|object|operator|out|override|params|" +
                               "private|protected|public|readonly|ref|return|sbyte|sealed|" +
                               "short|sizeof|stackalloc|static|string|struct|switch|this|" +
                               "throw|true|try|typeof|uint|ulong|unchecked|unsafe|ushort|" +
                               "using|virtual|void|volatile|while)\\b";
        rules.add(new SyntaxRule(TokenType.KEYWORD, csharpKeywords, DefaultColors.KEYWORD));

        // 属性
        rules.add(new SyntaxRule(TokenType.ANNOTATION, "\\[.*?\\]", DefaultColors.ANNOTATION));

        // 数字
        rules.add(new SyntaxRule(TokenType.NUMBER, "\\b\\d+(\\.\\d+)?[fFdDmM]?\\b", DefaultColors.NUMBER));
        rules.add(new SyntaxRule(TokenType.NUMBER, "\\b0[xX][0-9a-fA-F]+[lL]?\\b", DefaultColors.NUMBER));

        // 函数调用
        rules.add(new SyntaxRule(TokenType.FUNCTION, "\\b\\w+(?=\\s*\\()", DefaultColors.FUNCTION));
    }

    /**
     * Dart语法规则
     */
    private void addDartRules(List<SyntaxRule> rules) {
        // 注释
        rules.add(new SyntaxRule(TokenType.COMMENT, Pattern.compile("//.*$", Pattern.MULTILINE), DefaultColors.COMMENT));
        rules.add(new SyntaxRule(TokenType.COMMENT, "/\\*[\\s\\S]*?\\*/", DefaultColors.COMMENT));

        // 字符串
        rules.add(new SyntaxRule(TokenType.STRING, "\"(?:[^\"\\\\]|\\\\.)*\"", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "'(?:[^'\\\\]|\\\\.)*'", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "\"\"\"[\\s\\S]*?\"\"\"", DefaultColors.STRING)); // 多行字符串

        // Dart关键字
        String dartKeywords = "\\b(abstract|as|assert|async|await|break|case|catch|class|const|" +
                             "continue|covariant|default|deferred|do|dynamic|else|enum|export|" +
                             "extends|external|factory|false|final|finally|for|Function|get|" +
                             "hide|if|implements|import|in|interface|is|library|mixin|new|" +
                             "null|on|operator|part|rethrow|return|set|show|static|super|" +
                             "switch|sync|this|throw|true|try|typedef|var|void|while|with|yield)\\b";
        rules.add(new SyntaxRule(TokenType.KEYWORD, dartKeywords, DefaultColors.KEYWORD));

        // Dart类型
        String dartTypes = "\\b(int|double|num|String|bool|List|Map|Set|Object|dynamic|void)\\b";
        rules.add(new SyntaxRule(TokenType.TYPE, dartTypes, DefaultColors.TYPE));

        // 注解
        rules.add(new SyntaxRule(TokenType.ANNOTATION, "@\\w+", DefaultColors.ANNOTATION));

        // 数字
        rules.add(new SyntaxRule(TokenType.NUMBER, "\\b\\d+(\\.\\d+)?\\b", DefaultColors.NUMBER));
        rules.add(new SyntaxRule(TokenType.NUMBER, "\\b0[xX][0-9a-fA-F]+\\b", DefaultColors.NUMBER));

        // 函数调用
        rules.add(new SyntaxRule(TokenType.FUNCTION, "\\b\\w+(?=\\s*\\()", DefaultColors.FUNCTION));
    }

    /**
     * Scala语法规则
     */
    private void addScalaRules(List<SyntaxRule> rules) {
        // 注释
        rules.add(new SyntaxRule(TokenType.COMMENT, Pattern.compile("//.*$", Pattern.MULTILINE), DefaultColors.COMMENT));
        rules.add(new SyntaxRule(TokenType.COMMENT, "/\\*[\\s\\S]*?\\*/", DefaultColors.COMMENT));

        // 字符串
        rules.add(new SyntaxRule(TokenType.STRING, "\"(?:[^\"\\\\]|\\\\.)*\"", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "'(?:[^'\\\\]|\\\\.)*'", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "\"\"\"[\\s\\S]*?\"\"\"", DefaultColors.STRING)); // 多行字符串

        // Scala关键字
        String scalaKeywords = "\\b(abstract|case|catch|class|def|do|else|extends|false|final|" +
                              "finally|for|forSome|if|implicit|import|lazy|match|new|null|" +
                              "object|override|package|private|protected|return|sealed|super|" +
                              "this|throw|trait|try|true|type|val|var|while|with|yield)\\b";
        rules.add(new SyntaxRule(TokenType.KEYWORD, scalaKeywords, DefaultColors.KEYWORD));

        // Scala类型
        String scalaTypes = "\\b(Any|AnyRef|AnyVal|Boolean|Byte|Char|Double|Float|Int|Long|" +
                           "Nothing|Null|Object|Short|String|Unit|List|Array|Map|Set|Option)\\b";
        rules.add(new SyntaxRule(TokenType.TYPE, scalaTypes, DefaultColors.TYPE));

        // 注解
        rules.add(new SyntaxRule(TokenType.ANNOTATION, "@\\w+", DefaultColors.ANNOTATION));

        // 数字
        rules.add(new SyntaxRule(TokenType.NUMBER, "\\b\\d+(\\.\\d+)?[fFdDlL]?\\b", DefaultColors.NUMBER));

        // 函数调用
        rules.add(new SyntaxRule(TokenType.FUNCTION, "\\b\\w+(?=\\s*\\()", DefaultColors.FUNCTION));
    }

    /**
     * Perl语法规则
     */
    private void addPerlRules(List<SyntaxRule> rules) {
        // 注释
        rules.add(new SyntaxRule(TokenType.COMMENT, Pattern.compile("#.*$", Pattern.MULTILINE), DefaultColors.COMMENT));

        // 字符串
        rules.add(new SyntaxRule(TokenType.STRING, "\"(?:[^\"\\\\]|\\\\.)*\"", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "'(?:[^'\\\\]|\\\\.)*'", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "q[qwrx]?[^\\w\\s][\\s\\S]*?[^\\w\\s]", DefaultColors.STRING));

        // Perl关键字
        String perlKeywords = "\\b(and|cmp|continue|CORE|do|else|elsif|eq|eval|exit|for|" +
                             "foreach|ge|gt|if|last|le|local|lt|my|ne|next|no|not|or|" +
                             "our|package|redo|require|return|sub|unless|until|use|while|xor)\\b";
        rules.add(new SyntaxRule(TokenType.KEYWORD, perlKeywords, DefaultColors.KEYWORD));

        // 变量
        rules.add(new SyntaxRule(TokenType.FUNCTION, "[\\$@%]\\w+", DefaultColors.FUNCTION));

        // 数字
        rules.add(new SyntaxRule(TokenType.NUMBER, "\\b\\d+(\\.\\d+)?\\b", DefaultColors.NUMBER));

        // 函数调用
        rules.add(new SyntaxRule(TokenType.FUNCTION, "\\b\\w+(?=\\s*\\()", DefaultColors.FUNCTION));
    }

    /**
     * Lua语法规则
     */
    private void addLuaRules(List<SyntaxRule> rules) {
        // 注释
        rules.add(new SyntaxRule(TokenType.COMMENT, Pattern.compile("--.*$", Pattern.MULTILINE), DefaultColors.COMMENT));
        rules.add(new SyntaxRule(TokenType.COMMENT, "--\\[\\[[\\s\\S]*?\\]\\]", DefaultColors.COMMENT));

        // 字符串
        rules.add(new SyntaxRule(TokenType.STRING, "\"(?:[^\"\\\\]|\\\\.)*\"", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "'(?:[^'\\\\]|\\\\.)*'", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "\\[\\[[\\s\\S]*?\\]\\]", DefaultColors.STRING)); // 长字符串

        // Lua关键字
        String luaKeywords = "\\b(and|break|do|else|elseif|end|false|for|function|goto|if|" +
                            "in|local|nil|not|or|repeat|return|then|true|until|while)\\b";
        rules.add(new SyntaxRule(TokenType.KEYWORD, luaKeywords, DefaultColors.KEYWORD));

        // 内置函数
        String luaBuiltins = "\\b(assert|collectgarbage|dofile|error|getmetatable|ipairs|" +
                            "load|loadfile|next|pairs|pcall|print|rawequal|rawget|rawlen|" +
                            "rawset|require|select|setmetatable|tonumber|tostring|type|xpcall)\\b";
        rules.add(new SyntaxRule(TokenType.FUNCTION, luaBuiltins, DefaultColors.FUNCTION));

        // 数字
        rules.add(new SyntaxRule(TokenType.NUMBER, "\\b\\d+(\\.\\d+)?([eE][+-]?\\d+)?\\b", DefaultColors.NUMBER));
        rules.add(new SyntaxRule(TokenType.NUMBER, "\\b0[xX][0-9a-fA-F]+\\b", DefaultColors.NUMBER));

        // 函数调用
        rules.add(new SyntaxRule(TokenType.FUNCTION, "\\b\\w+(?=\\s*\\()", DefaultColors.FUNCTION));
    }

    /**
     * R语法规则
     */
    private void addRRules(List<SyntaxRule> rules) {
        // 注释
        rules.add(new SyntaxRule(TokenType.COMMENT, Pattern.compile("#.*$", Pattern.MULTILINE), DefaultColors.COMMENT));

        // 字符串
        rules.add(new SyntaxRule(TokenType.STRING, "\"(?:[^\"\\\\]|\\\\.)*\"", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "'(?:[^'\\\\]|\\\\.)*'", DefaultColors.STRING));

        // R关键字
        String rKeywords = "\\b(if|else|repeat|while|function|for|in|next|break|TRUE|FALSE|" +
                          "NULL|Inf|NaN|NA|NA_integer_|NA_real_|NA_complex_|NA_character_)\\b";
        rules.add(new SyntaxRule(TokenType.KEYWORD, rKeywords, DefaultColors.KEYWORD));

        // 内置函数
        String rBuiltins = "\\b(c|list|data\\.frame|matrix|array|factor|length|dim|names|" +
                          "class|str|summary|head|tail|print|cat|paste|sprintf|" +
                          "mean|median|sd|var|min|max|sum|sort|order|unique|" +
                          "read\\.csv|write\\.csv|library|require|source|install\\.packages)\\b";
        rules.add(new SyntaxRule(TokenType.FUNCTION, rBuiltins, DefaultColors.FUNCTION));

        // 数字
        rules.add(new SyntaxRule(TokenType.NUMBER, "\\b\\d+(\\.\\d+)?([eE][+-]?\\d+)?[LiI]?\\b", DefaultColors.NUMBER));

        // 赋值操作符
        rules.add(new SyntaxRule(TokenType.OPERATOR, "<-|->|<<-|->>", DefaultColors.OPERATOR));

        // 函数调用
        rules.add(new SyntaxRule(TokenType.FUNCTION, "\\b\\w+(?=\\s*\\()", DefaultColors.FUNCTION));
    }

    /**
     * MATLAB语法规则
     */
    private void addMatlabRules(List<SyntaxRule> rules) {
        // 注释
        rules.add(new SyntaxRule(TokenType.COMMENT, Pattern.compile("%.*$", Pattern.MULTILINE), DefaultColors.COMMENT));

        // 字符串
        rules.add(new SyntaxRule(TokenType.STRING, "'[^']*'", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "\"[^\"]*\"", DefaultColors.STRING));

        // MATLAB关键字
        String matlabKeywords = "\\b(break|case|catch|classdef|continue|else|elseif|end|for|" +
                               "function|global|if|otherwise|parfor|persistent|return|" +
                               "spmd|switch|try|while)\\b";
        rules.add(new SyntaxRule(TokenType.KEYWORD, matlabKeywords, DefaultColors.KEYWORD));

        // 内置函数
        String matlabBuiltins = "\\b(abs|acos|asin|atan|ceil|cos|exp|fix|floor|log|log10|" +
                               "max|min|mod|rand|randn|round|sign|sin|sqrt|tan|" +
                               "size|length|numel|zeros|ones|eye|diag|plot|figure|hold)\\b";
        rules.add(new SyntaxRule(TokenType.FUNCTION, matlabBuiltins, DefaultColors.FUNCTION));

        // 数字
        rules.add(new SyntaxRule(TokenType.NUMBER, "\\b\\d+(\\.\\d+)?([eE][+-]?\\d+)?[ij]?\\b", DefaultColors.NUMBER));

        // 函数调用
        rules.add(new SyntaxRule(TokenType.FUNCTION, "\\b\\w+(?=\\s*\\()", DefaultColors.FUNCTION));
    }

    /**
     * Objective-C语法规则
     */
    private void addObjectiveCRules(List<SyntaxRule> rules) {
        // 先添加C的规则
        addCRules(rules);

        // Objective-C特有关键字
        String objcKeywords = "\\b(@interface|@implementation|@protocol|@property|@synthesize|" +
                             "@dynamic|@selector|@encode|@synchronized|@try|@catch|@finally|" +
                             "@throw|@autoreleasepool|@class|@public|@private|@protected|@package|" +
                             "id|Class|SEL|IMP|BOOL|YES|NO|nil|Nil|self|super)\\b";
        rules.add(new SyntaxRule(TokenType.KEYWORD, objcKeywords, DefaultColors.KEYWORD));

        // 方法调用
        rules.add(new SyntaxRule(TokenType.FUNCTION, "\\[[\\w\\s:]+\\]", DefaultColors.FUNCTION));

        // 属性访问
        rules.add(new SyntaxRule(TokenType.FUNCTION, "\\w+\\.\\w+", DefaultColors.FUNCTION));

        // 字符串字面量
        rules.add(new SyntaxRule(TokenType.STRING, "@\"(?:[^\"\\\\]|\\\\.)*\"", DefaultColors.STRING));
    }

    /**
     * Assembly语法规则
     */
    private void addAssemblyRules(List<SyntaxRule> rules) {
        // 注释
        rules.add(new SyntaxRule(TokenType.COMMENT, Pattern.compile(";.*$", Pattern.MULTILINE), DefaultColors.COMMENT));
        rules.add(new SyntaxRule(TokenType.COMMENT, Pattern.compile("#.*$", Pattern.MULTILINE), DefaultColors.COMMENT));

        // 指令
        String asmInstructions = "\\b(mov|add|sub|mul|div|cmp|jmp|je|jne|jl|jg|call|ret|" +
                                "push|pop|inc|dec|and|or|xor|not|shl|shr|int|nop)\\b";
        rules.add(new SyntaxRule(TokenType.KEYWORD, asmInstructions, DefaultColors.KEYWORD));

        // 寄存器
        String registers = "\\b(eax|ebx|ecx|edx|esi|edi|esp|ebp|ax|bx|cx|dx|al|ah|bl|bh|" +
                          "cl|ch|dl|dh|rax|rbx|rcx|rdx|rsi|rdi|rsp|rbp)\\b";
        rules.add(new SyntaxRule(TokenType.TYPE, registers, DefaultColors.TYPE));

        // 标签
        rules.add(new SyntaxRule(TokenType.FUNCTION, "^\\w+:", DefaultColors.FUNCTION));

        // 数字
        rules.add(new SyntaxRule(TokenType.NUMBER, "\\b\\d+[hH]?\\b", DefaultColors.NUMBER));
        rules.add(new SyntaxRule(TokenType.NUMBER, "\\b0[xX][0-9a-fA-F]+\\b", DefaultColors.NUMBER));

        // 字符串
        rules.add(new SyntaxRule(TokenType.STRING, "\"[^\"]*\"", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "'[^']*'", DefaultColors.STRING));
    }

    /**
     * Bash语法规则（扩展Shell规则）
     */
    private void addBashRules(List<SyntaxRule> rules) {
        // 使用Shell规则作为基础
        addShellRules(rules);

        // Bash特有关键字
        String bashKeywords = "\\b(select|time|until|declare|typeset|let|source|alias|" +
                             "unalias|history|jobs|bg|fg|disown|suspend|compgen|complete|" +
                             "shopt|set|unset|enable|disable|builtin|command|type|which)\\b";
        rules.add(new SyntaxRule(TokenType.KEYWORD, bashKeywords, DefaultColors.KEYWORD));

        // 数组语法
        rules.add(new SyntaxRule(TokenType.FUNCTION, "\\$\\{\\w+\\[[^\\]]*\\]\\}", DefaultColors.FUNCTION));

        // 参数扩展
        rules.add(new SyntaxRule(TokenType.FUNCTION, "\\$\\{[^}]+\\}", DefaultColors.FUNCTION));
    }

    /**
     * PowerShell语法规则
     */
    private void addPowerShellRules(List<SyntaxRule> rules) {
        // 注释
        rules.add(new SyntaxRule(TokenType.COMMENT, Pattern.compile("#.*$", Pattern.MULTILINE), DefaultColors.COMMENT));
        rules.add(new SyntaxRule(TokenType.COMMENT, "<#[\\s\\S]*?#>", DefaultColors.COMMENT));

        // 字符串
        rules.add(new SyntaxRule(TokenType.STRING, "\"(?:[^\"\\\\]|\\\\.)*\"", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "'[^']*'", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "@\"[\\s\\S]*?\"@", DefaultColors.STRING)); // Here-String
        rules.add(new SyntaxRule(TokenType.STRING, "@'[\\s\\S]*?'@", DefaultColors.STRING));

        // PowerShell关键字
        String psKeywords = "\\b(begin|break|catch|class|continue|data|define|do|dynamicparam|" +
                           "else|elseif|end|exit|filter|finally|for|foreach|from|function|" +
                           "if|in|param|process|return|switch|throw|trap|try|until|using|" +
                           "var|while|workflow|parallel|sequence|inlinescript)\\b";
        rules.add(new SyntaxRule(TokenType.KEYWORD, psKeywords, DefaultColors.KEYWORD));

        // Cmdlets
        rules.add(new SyntaxRule(TokenType.FUNCTION, "\\b\\w+-\\w+\\b", DefaultColors.FUNCTION));

        // 变量
        rules.add(new SyntaxRule(TokenType.FUNCTION, "\\$\\w+", DefaultColors.FUNCTION));

        // 参数
        rules.add(new SyntaxRule(TokenType.ANNOTATION, "-\\w+", DefaultColors.ANNOTATION));

        // 数字
        rules.add(new SyntaxRule(TokenType.NUMBER, "\\b\\d+(\\.\\d+)?[dDfFlL]?\\b", DefaultColors.NUMBER));
    }

    /**
     * Batch语法规则
     */
    private void addBatchRules(List<SyntaxRule> rules) {
        // 注释
        rules.add(new SyntaxRule(TokenType.COMMENT, Pattern.compile("REM.*$", Pattern.MULTILINE | Pattern.CASE_INSENSITIVE), DefaultColors.COMMENT));
        rules.add(new SyntaxRule(TokenType.COMMENT, Pattern.compile("::.*$", Pattern.MULTILINE), DefaultColors.COMMENT));

        // Batch关键字
        String batchKeywords = "\\b(?i)(if|else|for|do|while|goto|call|exit|pause|echo|set|" +
                              "setlocal|endlocal|pushd|popd|cd|md|rd|del|copy|move|xcopy|" +
                              "attrib|type|find|findstr|sort|more|cls|date|time|ver)\\b";
        rules.add(new SyntaxRule(TokenType.KEYWORD, batchKeywords, DefaultColors.KEYWORD));

        // 变量
        rules.add(new SyntaxRule(TokenType.FUNCTION, "%\\w+%", DefaultColors.FUNCTION));
        rules.add(new SyntaxRule(TokenType.FUNCTION, "!\\w+!", DefaultColors.FUNCTION));

        // 标签
        rules.add(new SyntaxRule(TokenType.ANNOTATION, "^:\\w+", DefaultColors.ANNOTATION));

        // 字符串
        rules.add(new SyntaxRule(TokenType.STRING, "\"[^\"]*\"", DefaultColors.STRING));

        // 数字
        rules.add(new SyntaxRule(TokenType.NUMBER, "\\b\\d+\\b", DefaultColors.NUMBER));
    }

    /**
     * Visual Basic语法规则
     */
    private void addVBRules(List<SyntaxRule> rules) {
        // 注释
        rules.add(new SyntaxRule(TokenType.COMMENT, Pattern.compile("'.*$", Pattern.MULTILINE), DefaultColors.COMMENT));

        // 字符串
        rules.add(new SyntaxRule(TokenType.STRING, "\"[^\"]*\"", DefaultColors.STRING));

        // VB关键字
        String vbKeywords = "\\b(?i)(and|as|boolean|byref|byte|byval|call|case|catch|cbool|" +
                           "cbyte|cchar|cdate|cdbl|cdec|char|cint|class|clng|cobj|const|" +
                           "continue|csbyte|cshort|csng|cstr|ctype|cuint|culng|cushort|" +
                           "date|decimal|declare|default|delegate|dim|directcast|do|double|" +
                           "each|else|elseif|end|endif|enum|erase|error|event|exit|false|" +
                           "finally|for|friend|function|get|gettype|gosub|goto|handles|if|" +
                           "implements|imports|in|inherits|integer|interface|is|let|lib|" +
                           "like|long|loop|me|mod|module|mustinherit|mustoverride|mybase|" +
                           "myclass|namespace|new|next|not|nothing|notinheritable|" +
                           "notoverridable|object|of|on|option|optional|or|overloads|" +
                           "overridable|overrides|paramarray|preserve|private|property|" +
                           "protected|public|raiseevent|readonly|redim|rem|removehandler|" +
                           "resume|return|select|set|shadows|shared|short|single|static|" +
                           "step|stop|string|structure|sub|synclock|then|throw|to|true|" +
                           "try|typeof|until|variant|when|while|with|withevents|writeonly)\\b";
        rules.add(new SyntaxRule(TokenType.KEYWORD, vbKeywords, DefaultColors.KEYWORD));

        // 数字
        rules.add(new SyntaxRule(TokenType.NUMBER, "\\b\\d+(\\.\\d+)?[fFdDlL]?\\b", DefaultColors.NUMBER));

        // 函数调用
        rules.add(new SyntaxRule(TokenType.FUNCTION, "\\b\\w+(?=\\s*\\()", DefaultColors.FUNCTION));
    }

    /**
     * Haskell语法规则
     */
    private void addHaskellRules(List<SyntaxRule> rules) {
        // 注释
        rules.add(new SyntaxRule(TokenType.COMMENT, Pattern.compile("--.*$", Pattern.MULTILINE), DefaultColors.COMMENT));
        rules.add(new SyntaxRule(TokenType.COMMENT, "\\{-[\\s\\S]*?-\\}", DefaultColors.COMMENT));

        // 字符串
        rules.add(new SyntaxRule(TokenType.STRING, "\"(?:[^\"\\\\]|\\\\.)*\"", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "'(?:[^'\\\\]|\\\\.)*'", DefaultColors.STRING));

        // Haskell关键字
        String haskellKeywords = "\\b(case|class|data|default|deriving|do|else|if|import|in|" +
                                "infix|infixl|infixr|instance|let|module|newtype|of|then|" +
                                "type|where|as|qualified|hiding)\\b";
        rules.add(new SyntaxRule(TokenType.KEYWORD, haskellKeywords, DefaultColors.KEYWORD));

        // 类型
        String haskellTypes = "\\b(Int|Integer|Float|Double|Char|String|Bool|Maybe|Either|" +
                             "IO|Monad|Functor|Applicative)\\b";
        rules.add(new SyntaxRule(TokenType.TYPE, haskellTypes, DefaultColors.TYPE));

        // 数字
        rules.add(new SyntaxRule(TokenType.NUMBER, "\\b\\d+(\\.\\d+)?([eE][+-]?\\d+)?\\b", DefaultColors.NUMBER));

        // 函数调用
        rules.add(new SyntaxRule(TokenType.FUNCTION, "\\b[a-z]\\w*(?=\\s)", DefaultColors.FUNCTION));
    }

    /**
     * Groovy语法规则
     */
    private void addGroovyRules(List<SyntaxRule> rules) {
        // 先添加Java规则作为基础
        addJavaRules(rules);

        // Groovy特有关键字
        String groovyKeywords = "\\b(as|def|in|trait|it)\\b";
        rules.add(new SyntaxRule(TokenType.KEYWORD, groovyKeywords, DefaultColors.KEYWORD));

        // GString
        rules.add(new SyntaxRule(TokenType.STRING, "\"(?:[^\"\\\\$]|\\\\.|\\$\\{[^}]*\\}|\\$\\w+)*\"", DefaultColors.STRING));

        // 多行字符串
        rules.add(new SyntaxRule(TokenType.STRING, "\"\"\"[\\s\\S]*?\"\"\"", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "'''[\\s\\S]*?'''", DefaultColors.STRING));

        // 闭包
        rules.add(new SyntaxRule(TokenType.FUNCTION, "\\{[^}]*->", DefaultColors.FUNCTION));
    }

    /**
     * Dockerfile语法规则
     */
    private void addDockerfileRules(List<SyntaxRule> rules) {
        // 注释
        rules.add(new SyntaxRule(TokenType.COMMENT, Pattern.compile("#.*$", Pattern.MULTILINE), DefaultColors.COMMENT));

        // Docker指令
        String dockerInstructions = "\\b(?i)(FROM|RUN|CMD|LABEL|MAINTAINER|EXPOSE|ENV|ADD|COPY|" +
                                   "ENTRYPOINT|VOLUME|USER|WORKDIR|ARG|ONBUILD|STOPSIGNAL|" +
                                   "HEALTHCHECK|SHELL)\\b";
        rules.add(new SyntaxRule(TokenType.KEYWORD, dockerInstructions, DefaultColors.KEYWORD));

        // 字符串
        rules.add(new SyntaxRule(TokenType.STRING, "\"[^\"]*\"", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "'[^']*'", DefaultColors.STRING));

        // 变量
        rules.add(new SyntaxRule(TokenType.FUNCTION, "\\$\\{?\\w+\\}?", DefaultColors.FUNCTION));

        // 端口号
        rules.add(new SyntaxRule(TokenType.NUMBER, "\\b\\d{2,5}\\b", DefaultColors.NUMBER));
    }

    /**
     * INI语法规则
     */
    private void addIniRules(List<SyntaxRule> rules) {
        // 注释
        rules.add(new SyntaxRule(TokenType.COMMENT, Pattern.compile(";.*$", Pattern.MULTILINE), DefaultColors.COMMENT));
        rules.add(new SyntaxRule(TokenType.COMMENT, Pattern.compile("#.*$", Pattern.MULTILINE), DefaultColors.COMMENT));

        // 节标题
        rules.add(new SyntaxRule(TokenType.TAG, "\\[.*?\\]", DefaultColors.TAG));

        // 键
        rules.add(new SyntaxRule(TokenType.ATTRIBUTE, "^\\s*[\\w.-]+(?=\\s*=)", DefaultColors.ATTRIBUTE));

        // 值
        rules.add(new SyntaxRule(TokenType.STRING, "=\\s*.*$", DefaultColors.STRING));

        // 数字
        rules.add(new SyntaxRule(TokenType.NUMBER, "\\b\\d+(\\.\\d+)?\\b", DefaultColors.NUMBER));

        // 布尔值
        rules.add(new SyntaxRule(TokenType.KEYWORD, "\\b(?i)(true|false|yes|no|on|off|1|0)\\b", DefaultColors.KEYWORD));
    }

    /**
     * TOML语法规则
     */
    private void addTomlRules(List<SyntaxRule> rules) {
        // 注释
        rules.add(new SyntaxRule(TokenType.COMMENT, Pattern.compile("#.*$", Pattern.MULTILINE), DefaultColors.COMMENT));

        // 表标题
        rules.add(new SyntaxRule(TokenType.TAG, "\\[.*?\\]", DefaultColors.TAG));

        // 键
        rules.add(new SyntaxRule(TokenType.ATTRIBUTE, "^\\s*[\\w.-]+(?=\\s*=)", DefaultColors.ATTRIBUTE));

        // 字符串
        rules.add(new SyntaxRule(TokenType.STRING, "\"(?:[^\"\\\\]|\\\\.)*\"", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "'[^']*'", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "\"\"\"[\\s\\S]*?\"\"\"", DefaultColors.STRING)); // 多行字符串
        rules.add(new SyntaxRule(TokenType.STRING, "'''[\\s\\S]*?'''", DefaultColors.STRING));

        // 数字
        rules.add(new SyntaxRule(TokenType.NUMBER, "\\b\\d+(\\.\\d+)?([eE][+-]?\\d+)?\\b", DefaultColors.NUMBER));
        rules.add(new SyntaxRule(TokenType.NUMBER, "\\b0[xX][0-9a-fA-F]+\\b", DefaultColors.NUMBER));
        rules.add(new SyntaxRule(TokenType.NUMBER, "\\b0[oO][0-7]+\\b", DefaultColors.NUMBER));
        rules.add(new SyntaxRule(TokenType.NUMBER, "\\b0[bB][01]+\\b", DefaultColors.NUMBER));

        // 布尔值
        rules.add(new SyntaxRule(TokenType.KEYWORD, "\\b(true|false)\\b", DefaultColors.KEYWORD));

        // 日期时间
        rules.add(new SyntaxRule(TokenType.ANNOTATION, "\\d{4}-\\d{2}-\\d{2}(T\\d{2}:\\d{2}:\\d{2}(\\.\\d+)?(Z|[+-]\\d{2}:\\d{2}))?", DefaultColors.ANNOTATION));
    }

    /**
     * Makefile语法规则
     */
    private void addMakefileRules(List<SyntaxRule> rules) {
        // 注释
        rules.add(new SyntaxRule(TokenType.COMMENT, Pattern.compile("#.*$", Pattern.MULTILINE), DefaultColors.COMMENT));

        // 目标
        rules.add(new SyntaxRule(TokenType.FUNCTION, "^[\\w.-]+:", DefaultColors.FUNCTION));

        // 变量定义
        rules.add(new SyntaxRule(TokenType.ATTRIBUTE, "^\\s*[A-Z_][A-Z0-9_]*\\s*[?:+]?=", DefaultColors.ATTRIBUTE));

        // 变量引用
        rules.add(new SyntaxRule(TokenType.FUNCTION, "\\$\\([^)]+\\)", DefaultColors.FUNCTION));
        rules.add(new SyntaxRule(TokenType.FUNCTION, "\\$\\{[^}]+\\}", DefaultColors.FUNCTION));
        rules.add(new SyntaxRule(TokenType.FUNCTION, "\\$[@<^+?*%]", DefaultColors.FUNCTION));

        // Make函数
        String makeFunctions = "\\b(subst|patsubst|strip|findstring|filter|filter-out|sort|" +
                              "word|wordlist|words|firstword|lastword|dir|notdir|suffix|" +
                              "basename|addsuffix|addprefix|join|wildcard|realpath|abspath|" +
                              "if|or|and|foreach|call|value|eval|origin|flavor|shell|error|" +
                              "warning|info)\\b";
        rules.add(new SyntaxRule(TokenType.KEYWORD, makeFunctions, DefaultColors.KEYWORD));

        // 字符串
        rules.add(new SyntaxRule(TokenType.STRING, "\"[^\"]*\"", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "'[^']*'", DefaultColors.STRING));
    }

    /**
     * 通用语法规则（用于不支持的语言）
     */
    private void addGenericRules(List<SyntaxRule> rules) {
        // 字符串
        rules.add(new SyntaxRule(TokenType.STRING, "\"(?:[^\"\\\\]|\\\\.)*\"", DefaultColors.STRING));
        rules.add(new SyntaxRule(TokenType.STRING, "'(?:[^'\\\\]|\\\\.)*'", DefaultColors.STRING));

        // 数字
        rules.add(new SyntaxRule(TokenType.NUMBER, "\\b\\d+(\\.\\d+)?\\b", DefaultColors.NUMBER));

        // 注释（通用）
        rules.add(new SyntaxRule(TokenType.COMMENT, Pattern.compile("//.*$", Pattern.MULTILINE), DefaultColors.COMMENT));
        rules.add(new SyntaxRule(TokenType.COMMENT, Pattern.compile("#.*$", Pattern.MULTILINE), DefaultColors.COMMENT));
    }

    /**
     * 对文本应用语法高亮
     */
    public Spannable highlight(String text) {
        if (text == null || text.isEmpty()) {
            return new SpannableStringBuilder("");
        }
        
        SpannableStringBuilder spannable = new SpannableStringBuilder(text);
        
        // 应用所有语法规则
        for (SyntaxRule rule : rules) {
            applyRule(spannable, rule);
        }
        
        return spannable;
    }

    /**
     * 应用单个语法规则
     */
    private void applyRule(SpannableStringBuilder spannable, SyntaxRule rule) {
        Matcher matcher = rule.pattern.matcher(spannable);
        
        while (matcher.find()) {
            int start = matcher.start();
            int end = matcher.end();
            
            // 检查是否已经有颜色span，避免重复
            ForegroundColorSpan[] existingSpans = spannable.getSpans(start, end, ForegroundColorSpan.class);
            if (existingSpans.length == 0) {
                spannable.setSpan(new ForegroundColorSpan(rule.color), 
                                 start, end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            }
        }
    }

    /**
     * 获取当前语言
     */
    public LanguageDetector.Language getLanguage() {
        return language;
    }

    /**
     * 清除所有高亮
     */
    public static void clearHighlight(Spannable spannable) {
        ForegroundColorSpan[] spans = spannable.getSpans(0, spannable.length(), ForegroundColorSpan.class);
        for (ForegroundColorSpan span : spans) {
            spannable.removeSpan(span);
        }
    }
}