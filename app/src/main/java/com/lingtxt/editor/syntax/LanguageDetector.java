package com.lingtxt.editor.syntax;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 语言检测器
 * 根据文件扩展名和内容特征自动检测编程语言
 */
public class LanguageDetector {

    /**
     * 支持的编程语言
     */
    public enum Language {
        JAVA("Java"),
        JAVASCRIPT("JavaScript"),
        PYTHON("Python"),
        HTML("HTML"),
        CSS("CSS"),
        XML("XML"),
        JSON("JSON"),
        MARKDOWN("Markdown"),
        YAML("YAML"),
        PROPERTIES("Properties"),
        SHELL("Shell"),
        SQL("SQL"),
        C("C"),
        CPP("C++"),
        GO("Go"),
        KOTLIN("Kotlin"),
        SWIFT("Swift"),
        RUST("Rust"),
        PHP("PHP"),
        RUBY("Ruby"),
        TYPESCRIPT("TypeScript"),
        CSHARP("C#"),
        DART("Dart"),
        SCALA("Scala"),
        PERL("Perl"),
        LUA("Lua"),
        R("R"),
        MATLAB("MATLAB"),
        OBJECTIVE_C("Objective-C"),
        ASSEMBLY("Assembly"),
        BASH("Bash"),
        POWERSHELL("PowerShell"),
        BATCH("Batch"),
        VB("Visual Basic"),
        PASCAL("Pascal"),
        FORTRAN("Fortran"),
        COBOL("COBOL"),
        HASKELL("Haskell"),
        ERLANG("Erlang"),
        ELIXIR("Elixir"),
        CLOJURE("Clojure"),
        LISP("Lisp"),
        SCHEME("Scheme"),
        PROLOG("Prolog"),
        GROOVY("Groovy"),
        JULIA("Julia"),
        CRYSTAL("Crystal"),
        NIM("Nim"),
        ZIG("Zig"),
        VERILOG("Verilog"),
        VHDL("VHDL"),
        MAKEFILE("Makefile"),
        CMAKE("CMake"),
        DOCKERFILE("Dockerfile"),
        NGINX("Nginx"),
        APACHE("Apache"),
        INI("INI"),
        TOML("TOML"),
        PROTOBUF("Protocol Buffers"),
        GRAPHQL("GraphQL"),
        LATEX("LaTeX"),
        BIBTEX("BibTeX"),
        PLAIN_TEXT("Plain Text");

        private final String displayName;

        Language(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    // 文件扩展名到语言的映射
    private static final Map<String, Language> EXTENSION_MAP = new HashMap<>();
    
    // 内容特征模式
    private static final Map<Language, Pattern[]> CONTENT_PATTERNS = new HashMap<>();

    static {
        initializeExtensionMap();
        initializeContentPatterns();
    }

    /**
     * 初始化文件扩展名映射
     */
    private static void initializeExtensionMap() {
        // Java
        EXTENSION_MAP.put("java", Language.JAVA);
        EXTENSION_MAP.put("class", Language.JAVA);
        
        // JavaScript
        EXTENSION_MAP.put("js", Language.JAVASCRIPT);
        EXTENSION_MAP.put("jsx", Language.JAVASCRIPT);
        EXTENSION_MAP.put("mjs", Language.JAVASCRIPT);

        // TypeScript
        EXTENSION_MAP.put("ts", Language.TYPESCRIPT);
        EXTENSION_MAP.put("tsx", Language.TYPESCRIPT);
        
        // Python
        EXTENSION_MAP.put("py", Language.PYTHON);
        EXTENSION_MAP.put("pyw", Language.PYTHON);
        EXTENSION_MAP.put("pyx", Language.PYTHON);
        
        // Web
        EXTENSION_MAP.put("html", Language.HTML);
        EXTENSION_MAP.put("htm", Language.HTML);
        EXTENSION_MAP.put("css", Language.CSS);
        EXTENSION_MAP.put("scss", Language.CSS);
        EXTENSION_MAP.put("sass", Language.CSS);
        
        // Data formats
        EXTENSION_MAP.put("xml", Language.XML);
        EXTENSION_MAP.put("json", Language.JSON);
        EXTENSION_MAP.put("yml", Language.YAML);
        EXTENSION_MAP.put("yaml", Language.YAML);
        
        // Config
        EXTENSION_MAP.put("properties", Language.PROPERTIES);
        EXTENSION_MAP.put("conf", Language.PROPERTIES);
        EXTENSION_MAP.put("config", Language.PROPERTIES);
        
        // Scripts
        EXTENSION_MAP.put("sh", Language.SHELL);
        EXTENSION_MAP.put("bash", Language.SHELL);
        EXTENSION_MAP.put("zsh", Language.SHELL);
        
        // Database
        EXTENSION_MAP.put("sql", Language.SQL);
        
        // C/C++
        EXTENSION_MAP.put("c", Language.C);
        EXTENSION_MAP.put("h", Language.C);
        EXTENSION_MAP.put("cpp", Language.CPP);
        EXTENSION_MAP.put("cxx", Language.CPP);
        EXTENSION_MAP.put("cc", Language.CPP);
        EXTENSION_MAP.put("hpp", Language.CPP);
        
        // Modern languages
        EXTENSION_MAP.put("go", Language.GO);
        EXTENSION_MAP.put("kt", Language.KOTLIN);
        EXTENSION_MAP.put("kts", Language.KOTLIN);
        EXTENSION_MAP.put("swift", Language.SWIFT);
        EXTENSION_MAP.put("rs", Language.RUST);
        EXTENSION_MAP.put("php", Language.PHP);
        EXTENSION_MAP.put("php3", Language.PHP);
        EXTENSION_MAP.put("php4", Language.PHP);
        EXTENSION_MAP.put("php5", Language.PHP);
        EXTENSION_MAP.put("phtml", Language.PHP);
        EXTENSION_MAP.put("rb", Language.RUBY);
        EXTENSION_MAP.put("rbw", Language.RUBY);

        // Additional languages
        EXTENSION_MAP.put("cs", Language.CSHARP);
        EXTENSION_MAP.put("dart", Language.DART);
        EXTENSION_MAP.put("scala", Language.SCALA);
        EXTENSION_MAP.put("sc", Language.SCALA);
        EXTENSION_MAP.put("pl", Language.PERL);
        EXTENSION_MAP.put("pm", Language.PERL);
        EXTENSION_MAP.put("lua", Language.LUA);
        EXTENSION_MAP.put("r", Language.R);
        EXTENSION_MAP.put("R", Language.R);
        EXTENSION_MAP.put("m", Language.MATLAB);
        EXTENSION_MAP.put("mm", Language.OBJECTIVE_C);
        EXTENSION_MAP.put("asm", Language.ASSEMBLY);
        EXTENSION_MAP.put("s", Language.ASSEMBLY);
        EXTENSION_MAP.put("S", Language.ASSEMBLY);

        // Shell scripts
        EXTENSION_MAP.put("bash", Language.BASH);
        EXTENSION_MAP.put("zsh", Language.BASH);
        EXTENSION_MAP.put("fish", Language.BASH);
        EXTENSION_MAP.put("ps1", Language.POWERSHELL);
        EXTENSION_MAP.put("psm1", Language.POWERSHELL);
        EXTENSION_MAP.put("psd1", Language.POWERSHELL);
        EXTENSION_MAP.put("bat", Language.BATCH);
        EXTENSION_MAP.put("cmd", Language.BATCH);
        EXTENSION_MAP.put("vb", Language.VB);
        EXTENSION_MAP.put("vbs", Language.VB);
        EXTENSION_MAP.put("hs", Language.HASKELL);
        EXTENSION_MAP.put("lhs", Language.HASKELL);
        EXTENSION_MAP.put("groovy", Language.GROOVY);
        EXTENSION_MAP.put("gradle", Language.GROOVY);

        // Configuration files
        EXTENSION_MAP.put("dockerfile", Language.DOCKERFILE);
        EXTENSION_MAP.put("Dockerfile", Language.DOCKERFILE);
        EXTENSION_MAP.put("ini", Language.INI);
        EXTENSION_MAP.put("cfg", Language.INI);
        EXTENSION_MAP.put("toml", Language.TOML);
        EXTENSION_MAP.put("makefile", Language.MAKEFILE);
        EXTENSION_MAP.put("Makefile", Language.MAKEFILE);
        EXTENSION_MAP.put("mk", Language.MAKEFILE);

        // Documentation
        EXTENSION_MAP.put("md", Language.MARKDOWN);
        EXTENSION_MAP.put("markdown", Language.MARKDOWN);
        EXTENSION_MAP.put("txt", Language.PLAIN_TEXT);
    }

    /**
     * 初始化内容特征模式
     */
    private static void initializeContentPatterns() {
        // Java patterns
        CONTENT_PATTERNS.put(Language.JAVA, new Pattern[]{
            Pattern.compile("\\bpublic\\s+class\\b"),
            Pattern.compile("\\bpackage\\s+[\\w.]+;"),
            Pattern.compile("\\bimport\\s+[\\w.]+;"),
            Pattern.compile("\\bpublic\\s+static\\s+void\\s+main\\b")
        });
        
        // JavaScript patterns
        CONTENT_PATTERNS.put(Language.JAVASCRIPT, new Pattern[]{
            Pattern.compile("\\bfunction\\s+\\w+\\s*\\("),
            Pattern.compile("\\bvar\\s+\\w+\\s*="),
            Pattern.compile("\\blet\\s+\\w+\\s*="),
            Pattern.compile("\\bconst\\s+\\w+\\s*="),
            Pattern.compile("\\bconsole\\.log\\s*\\(")
        });
        
        // Python patterns
        CONTENT_PATTERNS.put(Language.PYTHON, new Pattern[]{
            Pattern.compile("\\bdef\\s+\\w+\\s*\\("),
            Pattern.compile("\\bimport\\s+\\w+"),
            Pattern.compile("\\bfrom\\s+\\w+\\s+import\\b"),
            Pattern.compile("\\bif\\s+__name__\\s*==\\s*['\"]__main__['\"]")
        });
        
        // HTML patterns
        CONTENT_PATTERNS.put(Language.HTML, new Pattern[]{
            Pattern.compile("<!DOCTYPE\\s+html>", Pattern.CASE_INSENSITIVE),
            Pattern.compile("<html[^>]*>", Pattern.CASE_INSENSITIVE),
            Pattern.compile("<head[^>]*>", Pattern.CASE_INSENSITIVE),
            Pattern.compile("<body[^>]*>", Pattern.CASE_INSENSITIVE)
        });
        
        // XML patterns
        CONTENT_PATTERNS.put(Language.XML, new Pattern[]{
            Pattern.compile("<\\?xml\\s+version\\s*=\\s*['\"][^'\"]*['\"]")
        });
        
        // JSON patterns
        CONTENT_PATTERNS.put(Language.JSON, new Pattern[]{
            Pattern.compile("^\\s*\\{.*\\}\\s*$", Pattern.DOTALL),
            Pattern.compile("^\\s*\\[.*\\]\\s*$", Pattern.DOTALL)
        });
    }

    /**
     * 根据文件名检测语言
     */
    public static Language detectByFileName(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return Language.PLAIN_TEXT;
        }
        
        // 提取文件扩展名
        int lastDot = fileName.lastIndexOf('.');
        if (lastDot == -1 || lastDot == fileName.length() - 1) {
            return Language.PLAIN_TEXT;
        }
        
        String extension = fileName.substring(lastDot + 1).toLowerCase();
        return EXTENSION_MAP.getOrDefault(extension, Language.PLAIN_TEXT);
    }

    /**
     * 根据文件内容检测语言
     */
    public static Language detectByContent(String content) {
        if (content == null || content.trim().isEmpty()) {
            return Language.PLAIN_TEXT;
        }
        
        // 限制检测内容长度，避免性能问题
        String sampleContent = content.length() > 1000 ? 
                              content.substring(0, 1000) : content;
        
        // 计算每种语言的匹配分数
        Language bestMatch = Language.PLAIN_TEXT;
        int bestScore = 0;
        
        for (Map.Entry<Language, Pattern[]> entry : CONTENT_PATTERNS.entrySet()) {
            Language language = entry.getKey();
            Pattern[] patterns = entry.getValue();
            
            int score = 0;
            for (Pattern pattern : patterns) {
                if (pattern.matcher(sampleContent).find()) {
                    score++;
                }
            }
            
            if (score > bestScore) {
                bestScore = score;
                bestMatch = language;
            }
        }
        
        return bestScore > 0 ? bestMatch : Language.PLAIN_TEXT;
    }

    /**
     * 综合检测语言（优先使用文件名，内容作为辅助）
     */
    public static Language detectLanguage(String fileName, String content) {
        Language byFileName = detectByFileName(fileName);
        
        // 如果文件名检测结果不是纯文本，直接返回
        if (byFileName != Language.PLAIN_TEXT) {
            return byFileName;
        }
        
        // 否则尝试通过内容检测
        return detectByContent(content);
    }

    /**
     * 获取所有支持的语言
     */
    public static Language[] getSupportedLanguages() {
        return Language.values();
    }

    /**
     * 检查是否支持指定语言
     */
    public static boolean isLanguageSupported(Language language) {
        return language != null && language != Language.PLAIN_TEXT;
    }
}