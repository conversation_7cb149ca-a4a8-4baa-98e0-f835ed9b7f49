package com.lingtxt.editor.syntax;

import android.os.Handler;
import android.os.Looper;
import android.text.Editable;
import android.text.Spannable;
import android.text.TextWatcher;
import android.widget.EditText;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 语法高亮管理器
 * 管理编辑器的语法高亮功能，包括异步处理和性能优化
 */
public class SyntaxHighlightManager {

    private final EditText editText;
    private final ExecutorService executorService;
    private final Handler mainHandler;
    
    private SyntaxHighlighter highlighter;
    private LanguageDetector.Language currentLanguage;
    private boolean isHighlightEnabled = true;
    private boolean isProcessing = false;
    
    // 性能优化参数
    private static final int HIGHLIGHT_DELAY_MS = 300; // 延迟高亮，避免频繁更新
    private static final int MAX_HIGHLIGHT_LENGTH = 50000; // 最大高亮文本长度
    
    private Runnable pendingHighlightTask;
    
    // TextWatcher实例，避免重复创建
    private final TextWatcher textWatcher = new TextWatcher() {
        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {}

        @Override
        public void afterTextChanged(Editable s) {
            if (isHighlightEnabled && highlighter != null) {
                scheduleHighlight();
            }
        }
    };
    
    public SyntaxHighlightManager(EditText editText) {
        this.editText = editText;
        this.executorService = Executors.newSingleThreadExecutor();
        this.mainHandler = new Handler(Looper.getMainLooper());
        
        setupTextWatcher();
    }

    /**
     * 设置文本变化监听器
     */
    private void setupTextWatcher() {
        editText.addTextChangedListener(textWatcher);
    }

    /**
     * 设置语言并初始化高亮器
     */
    public void setLanguage(LanguageDetector.Language language) {
        if (language == currentLanguage) {
            return; // 语言没有变化，不需要重新设置
        }
        
        currentLanguage = language;
        
        if (LanguageDetector.isLanguageSupported(language)) {
            highlighter = new SyntaxHighlighter(language);
            if (isHighlightEnabled) {
                scheduleHighlight();
            }
        } else {
            highlighter = null;
            clearHighlight();
        }
    }

    /**
     * 自动检测语言并设置
     */
    public void autoDetectLanguage(String fileName) {
        String content = editText.getText().toString();
        LanguageDetector.Language detectedLanguage = 
            LanguageDetector.detectLanguage(fileName, content);
        setLanguage(detectedLanguage);
    }

    /**
     * 启用或禁用语法高亮
     */
    public void setHighlightEnabled(boolean enabled) {
        if (isHighlightEnabled == enabled) {
            return;
        }
        
        isHighlightEnabled = enabled;
        
        if (enabled && highlighter != null) {
            scheduleHighlight();
        } else if (!enabled) {
            clearHighlight();
        }
    }


    /**
     * 调度语法高亮任务（延迟执行）
     */
    private void scheduleHighlight() {
        if (isProcessing) {
            return; // 已经在处理中，避免重复调度
        }
        
        cancelPendingHighlight();
        
        pendingHighlightTask = this::performHighlight;
        mainHandler.postDelayed(pendingHighlightTask, HIGHLIGHT_DELAY_MS);
    }

    /**
     * 取消待处理的高亮任务
     */
    private void cancelPendingHighlight() {
        if (pendingHighlightTask != null) {
            mainHandler.removeCallbacks(pendingHighlightTask);
            pendingHighlightTask = null;
        }
    }

    /**
     * 执行语法高亮
     */
    private void performHighlight() {
        if (!isHighlightEnabled || highlighter == null || isProcessing) {
            return;
        }
        
        String text = editText.getText().toString();
        
        // 检查文本长度，避免处理过大的文件影响性能
        if (text.length() > MAX_HIGHLIGHT_LENGTH) {
            return;
        }
        
        isProcessing = true;
        
        // 在后台线程中进行高亮处理
        executorService.execute(() -> {
            try {
                Spannable highlighted = highlighter.highlight(text);
                
                // 在主线程中应用结果
                mainHandler.post(() -> {
                    try {
                        // 检查文本是否已经改变
                        if (text.equals(editText.getText().toString())) {
                            applyHighlightResult(highlighted);
                        }
                    } finally {
                        isProcessing = false;
                    }
                });
                
            } catch (Exception e) {
                // 处理异常，确保processing状态被重置
                mainHandler.post(() -> isProcessing = false);
            }
        });
    }

    /**
     * 应用高亮结果到EditText
     */
    private void applyHighlightResult(Spannable highlighted) {
        // 保存当前光标位置
        int selectionStart = editText.getSelectionStart();
        int selectionEnd = editText.getSelectionEnd();
        
        // 临时移除TextWatcher，避免触发递归
        editText.removeTextChangedListener(textWatcher);
        
        try {
            // 应用高亮文本
            editText.setText(highlighted);
            
            // 恢复光标位置
            if (selectionStart >= 0 && selectionEnd >= 0 && 
                selectionStart <= highlighted.length() && selectionEnd <= highlighted.length()) {
                editText.setSelection(selectionStart, selectionEnd);
            }
        } finally {
            // 重新添加TextWatcher
            editText.addTextChangedListener(textWatcher);
        }
    }

    /**
     * 清除所有语法高亮
     */
    public void clearHighlight() {
        cancelPendingHighlight();
        
        Editable editable = editText.getText();
        if (editable instanceof Spannable) {
            SyntaxHighlighter.clearHighlight((Spannable) editable);
        }
    }

    /**
     * 获取当前语言
     */
    public LanguageDetector.Language getCurrentLanguage() {
        return currentLanguage;
    }


    /**
     * 释放资源
     */
    public void destroy() {
        cancelPendingHighlight();
        
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
        
        clearHighlight();
    }


    /**
     * 获取语法高亮器
     */
    public SyntaxHighlighter getSyntaxHighlighter() {
        return highlighter;
    }
}