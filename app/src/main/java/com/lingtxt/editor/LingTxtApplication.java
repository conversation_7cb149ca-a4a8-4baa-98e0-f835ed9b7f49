package com.lingtxt.editor;

import android.app.Application;

import com.lingtxt.editor.data.model.AppLanguage;
import com.lingtxt.editor.data.model.Theme;
import com.lingtxt.editor.data.repository.SettingsRepository;
import com.lingtxt.editor.di.AppComponent;
import com.lingtxt.editor.di.DaggerAppComponent;
import com.lingtxt.editor.ui.main.MainActivity;
import com.lingtxt.editor.utils.GlobalExceptionHandler;
import com.lingtxt.editor.utils.SettingsChangeManager;

/**
 * 应用程序入口类，配置Dagger依赖注入
 */
public class LingTxtApplication extends Application {

    private AppComponent appComponent;

    @Override
    public void onCreate() {
        super.onCreate();

        // 安装全局异常处理器
        GlobalExceptionHandler.install(this, MainActivity.class);

        // 初始化Dagger组件
        appComponent = DaggerAppComponent.builder()
                .application(this)
                .build();

        // 应用主题和语言设置
        applyThemeSettings();
        applyLanguageSettings();

        // 在调试模式下打印支持的文件类型配置
        // TODO: 启用调试信息打印
        // if (BuildConfig.DEBUG) {
        //     com.lingtxt.editor.utils.ManifestGenerator.printConfigurations();
        // }
    }



    /**
     * 应用主题设置
     */
    private void applyThemeSettings() {
        try {
            SettingsRepository settingsRepository = appComponent.getSettingsRepository();
            settingsRepository.getTheme()
                .subscribe(
                    theme -> {
                        // 使用SettingsChangeManager统一应用主题
                        SettingsChangeManager.getInstance().applyThemeChange(theme);
                    },
                    throwable -> {
                        // 默认跟随系统
                        SettingsChangeManager.getInstance().applyThemeChange(Theme.SYSTEM);
                    }
                );
        } catch (Exception e) {
            // 默认跟随系统
            SettingsChangeManager.getInstance().applyThemeChange(Theme.SYSTEM);
        }
    }

    /**
     * 应用语言设置
     */
    private void applyLanguageSettings() {
        try {
            SettingsRepository settingsRepository = appComponent.getSettingsRepository();
            settingsRepository.getLanguage()
                .subscribe(
                    language -> {
                        // 使用SettingsChangeManager统一应用语言
                        SettingsChangeManager.getInstance().applyLanguageChange(this, language);
                    },
                    throwable -> {
                        // 默认跟随系统
                        SettingsChangeManager.getInstance().applyLanguageChange(this, AppLanguage.SYSTEM);
                    }
                );
        } catch (Exception e) {
            // 默认跟随系统
            SettingsChangeManager.getInstance().applyLanguageChange(this, AppLanguage.SYSTEM);
        }
    }

    public AppComponent getAppComponent() {
        return appComponent;
    }
}