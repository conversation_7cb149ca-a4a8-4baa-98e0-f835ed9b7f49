package com.lingtxt.editor.di;

import android.content.Context;
import android.content.SharedPreferences;
import com.lingtxt.editor.data.database.DatabaseHelper;
import com.lingtxt.editor.data.database.dao.RecentFileDao;
import com.lingtxt.editor.data.database.dao.RecentFileDaoImpl;
import com.lingtxt.editor.data.preferences.PreferencesManager;
import com.lingtxt.editor.data.repository.FileRepository;
import com.lingtxt.editor.data.repository.FileRepositoryImpl;
import com.lingtxt.editor.data.repository.SettingsRepository;
import com.lingtxt.editor.data.repository.SettingsRepositoryImpl;
import dagger.Module;
import dagger.Provides;
import javax.inject.Singleton;

/**
 * 数据模块，提供Repository和数据源相关的依赖
 */
@Module
public class DataModule {

    @Provides
    @Singleton
    DatabaseHelper provideDatabaseHelper(Context context) {
        return new DatabaseHelper(context);
    }

    @Provides
    @Singleton
    RecentFileDao provideRecentFileDao(DatabaseHelper databaseHelper) {
        return new RecentFileDaoImpl(databaseHelper);
    }

    @Provides
    @Singleton
    PreferencesManager providePreferencesManager(SharedPreferences sharedPreferences) {
        return new PreferencesManager(sharedPreferences);
    }

    @Provides
    @Singleton
    FileRepository provideFileRepository(Context context) {
        return new FileRepositoryImpl(context);
    }

    @Provides
    @Singleton
    SettingsRepository provideSettingsRepository(SharedPreferences sharedPreferences) {
        return new SettingsRepositoryImpl(sharedPreferences);
    }
}