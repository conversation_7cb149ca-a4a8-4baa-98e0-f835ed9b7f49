package com.lingtxt.editor.di;

import android.app.Application;
import com.lingtxt.editor.ui.main.MainActivity;
import com.lingtxt.editor.ui.settings.SettingsActivity;
import com.lingtxt.editor.data.repository.SettingsRepository;
import dagger.BindsInstance;
import dagger.Component;
import javax.inject.Singleton;

/**
 * Dagger应用程序组件，定义依赖注入的范围
 */
@Singleton
@Component(modules = {
    AppModule.class,
    DataModule.class,
    ViewModelModule.class
})
public interface AppComponent {

    @Component.Builder
    interface Builder {
        @BindsInstance
        Builder application(Application application);
        AppComponent build();
    }

    // 注入到Activity
    void inject(MainActivity mainActivity);
    void inject(SettingsActivity settingsActivity);

    // 提供Repository
    SettingsRepository getSettingsRepository();
}