package com.lingtxt.editor.data.preferences;

import android.content.SharedPreferences;
import com.lingtxt.editor.data.model.AppLanguage;
import com.lingtxt.editor.data.model.AppSettings;
import com.lingtxt.editor.data.model.Theme;
import io.reactivex.rxjava3.core.Completable;
import io.reactivex.rxjava3.core.Observable;
import javax.inject.Inject;
import javax.inject.Singleton;

/**
 * SharedPreferences配置管理类
 */
@Singleton
public class PreferencesManager {

    // 配置键名常量
    private static final String KEY_FONT_SIZE = "font_size";
    private static final String KEY_FONT_FAMILY = "font_family";
    private static final String KEY_THEME = "theme";
    private static final String KEY_LANGUAGE = "language";
    private static final String KEY_SHOW_LINE_NUMBERS = "show_line_numbers";
    private static final String KEY_SHOW_INVISIBLE_CHARS = "show_invisible_chars";
    private static final String KEY_ENABLE_SYNTAX_HIGHLIGHT = "enable_syntax_highlight";
    private static final String KEY_ENABLE_CODE_FOLDING = "enable_code_folding";
    private static final String KEY_ENABLE_AUTO_INDENT = "enable_auto_indent";
    private static final String KEY_ENABLE_WORD_WRAP = "enable_word_wrap";
    private static final String KEY_TAB_SIZE = "tab_size";
    private static final String KEY_USE_SPACES_FOR_TABS = "use_spaces_for_tabs";
    private static final String KEY_ENABLE_GESTURES = "enable_gestures";
    private static final String KEY_ENABLE_VOICE_COMMANDS = "enable_voice_commands";
    private static final String KEY_MAX_RECENT_FILES = "max_recent_files";
    
    // 编辑器状态相关
    private static final String KEY_LAST_CURSOR_POSITION = "last_cursor_position";
    private static final String KEY_LAST_SCROLL_X = "last_scroll_x";
    private static final String KEY_LAST_SCROLL_Y = "last_scroll_y";
    
    // 应用状态相关
    private static final String KEY_FIRST_LAUNCH = "first_launch";
    private static final String KEY_APP_VERSION = "app_version";

    private final SharedPreferences sharedPreferences;

    @Inject
    public PreferencesManager(SharedPreferences sharedPreferences) {
        this.sharedPreferences = sharedPreferences;
    }

    /**
     * 获取应用设置
     */
    public Observable<AppSettings> getAppSettings() {
        return Observable.fromCallable(() -> {
            AppSettings.Builder builder = new AppSettings.Builder();
            
            builder.setFontSize(sharedPreferences.getFloat(KEY_FONT_SIZE, 14f))
                   .setFontFamily(sharedPreferences.getString(KEY_FONT_FAMILY, "monospace"))
                   .setTheme(Theme.valueOf(sharedPreferences.getString(KEY_THEME, Theme.SYSTEM.name())))
                   .setLanguage(AppLanguage.valueOf(sharedPreferences.getString(KEY_LANGUAGE, AppLanguage.SYSTEM.name())))
                   .setShowLineNumbers(sharedPreferences.getBoolean(KEY_SHOW_LINE_NUMBERS, true))
                   .setShowInvisibleChars(sharedPreferences.getBoolean(KEY_SHOW_INVISIBLE_CHARS, false))
                   .setEnableSyntaxHighlight(sharedPreferences.getBoolean(KEY_ENABLE_SYNTAX_HIGHLIGHT, true))
                   .setEnableCodeFolding(sharedPreferences.getBoolean(KEY_ENABLE_CODE_FOLDING, true))
                   .setEnableAutoIndent(sharedPreferences.getBoolean(KEY_ENABLE_AUTO_INDENT, true))
                   .setEnableWordWrap(sharedPreferences.getBoolean(KEY_ENABLE_WORD_WRAP, false))
                   .setTabSize(sharedPreferences.getInt(KEY_TAB_SIZE, 4))
                   .setUseSpacesForTabs(sharedPreferences.getBoolean(KEY_USE_SPACES_FOR_TABS, true))
                   .setEnableGestures(sharedPreferences.getBoolean(KEY_ENABLE_GESTURES, true))
                   .setEnableVoiceCommands(sharedPreferences.getBoolean(KEY_ENABLE_VOICE_COMMANDS, false))
                   .setMaxRecentFiles(sharedPreferences.getInt(KEY_MAX_RECENT_FILES, 10));
            
            return builder.build();
        });
    }

    /**
     * 保存应用设置
     */
    public Completable saveAppSettings(AppSettings settings) {
        return Completable.fromAction(() -> {
            SharedPreferences.Editor editor = sharedPreferences.edit();
            
            editor.putFloat(KEY_FONT_SIZE, settings.getFontSize())
                  .putString(KEY_FONT_FAMILY, settings.getFontFamily())
                  .putString(KEY_THEME, settings.getTheme().name())
                  .putString(KEY_LANGUAGE, settings.getLanguage().name())
                  .putBoolean(KEY_SHOW_LINE_NUMBERS, settings.isShowLineNumbers())
                  .putBoolean(KEY_SHOW_INVISIBLE_CHARS, settings.isShowInvisibleChars())
                  .putBoolean(KEY_ENABLE_SYNTAX_HIGHLIGHT, settings.isEnableSyntaxHighlight())
                  .putBoolean(KEY_ENABLE_CODE_FOLDING, settings.isEnableCodeFolding())
                  .putBoolean(KEY_ENABLE_AUTO_INDENT, settings.isEnableAutoIndent())
                  .putBoolean(KEY_ENABLE_WORD_WRAP, settings.isEnableWordWrap())
                  .putInt(KEY_TAB_SIZE, settings.getTabSize())
                  .putBoolean(KEY_USE_SPACES_FOR_TABS, settings.isUseSpacesForTabs())
                  .putBoolean(KEY_ENABLE_GESTURES, settings.isEnableGestures())
                  .putBoolean(KEY_ENABLE_VOICE_COMMANDS, settings.isEnableVoiceCommands())
                  .putInt(KEY_MAX_RECENT_FILES, settings.getMaxRecentFiles());
            
            editor.apply();
        });
    }

    // 单独的设置项获取和保存方法
    public Observable<Float> getFontSize() {
        return Observable.fromCallable(() -> sharedPreferences.getFloat(KEY_FONT_SIZE, 14f));
    }

    public Completable setFontSize(float fontSize) {
        return Completable.fromAction(() -> 
            sharedPreferences.edit().putFloat(KEY_FONT_SIZE, fontSize).apply());
    }

    public Observable<String> getFontFamily() {
        return Observable.fromCallable(() -> sharedPreferences.getString(KEY_FONT_FAMILY, "monospace"));
    }

    public Completable setFontFamily(String fontFamily) {
        return Completable.fromAction(() -> 
            sharedPreferences.edit().putString(KEY_FONT_FAMILY, fontFamily).apply());
    }

    public Observable<Theme> getTheme() {
        return Observable.fromCallable(() -> 
            Theme.valueOf(sharedPreferences.getString(KEY_THEME, Theme.SYSTEM.name())));
    }

    public Completable setTheme(Theme theme) {
        return Completable.fromAction(() -> 
            sharedPreferences.edit().putString(KEY_THEME, theme.name()).apply());
    }

    public Observable<AppLanguage> getLanguage() {
        return Observable.fromCallable(() -> 
            AppLanguage.valueOf(sharedPreferences.getString(KEY_LANGUAGE, AppLanguage.SYSTEM.name())));
    }

    public Completable setLanguage(AppLanguage language) {
        return Completable.fromAction(() -> 
            sharedPreferences.edit().putString(KEY_LANGUAGE, language.name()).apply());
    }

    public Observable<Boolean> getShowLineNumbers() {
        return Observable.fromCallable(() -> sharedPreferences.getBoolean(KEY_SHOW_LINE_NUMBERS, true));
    }

    public Completable setShowLineNumbers(boolean show) {
        return Completable.fromAction(() -> 
            sharedPreferences.edit().putBoolean(KEY_SHOW_LINE_NUMBERS, show).apply());
    }

    public Observable<Boolean> getShowInvisibleChars() {
        return Observable.fromCallable(() -> sharedPreferences.getBoolean(KEY_SHOW_INVISIBLE_CHARS, false));
    }

    public Completable setShowInvisibleChars(boolean show) {
        return Completable.fromAction(() -> 
            sharedPreferences.edit().putBoolean(KEY_SHOW_INVISIBLE_CHARS, show).apply());
    }

    // 编辑器状态相关
    public void saveEditorState(int cursorPosition, int scrollX, int scrollY) {
        sharedPreferences.edit()
            .putInt(KEY_LAST_CURSOR_POSITION, cursorPosition)
            .putInt(KEY_LAST_SCROLL_X, scrollX)
            .putInt(KEY_LAST_SCROLL_Y, scrollY)
            .apply();
    }

    public int getLastCursorPosition() {
        return sharedPreferences.getInt(KEY_LAST_CURSOR_POSITION, 0);
    }

    public int getLastScrollX() {
        return sharedPreferences.getInt(KEY_LAST_SCROLL_X, 0);
    }

    public int getLastScrollY() {
        return sharedPreferences.getInt(KEY_LAST_SCROLL_Y, 0);
    }

    // 应用状态相关
    public boolean isFirstLaunch() {
        return sharedPreferences.getBoolean(KEY_FIRST_LAUNCH, true);
    }

    public void setFirstLaunchCompleted() {
        sharedPreferences.edit().putBoolean(KEY_FIRST_LAUNCH, false).apply();
    }

    public void setAppVersion(String version) {
        sharedPreferences.edit().putString(KEY_APP_VERSION, version).apply();
    }

    public String getAppVersion() {
        return sharedPreferences.getString(KEY_APP_VERSION, "");
    }

    /**
     * 重置所有设置为默认值
     */
    public Completable resetToDefaults() {
        return Completable.fromAction(() -> {
            sharedPreferences.edit().clear().apply();
        });
    }

    /**
     * 导出设置（返回JSON字符串）
     */
    public String exportSettings() {
        // 简单的键值对导出，实际项目中可以使用JSON库
        StringBuilder sb = new StringBuilder();
        sb.append("{");
        for (String key : sharedPreferences.getAll().keySet()) {
            Object value = sharedPreferences.getAll().get(key);
            sb.append("\"").append(key).append("\":\"").append(value).append("\",");
        }
        if (sb.length() > 1) {
            sb.setLength(sb.length() - 1); // 移除最后的逗号
        }
        sb.append("}");
        return sb.toString();
    }
}