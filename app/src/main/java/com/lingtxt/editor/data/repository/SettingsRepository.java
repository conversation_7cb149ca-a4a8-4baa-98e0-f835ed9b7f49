package com.lingtxt.editor.data.repository;

import com.lingtxt.editor.data.model.AppLanguage;
import com.lingtxt.editor.data.model.Theme;
import io.reactivex.rxjava3.core.Completable;
import io.reactivex.rxjava3.core.Observable;

/**
 * 设置Repository接口
 */
public interface SettingsRepository {
    
    Observable<Float> getFontSize();
    Observable<String> getFontFamily();
    Observable<Theme> getTheme();
    Observable<AppLanguage> getLanguage();
    Observable<Boolean> getShowLineNumbers();
    Observable<Boolean> getShowInvisibleChars();
    
    Completable updateFontSize(float size);
    Completable updateFontFamily(String fontFamily);
    Completable updateTheme(Theme theme);
    Completable updateLanguage(AppLanguage language);
    Completable updateShowLineNumbers(boolean show);
    Completable updateShowInvisibleChars(boolean show);
}