package com.lingtxt.editor.data.model;

import android.net.Uri;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * 文件信息数据模型
 */
public class FileInfo {
    private final Uri uri;
    private final String fileName;
    private final String filePath;
    private final long fileSize;
    private final long lastModified;
    private final String mimeType;
    private final String encoding;
    private final boolean isReadOnly;

    public FileInfo(@NonNull Uri uri, 
                   @NonNull String fileName, 
                   @Nullable String filePath,
                   long fileSize, 
                   long lastModified, 
                   @Nullable String mimeType,
                   @NonNull String encoding,
                   boolean isReadOnly) {
        this.uri = uri;
        this.fileName = fileName;
        this.filePath = filePath;
        this.fileSize = fileSize;
        this.lastModified = lastModified;
        this.mimeType = mimeType;
        this.encoding = encoding;
        this.isReadOnly = isReadOnly;
    }

    @NonNull
    public Uri getUri() {
        return uri;
    }

    @NonNull
    public String getFileName() {
        return fileName;
    }

    @Nullable
    public String getFilePath() {
        return filePath;
    }

    public long getFileSize() {
        return fileSize;
    }

    public long getLastModified() {
        return lastModified;
    }

    @Nullable
    public String getMimeType() {
        return mimeType;
    }

    @NonNull
    public String getEncoding() {
        return encoding;
    }

    public boolean isReadOnly() {
        return isReadOnly;
    }

    /**
     * 获取文件扩展名
     */
    @Nullable
    public String getFileExtension() {
        int lastDot = fileName.lastIndexOf('.');
        if (lastDot > 0 && lastDot < fileName.length() - 1) {
            return fileName.substring(lastDot + 1).toLowerCase();
        }
        return null;
    }

    /**
     * 判断是否为文本文件
     */
    public boolean isTextFile() {
        // 首先检查MIME类型
        if (mimeType != null && mimeType.startsWith("text/")) {
            return true;
        }
        
        // 使用统一的文件类型配置
        return com.lingtxt.editor.config.SupportedFileTypes.isSupportedFile(fileName);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        FileInfo fileInfo = (FileInfo) o;
        return uri.equals(fileInfo.uri);
    }

    @Override
    public int hashCode() {
        return uri.hashCode();
    }

    @Override
    public String toString() {
        return "FileInfo{" +
                "fileName='" + fileName + '\'' +
                ", fileSize=" + fileSize +
                ", encoding='" + encoding + '\'' +
                '}';
    }
}