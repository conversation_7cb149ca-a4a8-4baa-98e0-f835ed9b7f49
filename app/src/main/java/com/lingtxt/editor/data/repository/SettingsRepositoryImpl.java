package com.lingtxt.editor.data.repository;

import android.content.SharedPreferences;
import com.lingtxt.editor.data.model.AppLanguage;
import com.lingtxt.editor.data.model.Theme;
import io.reactivex.rxjava3.core.Completable;
import io.reactivex.rxjava3.core.Observable;
import javax.inject.Inject;

/**
 * 设置Repository实现类
 */
public class SettingsRepositoryImpl implements SettingsRepository {

    private static final String KEY_FONT_SIZE = "font_size";
    private static final String KEY_FONT_FAMILY = "font_family";
    private static final String KEY_THEME = "theme";
    private static final String KEY_LANGUAGE = "language";
    private static final String KEY_SHOW_LINE_NUMBERS = "show_line_numbers";
    private static final String KEY_SHOW_INVISIBLE_CHARS = "show_invisible_chars";

    private final SharedPreferences sharedPreferences;

    @Inject
    public SettingsRepositoryImpl(SharedPreferences sharedPreferences) {
        this.sharedPreferences = sharedPreferences;
    }

    @Override
    public Observable<Float> getFontSize() {
        return Observable.fromCallable(() -> sharedPreferences.getFloat(KEY_FONT_SIZE, 12f));
    }

    @Override
    public Observable<String> getFontFamily() {
        return Observable.fromCallable(() -> sharedPreferences.getString(KEY_FONT_FAMILY, "monospace"));
    }

    @Override
    public Observable<Theme> getTheme() {
        return Observable.fromCallable(() -> {
            String themeName = sharedPreferences.getString(KEY_THEME, Theme.SYSTEM.name());
            return Theme.valueOf(themeName);
        });
    }

    @Override
    public Observable<AppLanguage> getLanguage() {
        return Observable.fromCallable(() -> {
            String languageName = sharedPreferences.getString(KEY_LANGUAGE, AppLanguage.SYSTEM.name());
            return AppLanguage.valueOf(languageName);
        });
    }

    @Override
    public Observable<Boolean> getShowLineNumbers() {
        return Observable.fromCallable(() -> sharedPreferences.getBoolean(KEY_SHOW_LINE_NUMBERS, true));
    }

    @Override
    public Observable<Boolean> getShowInvisibleChars() {
        return Observable.fromCallable(() -> sharedPreferences.getBoolean(KEY_SHOW_INVISIBLE_CHARS, false));
    }

    @Override
    public Completable updateFontSize(float size) {
        return Completable.fromAction(() -> 
            sharedPreferences.edit().putFloat(KEY_FONT_SIZE, size).apply());
    }

    @Override
    public Completable updateFontFamily(String fontFamily) {
        return Completable.fromAction(() -> 
            sharedPreferences.edit().putString(KEY_FONT_FAMILY, fontFamily).apply());
    }

    @Override
    public Completable updateTheme(Theme theme) {
        return Completable.fromAction(() -> 
            sharedPreferences.edit().putString(KEY_THEME, theme.name()).apply());
    }

    @Override
    public Completable updateLanguage(AppLanguage language) {
        return Completable.fromAction(() -> 
            sharedPreferences.edit().putString(KEY_LANGUAGE, language.name()).apply());
    }

    @Override
    public Completable updateShowLineNumbers(boolean show) {
        return Completable.fromAction(() -> 
            sharedPreferences.edit().putBoolean(KEY_SHOW_LINE_NUMBERS, show).apply());
    }

    @Override
    public Completable updateShowInvisibleChars(boolean show) {
        return Completable.fromAction(() -> 
            sharedPreferences.edit().putBoolean(KEY_SHOW_INVISIBLE_CHARS, show).apply());
    }
}