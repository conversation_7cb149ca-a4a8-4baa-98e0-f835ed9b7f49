package com.lingtxt.editor.data.repository;

import android.content.Context;
import android.content.Intent;
import android.database.Cursor;
import android.net.Uri;
import android.provider.DocumentsContract;
import android.provider.OpenableColumns;
import com.lingtxt.editor.data.model.FileContent;
import com.lingtxt.editor.data.model.FileInfo;
import com.lingtxt.editor.utils.EncodingDetector;
import io.reactivex.rxjava3.core.Completable;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.Single;
import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.nio.charset.Charset;
import javax.inject.Inject;

/**
 * 文件操作Repository实现类
 */
public class FileRepositoryImpl implements FileRepository {

    private final Context context;
    private static final int CHUNK_SIZE = 8192; // 8KB chunks for large files
    private static final long MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB limit

    @Inject
    public FileRepositoryImpl(Context context) {
        this.context = context;
    }

    @Override
    public Observable<FileContent> openFile(Uri uri) {
        return Single.fromCallable(() -> getFileInfo(uri))
                .flatMapObservable(fileInfo -> {
                    if (fileInfo.getFileSize() > MAX_FILE_SIZE) {
                        return Observable.error(new IOException("文件过大，超过50MB限制"));
                    }
                    return loadFileContent(uri, fileInfo);
                });
    }

    @Override
    public Completable saveFile(Uri uri, String content) {
        return Completable.fromAction(() -> {
            try {
                // 检查文件是否可写
                if (!DocumentsContract.isDocumentUri(context, uri)) {
                    throw new IOException("文件不支持写入操作");
                }

                // 检测原文件编码
                String encoding = EncodingDetector.detectEncoding(context, uri);
                
                try (OutputStream outputStream = context.getContentResolver().openOutputStream(uri, "wt");
                     BufferedWriter writer = new BufferedWriter(
                         new OutputStreamWriter(outputStream, encoding))) {
                    
                    writer.write(content);
                    writer.flush();
                }
            } catch (Exception e) {
                throw new RuntimeException("文件保存失败: " + e.getMessage(), e);
            }
        });
    }

    @Override
    public Single<String> detectEncoding(Uri uri) {
        return Single.fromCallable(() -> EncodingDetector.detectEncoding(context, uri));
    }

    @Override
    public Observable<String> readWithEncoding(Uri uri, String encoding) {
        return Observable.fromCallable(() -> {
            if (!Charset.isSupported(encoding)) {
                throw new IllegalArgumentException("不支持的编码格式: " + encoding);
            }

            try (InputStream inputStream = context.getContentResolver().openInputStream(uri);
                 BufferedReader reader = new BufferedReader(
                     new InputStreamReader(inputStream, encoding))) {
                
                StringBuilder content = new StringBuilder();
                char[] buffer = new char[CHUNK_SIZE];
                int charsRead;
                
                while ((charsRead = reader.read(buffer)) != -1) {
                    content.append(buffer, 0, charsRead);
                }
                
                return content.toString();
            }
        });
    }

    /**
     * 获取文件信息
     */
    private FileInfo getFileInfo(Uri uri) {
        String fileName = "未知文件";
        String filePath = null;
        long fileSize = 0;
        long lastModified = 0;
        String mimeType = null;

        try (Cursor cursor = context.getContentResolver().query(
                uri, null, null, null, null)) {
            
            if (cursor != null && cursor.moveToFirst()) {
                // 获取文件名
                int nameIndex = cursor.getColumnIndex(OpenableColumns.DISPLAY_NAME);
                if (nameIndex >= 0) {
                    fileName = cursor.getString(nameIndex);
                }

                // 获取文件大小
                int sizeIndex = cursor.getColumnIndex(OpenableColumns.SIZE);
                if (sizeIndex >= 0) {
                    fileSize = cursor.getLong(sizeIndex);
                }

                // 获取最后修改时间
                int modifiedIndex = cursor.getColumnIndex(DocumentsContract.Document.COLUMN_LAST_MODIFIED);
                if (modifiedIndex >= 0) {
                    lastModified = cursor.getLong(modifiedIndex);
                }

                // 获取MIME类型
                int mimeIndex = cursor.getColumnIndex(DocumentsContract.Document.COLUMN_MIME_TYPE);
                if (mimeIndex >= 0) {
                    mimeType = cursor.getString(mimeIndex);
                }
            }
        } catch (Exception e) {
            // 如果查询失败，使用默认值
        }

        // 尝试从URI获取路径
        filePath = uri.getPath();

        // 检测编码
        String encoding = EncodingDetector.detectEncoding(context, uri);

        // 检查是否只读
        boolean isReadOnly = !isWritable(uri);

        return new FileInfo(uri, fileName, filePath, fileSize, lastModified, 
                           mimeType, encoding, isReadOnly);
    }

    /**
     * 加载文件内容
     */
    private Observable<FileContent> loadFileContent(Uri uri, FileInfo fileInfo) {
        return Observable.fromCallable(() -> {
            try (InputStream inputStream = context.getContentResolver().openInputStream(uri);
                 BufferedReader reader = new BufferedReader(
                     new InputStreamReader(inputStream, fileInfo.getEncoding()))) {
                
                StringBuilder content = new StringBuilder();
                char[] buffer = new char[CHUNK_SIZE];
                int charsRead;
                long totalChars = 0;
                
                while ((charsRead = reader.read(buffer)) != -1) {
                    content.append(buffer, 0, charsRead);
                    totalChars += charsRead;
                    
                    // 检查是否超过大小限制
                    if (totalChars > MAX_FILE_SIZE / 2) { // 字符数大约是字节数的一半
                        throw new IOException("文件内容过大");
                    }
                }
                
                return new FileContent(
                    content.toString(),
                    fileInfo.getEncoding(),
                    content.length(),
                    System.currentTimeMillis()
                );
            }
        });
    }

    /**
     * 检查文件是否可写
     */
    private boolean isWritable(Uri uri) {
        try {
            // 尝试获取写入权限
            context.getContentResolver().takePersistableUriPermission(
                uri, Intent.FLAG_GRANT_WRITE_URI_PERMISSION);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 分块读取大文件
     */
    public Observable<String> readFileInChunks(Uri uri, String encoding, int chunkSize) {
        return Observable.create(emitter -> {
            try (InputStream inputStream = context.getContentResolver().openInputStream(uri);
                 BufferedReader reader = new BufferedReader(
                     new InputStreamReader(inputStream, encoding))) {
                
                char[] buffer = new char[chunkSize];
                int charsRead;
                
                while ((charsRead = reader.read(buffer)) != -1 && !emitter.isDisposed()) {
                    String chunk = new String(buffer, 0, charsRead);
                    emitter.onNext(chunk);
                }
                
                if (!emitter.isDisposed()) {
                    emitter.onComplete();
                }
            } catch (Exception e) {
                if (!emitter.isDisposed()) {
                    emitter.onError(e);
                }
            }
        });
    }

    /**
     * 获取文件的基本信息（不读取内容）
     */
    public Single<FileInfo> getFileInfoOnly(Uri uri) {
        return Single.fromCallable(() -> getFileInfo(uri));
    }

    /**
     * 检查文件是否存在且可读
     */
    public Single<Boolean> isFileAccessible(Uri uri) {
        return Single.fromCallable(() -> {
            try (InputStream inputStream = context.getContentResolver().openInputStream(uri)) {
                return inputStream != null;
            } catch (Exception e) {
                return false;
            }
        });
    }
}