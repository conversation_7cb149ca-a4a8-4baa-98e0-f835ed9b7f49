package com.lingtxt.editor.data.database.entity;

import androidx.annotation.NonNull;

/**
 * 最近文件实体类
 */
public class RecentFile {
    public static final String TABLE_NAME = "recent_files";
    public static final String COLUMN_ID = "id";
    public static final String COLUMN_URI = "uri";
    public static final String COLUMN_FILE_NAME = "file_name";
    public static final String COLUMN_FILE_PATH = "file_path";
    public static final String COLUMN_FILE_SIZE = "file_size";
    public static final String COLUMN_LAST_MODIFIED = "last_modified";
    public static final String COLUMN_LAST_OPENED = "last_opened";
    public static final String COLUMN_ENCODING = "encoding";
    public static final String COLUMN_CURSOR_POSITION = "cursor_position";
    public static final String COLUMN_SCROLL_X = "scroll_x";
    public static final String COLUMN_SCROLL_Y = "scroll_y";

    private long id;
    private String uri;
    private String fileName;
    private String filePath;
    private long fileSize;
    private long lastModified;
    private long lastOpened;
    private String encoding;
    private int cursorPosition;
    private int scrollX;
    private int scrollY;

    public RecentFile() {
    }

    public RecentFile(@NonNull String uri, 
                     @NonNull String fileName, 
                     String filePath,
                     long fileSize, 
                     long lastModified, 
                     long lastOpened,
                     @NonNull String encoding,
                     int cursorPosition,
                     int scrollX,
                     int scrollY) {
        this.uri = uri;
        this.fileName = fileName;
        this.filePath = filePath;
        this.fileSize = fileSize;
        this.lastModified = lastModified;
        this.lastOpened = lastOpened;
        this.encoding = encoding;
        this.cursorPosition = cursorPosition;
        this.scrollX = scrollX;
        this.scrollY = scrollY;
    }

    // Getters and Setters
    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    @NonNull
    public String getUri() {
        return uri;
    }

    public void setUri(@NonNull String uri) {
        this.uri = uri;
    }

    @NonNull
    public String getFileName() {
        return fileName;
    }

    public void setFileName(@NonNull String fileName) {
        this.fileName = fileName;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public long getFileSize() {
        return fileSize;
    }

    public void setFileSize(long fileSize) {
        this.fileSize = fileSize;
    }

    public long getLastModified() {
        return lastModified;
    }

    public void setLastModified(long lastModified) {
        this.lastModified = lastModified;
    }

    public long getLastOpened() {
        return lastOpened;
    }

    public void setLastOpened(long lastOpened) {
        this.lastOpened = lastOpened;
    }

    @NonNull
    public String getEncoding() {
        return encoding;
    }

    public void setEncoding(@NonNull String encoding) {
        this.encoding = encoding;
    }

    public int getCursorPosition() {
        return cursorPosition;
    }

    public void setCursorPosition(int cursorPosition) {
        this.cursorPosition = cursorPosition;
    }

    public int getScrollX() {
        return scrollX;
    }

    public void setScrollX(int scrollX) {
        this.scrollX = scrollX;
    }

    public int getScrollY() {
        return scrollY;
    }

    public void setScrollY(int scrollY) {
        this.scrollY = scrollY;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        RecentFile that = (RecentFile) o;
        return uri.equals(that.uri);
    }

    @Override
    public int hashCode() {
        return uri.hashCode();
    }

    @Override
    public String toString() {
        return "RecentFile{" +
                "fileName='" + fileName + '\'' +
                ", lastOpened=" + lastOpened +
                '}';
    }
}