package com.lingtxt.editor.data.database.dao;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import com.lingtxt.editor.data.database.DatabaseHelper;
import com.lingtxt.editor.data.database.entity.RecentFile;
import io.reactivex.rxjava3.core.Completable;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.Single;
import java.util.ArrayList;
import java.util.List;
import javax.inject.Inject;

/**
 * 最近文件DAO实现类
 */
public class RecentFileDaoImpl implements RecentFileDao {

    private final DatabaseHelper databaseHelper;

    @Inject
    public RecentFileDaoImpl(DatabaseHelper databaseHelper) {
        this.databaseHelper = databaseHelper;
    }

    @Override
    public Observable<List<RecentFile>> getAllRecentFiles() {
        return Observable.fromCallable(() -> {
            SQLiteDatabase db = databaseHelper.getReadableDatabase();
            List<RecentFile> recentFiles = new ArrayList<>();
            
            String query = "SELECT * FROM " + RecentFile.TABLE_NAME + 
                          " ORDER BY " + RecentFile.COLUMN_LAST_OPENED + " DESC";
            
            try (Cursor cursor = db.rawQuery(query, null)) {
                while (cursor.moveToNext()) {
                    recentFiles.add(cursorToRecentFile(cursor));
                }
            }
            
            return recentFiles;
        });
    }

    @Override
    public Observable<List<RecentFile>> getRecentFiles(int limit) {
        return Observable.fromCallable(() -> {
            SQLiteDatabase db = databaseHelper.getReadableDatabase();
            List<RecentFile> recentFiles = new ArrayList<>();
            
            String query = "SELECT * FROM " + RecentFile.TABLE_NAME + 
                          " ORDER BY " + RecentFile.COLUMN_LAST_OPENED + " DESC LIMIT ?";
            
            try (Cursor cursor = db.rawQuery(query, new String[]{String.valueOf(limit)})) {
                while (cursor.moveToNext()) {
                    recentFiles.add(cursorToRecentFile(cursor));
                }
            }
            
            return recentFiles;
        });
    }

    @Override
    public Single<RecentFile> getRecentFileByUri(String uri) {
        return Single.fromCallable(() -> {
            SQLiteDatabase db = databaseHelper.getReadableDatabase();
            
            String query = "SELECT * FROM " + RecentFile.TABLE_NAME + 
                          " WHERE " + RecentFile.COLUMN_URI + " = ?";
            
            try (Cursor cursor = db.rawQuery(query, new String[]{uri})) {
                if (cursor.moveToFirst()) {
                    return cursorToRecentFile(cursor);
                } else {
                    throw new RuntimeException("Recent file not found for URI: " + uri);
                }
            }
        });
    }

    @Override
    public Completable insertOrUpdateRecentFile(RecentFile recentFile) {
        return Completable.fromAction(() -> {
            SQLiteDatabase db = databaseHelper.getWritableDatabase();
            
            ContentValues values = new ContentValues();
            values.put(RecentFile.COLUMN_URI, recentFile.getUri());
            values.put(RecentFile.COLUMN_FILE_NAME, recentFile.getFileName());
            values.put(RecentFile.COLUMN_FILE_PATH, recentFile.getFilePath());
            values.put(RecentFile.COLUMN_FILE_SIZE, recentFile.getFileSize());
            values.put(RecentFile.COLUMN_LAST_MODIFIED, recentFile.getLastModified());
            values.put(RecentFile.COLUMN_LAST_OPENED, recentFile.getLastOpened());
            values.put(RecentFile.COLUMN_ENCODING, recentFile.getEncoding());
            values.put(RecentFile.COLUMN_CURSOR_POSITION, recentFile.getCursorPosition());
            values.put(RecentFile.COLUMN_SCROLL_X, recentFile.getScrollX());
            values.put(RecentFile.COLUMN_SCROLL_Y, recentFile.getScrollY());
            
            // 使用REPLACE策略，如果URI已存在则更新，否则插入
            db.replaceOrThrow(RecentFile.TABLE_NAME, null, values);
        });
    }

    @Override
    public Completable deleteRecentFile(RecentFile recentFile) {
        return deleteRecentFileByUri(recentFile.getUri());
    }

    @Override
    public Completable deleteRecentFileByUri(String uri) {
        return Completable.fromAction(() -> {
            SQLiteDatabase db = databaseHelper.getWritableDatabase();
            db.delete(RecentFile.TABLE_NAME, 
                     RecentFile.COLUMN_URI + " = ?", 
                     new String[]{uri});
        });
    }

    @Override
    public Completable clearAllRecentFiles() {
        return Completable.fromAction(() -> {
            SQLiteDatabase db = databaseHelper.getWritableDatabase();
            db.delete(RecentFile.TABLE_NAME, null, null);
        });
    }

    @Override
    public Completable deleteOldRecentFiles(int maxCount) {
        return Completable.fromAction(() -> {
            SQLiteDatabase db = databaseHelper.getWritableDatabase();
            
            String query = "DELETE FROM " + RecentFile.TABLE_NAME + 
                          " WHERE " + RecentFile.COLUMN_ID + " NOT IN (" +
                          "SELECT " + RecentFile.COLUMN_ID + " FROM " + RecentFile.TABLE_NAME +
                          " ORDER BY " + RecentFile.COLUMN_LAST_OPENED + " DESC LIMIT ?)";
            
            db.execSQL(query, new Object[]{maxCount});
        });
    }

    @Override
    public Completable updateEditorState(String uri, int cursorPosition, int scrollX, int scrollY) {
        return Completable.fromAction(() -> {
            SQLiteDatabase db = databaseHelper.getWritableDatabase();
            
            ContentValues values = new ContentValues();
            values.put(RecentFile.COLUMN_CURSOR_POSITION, cursorPosition);
            values.put(RecentFile.COLUMN_SCROLL_X, scrollX);
            values.put(RecentFile.COLUMN_SCROLL_Y, scrollY);
            
            db.update(RecentFile.TABLE_NAME, values, 
                     RecentFile.COLUMN_URI + " = ?", 
                     new String[]{uri});
        });
    }

    @Override
    public Single<Boolean> isFileInRecentList(String uri) {
        return Single.fromCallable(() -> {
            SQLiteDatabase db = databaseHelper.getReadableDatabase();
            
            String query = "SELECT COUNT(*) FROM " + RecentFile.TABLE_NAME + 
                          " WHERE " + RecentFile.COLUMN_URI + " = ?";
            
            try (Cursor cursor = db.rawQuery(query, new String[]{uri})) {
                if (cursor.moveToFirst()) {
                    return cursor.getInt(0) > 0;
                }
                return false;
            }
        });
    }

    /**
     * 将Cursor转换为RecentFile对象
     */
    private RecentFile cursorToRecentFile(Cursor cursor) {
        RecentFile recentFile = new RecentFile();
        
        recentFile.setId(cursor.getLong(cursor.getColumnIndexOrThrow(RecentFile.COLUMN_ID)));
        recentFile.setUri(cursor.getString(cursor.getColumnIndexOrThrow(RecentFile.COLUMN_URI)));
        recentFile.setFileName(cursor.getString(cursor.getColumnIndexOrThrow(RecentFile.COLUMN_FILE_NAME)));
        recentFile.setFilePath(cursor.getString(cursor.getColumnIndexOrThrow(RecentFile.COLUMN_FILE_PATH)));
        recentFile.setFileSize(cursor.getLong(cursor.getColumnIndexOrThrow(RecentFile.COLUMN_FILE_SIZE)));
        recentFile.setLastModified(cursor.getLong(cursor.getColumnIndexOrThrow(RecentFile.COLUMN_LAST_MODIFIED)));
        recentFile.setLastOpened(cursor.getLong(cursor.getColumnIndexOrThrow(RecentFile.COLUMN_LAST_OPENED)));
        recentFile.setEncoding(cursor.getString(cursor.getColumnIndexOrThrow(RecentFile.COLUMN_ENCODING)));
        recentFile.setCursorPosition(cursor.getInt(cursor.getColumnIndexOrThrow(RecentFile.COLUMN_CURSOR_POSITION)));
        recentFile.setScrollX(cursor.getInt(cursor.getColumnIndexOrThrow(RecentFile.COLUMN_SCROLL_X)));
        recentFile.setScrollY(cursor.getInt(cursor.getColumnIndexOrThrow(RecentFile.COLUMN_SCROLL_Y)));
        
        return recentFile;
    }
}