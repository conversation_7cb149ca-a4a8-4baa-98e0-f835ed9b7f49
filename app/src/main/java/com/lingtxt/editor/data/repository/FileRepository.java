package com.lingtxt.editor.data.repository;

import android.net.Uri;
import com.lingtxt.editor.data.model.FileContent;
import com.lingtxt.editor.data.model.FileInfo;
import io.reactivex.rxjava3.core.Completable;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.Single;

/**
 * 文件操作Repository接口
 */
public interface FileRepository {
    
    /**
     * 打开文件
     */
    Observable<FileContent> openFile(Uri uri);
    
    /**
     * 保存文件
     */
    Completable saveFile(Uri uri, String content);
    
    /**
     * 检测文件编码
     */
    Single<String> detectEncoding(Uri uri);
    
    /**
     * 使用指定编码读取文件
     */
    Observable<String> readWithEncoding(Uri uri, String encoding);
    
    /**
     * 分块读取大文件
     */
    Observable<String> readFileInChunks(Uri uri, String encoding, int chunkSize);
    
    /**
     * 获取文件信息（不读取内容）
     */
    Single<FileInfo> getFileInfoOnly(Uri uri);
    
    /**
     * 检查文件是否存在且可读
     */
    Single<Boolean> isFileAccessible(Uri uri);
}