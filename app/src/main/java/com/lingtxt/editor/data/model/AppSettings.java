package com.lingtxt.editor.data.model;

import androidx.annotation.NonNull;

/**
 * 应用设置数据模型
 */
public class AppSettings {
    private final float fontSize;
    private final String fontFamily;
    private final Theme theme;
    private final AppLanguage language;
    private final boolean showLineNumbers;
    private final boolean showInvisibleChars;
    private final boolean enableSyntaxHighlight;
    private final boolean enableCodeFolding;
    private final boolean enableAutoIndent;
    private final boolean enableWordWrap;
    private final int tabSize;
    private final boolean useSpacesForTabs;
    private final boolean enableGestures;
    private final boolean enableVoiceCommands;
    private final int maxRecentFiles;

    private AppSettings(Builder builder) {
        this.fontSize = builder.fontSize;
        this.fontFamily = builder.fontFamily;
        this.theme = builder.theme;
        this.language = builder.language;
        this.showLineNumbers = builder.showLineNumbers;
        this.showInvisibleChars = builder.showInvisibleChars;
        this.enableSyntaxHighlight = builder.enableSyntaxHighlight;
        this.enableCodeFolding = builder.enableCodeFolding;
        this.enableAutoIndent = builder.enableAutoIndent;
        this.enableWordWrap = builder.enableWordWrap;
        this.tabSize = builder.tabSize;
        this.useSpacesForTabs = builder.useSpacesForTabs;
        this.enableGestures = builder.enableGestures;
        this.enableVoiceCommands = builder.enableVoiceCommands;
        this.maxRecentFiles = builder.maxRecentFiles;
    }

    public float getFontSize() {
        return fontSize;
    }

    @NonNull
    public String getFontFamily() {
        return fontFamily;
    }

    @NonNull
    public Theme getTheme() {
        return theme;
    }

    @NonNull
    public AppLanguage getLanguage() {
        return language;
    }

    public boolean isShowLineNumbers() {
        return showLineNumbers;
    }

    public boolean isShowInvisibleChars() {
        return showInvisibleChars;
    }

    public boolean isEnableSyntaxHighlight() {
        return enableSyntaxHighlight;
    }

    public boolean isEnableCodeFolding() {
        return enableCodeFolding;
    }

    public boolean isEnableAutoIndent() {
        return enableAutoIndent;
    }

    public boolean isEnableWordWrap() {
        return enableWordWrap;
    }

    public int getTabSize() {
        return tabSize;
    }

    public boolean isUseSpacesForTabs() {
        return useSpacesForTabs;
    }

    public boolean isEnableGestures() {
        return enableGestures;
    }

    public boolean isEnableVoiceCommands() {
        return enableVoiceCommands;
    }

    public int getMaxRecentFiles() {
        return maxRecentFiles;
    }

    /**
     * 获取默认设置
     */
    public static AppSettings getDefault() {
        return new Builder().build();
    }

    public static class Builder {
        private float fontSize = 14f;
        private String fontFamily = "monospace";
        private Theme theme = Theme.SYSTEM;
        private AppLanguage language = AppLanguage.SYSTEM;
        private boolean showLineNumbers = true;
        private boolean showInvisibleChars = false;
        private boolean enableSyntaxHighlight = true;
        private boolean enableCodeFolding = true;
        private boolean enableAutoIndent = true;
        private boolean enableWordWrap = false;
        private int tabSize = 4;
        private boolean useSpacesForTabs = true;
        private boolean enableGestures = true;
        private boolean enableVoiceCommands = false;
        private int maxRecentFiles = 10;

        public Builder setFontSize(float fontSize) {
            this.fontSize = Math.max(8f, Math.min(72f, fontSize));
            return this;
        }

        public Builder setFontFamily(@NonNull String fontFamily) {
            this.fontFamily = fontFamily;
            return this;
        }

        public Builder setTheme(@NonNull Theme theme) {
            this.theme = theme;
            return this;
        }

        public Builder setLanguage(@NonNull AppLanguage language) {
            this.language = language;
            return this;
        }

        public Builder setShowLineNumbers(boolean showLineNumbers) {
            this.showLineNumbers = showLineNumbers;
            return this;
        }

        public Builder setShowInvisibleChars(boolean showInvisibleChars) {
            this.showInvisibleChars = showInvisibleChars;
            return this;
        }

        public Builder setEnableSyntaxHighlight(boolean enableSyntaxHighlight) {
            this.enableSyntaxHighlight = enableSyntaxHighlight;
            return this;
        }

        public Builder setEnableCodeFolding(boolean enableCodeFolding) {
            this.enableCodeFolding = enableCodeFolding;
            return this;
        }

        public Builder setEnableAutoIndent(boolean enableAutoIndent) {
            this.enableAutoIndent = enableAutoIndent;
            return this;
        }

        public Builder setEnableWordWrap(boolean enableWordWrap) {
            this.enableWordWrap = enableWordWrap;
            return this;
        }

        public Builder setTabSize(int tabSize) {
            this.tabSize = Math.max(1, Math.min(8, tabSize));
            return this;
        }

        public Builder setUseSpacesForTabs(boolean useSpacesForTabs) {
            this.useSpacesForTabs = useSpacesForTabs;
            return this;
        }

        public Builder setEnableGestures(boolean enableGestures) {
            this.enableGestures = enableGestures;
            return this;
        }

        public Builder setEnableVoiceCommands(boolean enableVoiceCommands) {
            this.enableVoiceCommands = enableVoiceCommands;
            return this;
        }

        public Builder setMaxRecentFiles(int maxRecentFiles) {
            this.maxRecentFiles = Math.max(1, Math.min(50, maxRecentFiles));
            return this;
        }

        public AppSettings build() {
            return new AppSettings(this);
        }
    }

    @Override
    public String toString() {
        return "AppSettings{" +
                "fontSize=" + fontSize +
                ", fontFamily='" + fontFamily + '\'' +
                ", theme=" + theme +
                ", language=" + language +
                '}';
    }
}