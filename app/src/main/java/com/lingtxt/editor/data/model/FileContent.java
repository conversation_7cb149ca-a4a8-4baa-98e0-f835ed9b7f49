package com.lingtxt.editor.data.model;

/**
 * 文件内容数据模型
 */
public class FileContent {
    private final String content;
    private final String encoding;
    private final long size;
    private final long lastModified;

    public FileContent(String content, String encoding, long size, long lastModified) {
        this.content = content;
        this.encoding = encoding;
        this.size = size;
        this.lastModified = lastModified;
    }

    public String getContent() {
        return content;
    }

    public String getEncoding() {
        return encoding;
    }

    public long getSize() {
        return size;
    }

    public long getLastModified() {
        return lastModified;
    }
}