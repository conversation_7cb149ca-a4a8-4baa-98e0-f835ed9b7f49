package com.lingtxt.editor.data.database.dao;

import com.lingtxt.editor.data.database.entity.RecentFile;
import io.reactivex.rxjava3.core.Completable;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.Single;
import java.util.List;

/**
 * 最近文件DAO接口
 */
public interface RecentFileDao {

    /**
     * 获取所有最近文件，按最后打开时间降序排列
     */
    Observable<List<RecentFile>> getAllRecentFiles();

    /**
     * 获取指定数量的最近文件
     */
    Observable<List<RecentFile>> getRecentFiles(int limit);

    /**
     * 根据URI获取最近文件
     */
    Single<RecentFile> getRecentFileByUri(String uri);

    /**
     * 插入或更新最近文件
     */
    Completable insertOrUpdateRecentFile(RecentFile recentFile);

    /**
     * 删除最近文件
     */
    Completable deleteRecentFile(RecentFile recentFile);

    /**
     * 根据URI删除最近文件
     */
    Completable deleteRecentFileByUri(String uri);

    /**
     * 清空所有最近文件
     */
    Completable clearAllRecentFiles();

    /**
     * 删除超过指定数量的旧文件记录
     */
    Completable deleteOldRecentFiles(int maxCount);

    /**
     * 更新文件的编辑器状态
     */
    Completable updateEditorState(String uri, int cursorPosition, int scrollX, int scrollY);

    /**
     * 检查文件是否存在于最近文件中
     */
    Single<Boolean> isFileInRecentList(String uri);
}