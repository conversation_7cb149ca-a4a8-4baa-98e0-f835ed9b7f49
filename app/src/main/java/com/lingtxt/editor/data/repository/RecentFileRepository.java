package com.lingtxt.editor.data.repository;

import android.content.Context;
import android.content.SharedPreferences;
import android.net.Uri;
import com.lingtxt.editor.data.model.RecentFile;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import io.reactivex.rxjava3.core.Completable;
import io.reactivex.rxjava3.core.Single;
import io.reactivex.rxjava3.schedulers.Schedulers;

/**
 * 最近文件仓库
 * 管理最近访问的文件列表
 */
public class RecentFileRepository {
    
    private static final String PREFS_NAME = "recent_files";
    private static final String KEY_RECENT_FILES = "recent_files_list";
    private static final int MAX_RECENT_FILES = 20; // 最大保存20个最近文件
    
    private final SharedPreferences preferences;
    
    public RecentFileRepository(Context context) {
        preferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
    }
    
    /**
     * 获取最近文件列表
     */
    public Single<List<RecentFile>> getRecentFiles() {
        return Single.fromCallable(() -> {
            String json = preferences.getString(KEY_RECENT_FILES, "[]");
            List<RecentFile> recentFiles = parseRecentFilesFromJson(json);
            
            // 按访问时间排序（最新的在前）
            Collections.sort(recentFiles, (a, b) -> 
                Long.compare(b.getLastAccessed(), a.getLastAccessed()));
            
            return recentFiles;
        }).subscribeOn(Schedulers.io());
    }
    
    /**
     * 添加最近文件
     */
    public Completable addRecentFile(Uri uri, String fileName, String filePath, 
                                   long fileSize, String encoding, String mimeType) {
        return Completable.fromAction(() -> {
            List<RecentFile> recentFiles = getRecentFiles().blockingGet();
            
            String uriString = uri.toString();
            long currentTime = System.currentTimeMillis();
            
            // 移除已存在的相同文件
            recentFiles.removeIf(file -> uriString.equals(file.getUri()));
            
            // 添加新文件到开头
            RecentFile newFile = new RecentFile(uriString, fileName, filePath, 
                                              fileSize, currentTime, encoding, mimeType);
            recentFiles.add(0, newFile);
            
            // 限制列表大小
            while (recentFiles.size() > MAX_RECENT_FILES) {
                recentFiles.remove(recentFiles.size() - 1);
            }
            
            // 保存到SharedPreferences
            String json = convertRecentFilesToJson(recentFiles);
            preferences.edit().putString(KEY_RECENT_FILES, json).apply();
            
        }).subscribeOn(Schedulers.io());
    }
    
    /**
     * 移除最近文件
     */
    public Completable removeRecentFile(String uri) {
        return Completable.fromAction(() -> {
            List<RecentFile> recentFiles = getRecentFiles().blockingGet();
            recentFiles.removeIf(file -> uri.equals(file.getUri()));
            
            String json = convertRecentFilesToJson(recentFiles);
            preferences.edit().putString(KEY_RECENT_FILES, json).apply();
            
        }).subscribeOn(Schedulers.io());
    }
    
    /**
     * 清空最近文件列表
     */
    public Completable clearRecentFiles() {
        return Completable.fromAction(() -> {
            preferences.edit().remove(KEY_RECENT_FILES).apply();
        }).subscribeOn(Schedulers.io());
    }
    
    /**
     * 更新文件访问时间
     */
    public Completable updateAccessTime(Uri uri) {
        return Completable.fromAction(() -> {
            List<RecentFile> recentFiles = getRecentFiles().blockingGet();
            String uriString = uri.toString();
            
            for (int i = 0; i < recentFiles.size(); i++) {
                RecentFile file = recentFiles.get(i);
                if (uriString.equals(file.getUri())) {
                    // 更新访问时间并移到开头
                    RecentFile updatedFile = new RecentFile(
                        file.getUri(), file.getFileName(), file.getFilePath(),
                        file.getFileSize(), System.currentTimeMillis(),
                        file.getEncoding(), file.getMimeType()
                    );
                    
                    recentFiles.remove(i);
                    recentFiles.add(0, updatedFile);
                    
                    String json = convertRecentFilesToJson(recentFiles);
                    preferences.edit().putString(KEY_RECENT_FILES, json).apply();
                    break;
                }
            }
        }).subscribeOn(Schedulers.io());
    }
    
    /**
     * 检查文件是否在最近列表中
     */
    public Single<Boolean> isFileInRecentList(Uri uri) {
        return Single.fromCallable(() -> {
            List<RecentFile> recentFiles = getRecentFiles().blockingGet();
            String uriString = uri.toString();
            
            return recentFiles.stream()
                .anyMatch(file -> uriString.equals(file.getUri()));
                
        }).subscribeOn(Schedulers.io());
    }
    
    /**
     * 获取最近文件数量
     */
    public Single<Integer> getRecentFileCount() {
        return getRecentFiles().map(List::size);
    }

    /**
     * 从JSON字符串解析最近文件列表
     */
    private List<RecentFile> parseRecentFilesFromJson(String json) {
        List<RecentFile> recentFiles = new ArrayList<>();
        
        try {
            JSONArray jsonArray = new JSONArray(json);
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                
                String uri = jsonObject.getString("uri");
                String fileName = jsonObject.getString("fileName");
                String filePath = jsonObject.getString("filePath");
                long fileSize = jsonObject.getLong("fileSize");
                long lastAccessed = jsonObject.getLong("lastAccessed");
                String encoding = jsonObject.optString("encoding", "UTF-8");
                String mimeType = jsonObject.optString("mimeType", "text/plain");
                
                RecentFile file = new RecentFile(uri, fileName, filePath, 
                                               fileSize, lastAccessed, encoding, mimeType);
                recentFiles.add(file);
            }
        } catch (JSONException e) {
            // 解析失败，返回空列表
            recentFiles.clear();
        }
        
        return recentFiles;
    }

    /**
     * 将最近文件列表转换为JSON字符串
     */
    private String convertRecentFilesToJson(List<RecentFile> recentFiles) {
        try {
            JSONArray jsonArray = new JSONArray();
            
            for (RecentFile file : recentFiles) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("uri", file.getUri());
                jsonObject.put("fileName", file.getFileName());
                jsonObject.put("filePath", file.getFilePath());
                jsonObject.put("fileSize", file.getFileSize());
                jsonObject.put("lastAccessed", file.getLastAccessed());
                jsonObject.put("encoding", file.getEncoding());
                jsonObject.put("mimeType", file.getMimeType());
                
                jsonArray.put(jsonObject);
            }
            
            return jsonArray.toString();
        } catch (JSONException e) {
            // 转换失败，返回空数组
            return "[]";
        }
    }
}