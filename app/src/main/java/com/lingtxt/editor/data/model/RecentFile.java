package com.lingtxt.editor.data.model;

import android.net.Uri;

/**
 * 最近文件数据模型
 */
public class RecentFile {
    private final String uri;
    private final String fileName;
    private final String filePath;
    private final long fileSize;
    private final long lastAccessed;
    private final String encoding;
    private final String mimeType;
    
    public RecentFile(String uri, String fileName, String filePath, long fileSize, 
                     long lastAccessed, String encoding, String mimeType) {
        this.uri = uri;
        this.fileName = fileName;
        this.filePath = filePath;
        this.fileSize = fileSize;
        this.lastAccessed = lastAccessed;
        this.encoding = encoding;
        this.mimeType = mimeType;
    }
    
    // Getters
    public String getUri() {
        return uri;
    }
    
    public Uri getUriObject() {
        return Uri.parse(uri);
    }
    
    public String getFileName() {
        return fileName;
    }
    
    public String getFilePath() {
        return filePath;
    }
    
    public long getFileSize() {
        return fileSize;
    }
    
    public long getLastAccessed() {
        return lastAccessed;
    }
    
    public String getEncoding() {
        return encoding;
    }
    
    public String getMimeType() {
        return mimeType;
    }
    
    /**
     * 获取格式化的文件大小
     */
    public String getFormattedFileSize() {
        if (fileSize < 1024) {
            return fileSize + " B";
        } else if (fileSize < 1024 * 1024) {
            return String.format("%.1f KB", fileSize / 1024.0);
        } else {
            return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
        }
    }
    
    /**
     * 获取文件类型图标资源ID
     */
    public int getFileTypeIcon() {
        if (fileName == null) return android.R.drawable.ic_menu_edit;
        
        String extension = getFileExtension().toLowerCase();
        switch (extension) {
            case "java":
                return android.R.drawable.ic_menu_edit;
            case "xml":
            case "html":
                return android.R.drawable.ic_menu_view;
            case "json":
            case "yaml":
            case "yml":
                return android.R.drawable.ic_menu_info_details;
            case "md":
                return android.R.drawable.ic_menu_agenda;
            case "js":
            case "css":
                return android.R.drawable.ic_menu_compass;
            case "py":
            case "go":
            case "c":
            case "cpp":
            case "h":
                return android.R.drawable.ic_menu_manage;
            default:
                return android.R.drawable.ic_menu_edit;
        }
    }
    
    /**
     * 获取文件扩展名
     */
    public String getFileExtension() {
        if (fileName == null) return "";
        int lastDot = fileName.lastIndexOf('.');
        if (lastDot == -1 || lastDot == fileName.length() - 1) {
            return "";
        }
        return fileName.substring(lastDot + 1);
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        RecentFile that = (RecentFile) obj;
        return uri != null ? uri.equals(that.uri) : that.uri == null;
    }
    
    @Override
    public int hashCode() {
        return uri != null ? uri.hashCode() : 0;
    }
}