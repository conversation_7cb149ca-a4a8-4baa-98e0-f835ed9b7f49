package com.lingtxt.editor.data.model;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * 编辑器状态数据模型
 */
public class EditorState {
    private final String content;
    private final int cursorPosition;
    private final int selectionStart;
    private final int selectionEnd;
    private final int scrollX;
    private final int scrollY;
    private final float fontSize;
    private final boolean isModified;
    private final boolean isReadOnly;
    private final boolean showLineNumbers;
    private final boolean showInvisibleChars;
    private final boolean enableSyntaxHighlight;

    private EditorState(Builder builder) {
        this.content = builder.content;
        this.cursorPosition = builder.cursorPosition;
        this.selectionStart = builder.selectionStart;
        this.selectionEnd = builder.selectionEnd;
        this.scrollX = builder.scrollX;
        this.scrollY = builder.scrollY;
        this.fontSize = builder.fontSize;
        this.isModified = builder.isModified;
        this.isReadOnly = builder.isReadOnly;
        this.showLineNumbers = builder.showLineNumbers;
        this.showInvisibleChars = builder.showInvisibleChars;
        this.enableSyntaxHighlight = builder.enableSyntaxHighlight;
    }

    @NonNull
    public String getContent() {
        return content != null ? content : "";
    }

    public int getCursorPosition() {
        return cursorPosition;
    }

    public int getSelectionStart() {
        return selectionStart;
    }

    public int getSelectionEnd() {
        return selectionEnd;
    }

    public int getScrollX() {
        return scrollX;
    }

    public int getScrollY() {
        return scrollY;
    }

    public float getFontSize() {
        return fontSize;
    }

    public boolean isModified() {
        return isModified;
    }

    public boolean isReadOnly() {
        return isReadOnly;
    }

    public boolean isShowLineNumbers() {
        return showLineNumbers;
    }

    public boolean isShowInvisibleChars() {
        return showInvisibleChars;
    }

    public boolean isEnableSyntaxHighlight() {
        return enableSyntaxHighlight;
    }

    /**
     * 判断是否有选中的文本
     */
    public boolean hasSelection() {
        return selectionStart != selectionEnd;
    }

    /**
     * 获取选中的文本
     */
    @Nullable
    public String getSelectedText() {
        if (!hasSelection() || content == null) {
            return null;
        }
        int start = Math.max(0, Math.min(selectionStart, selectionEnd));
        int end = Math.min(content.length(), Math.max(selectionStart, selectionEnd));
        return content.substring(start, end);
    }

    /**
     * 获取当前行号（从1开始）
     */
    public int getCurrentLineNumber() {
        if (content == null || cursorPosition < 0) {
            return 1;
        }
        int lineNumber = 1;
        for (int i = 0; i < Math.min(cursorPosition, content.length()); i++) {
            if (content.charAt(i) == '\n') {
                lineNumber++;
            }
        }
        return lineNumber;
    }

    /**
     * 获取总行数
     */
    public int getTotalLines() {
        if (content == null || content.isEmpty()) {
            return 1;
        }
        int lines = 1;
        for (int i = 0; i < content.length(); i++) {
            if (content.charAt(i) == '\n') {
                lines++;
            }
        }
        return lines;
    }

    public static class Builder {
        private String content = "";
        private int cursorPosition = 0;
        private int selectionStart = 0;
        private int selectionEnd = 0;
        private int scrollX = 0;
        private int scrollY = 0;
        private float fontSize = 14f;
        private boolean isModified = false;
        private boolean isReadOnly = false;
        private boolean showLineNumbers = true;
        private boolean showInvisibleChars = false;
        private boolean enableSyntaxHighlight = true;

        public Builder setContent(@Nullable String content) {
            this.content = content != null ? content : "";
            return this;
        }

        public Builder setCursorPosition(int cursorPosition) {
            this.cursorPosition = Math.max(0, cursorPosition);
            return this;
        }

        public Builder setSelection(int start, int end) {
            this.selectionStart = Math.max(0, start);
            this.selectionEnd = Math.max(0, end);
            return this;
        }

        public Builder setScroll(int x, int y) {
            this.scrollX = Math.max(0, x);
            this.scrollY = Math.max(0, y);
            return this;
        }

        public Builder setFontSize(float fontSize) {
            this.fontSize = Math.max(8f, Math.min(72f, fontSize));
            return this;
        }

        public Builder setModified(boolean isModified) {
            this.isModified = isModified;
            return this;
        }

        public Builder setReadOnly(boolean isReadOnly) {
            this.isReadOnly = isReadOnly;
            return this;
        }

        public Builder setShowLineNumbers(boolean showLineNumbers) {
            this.showLineNumbers = showLineNumbers;
            return this;
        }

        public Builder setShowInvisibleChars(boolean showInvisibleChars) {
            this.showInvisibleChars = showInvisibleChars;
            return this;
        }

        public Builder setEnableSyntaxHighlight(boolean enableSyntaxHighlight) {
            this.enableSyntaxHighlight = enableSyntaxHighlight;
            return this;
        }

        public EditorState build() {
            return new EditorState(this);
        }
    }

    @Override
    public String toString() {
        return "EditorState{" +
                "cursorPosition=" + cursorPosition +
                ", fontSize=" + fontSize +
                ", isModified=" + isModified +
                ", currentLine=" + getCurrentLineNumber() +
                ", totalLines=" + getTotalLines() +
                '}';
    }
}