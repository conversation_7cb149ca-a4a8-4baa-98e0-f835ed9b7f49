package com.lingtxt.editor.data.database;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import com.lingtxt.editor.data.database.entity.RecentFile;
import javax.inject.Inject;
import javax.inject.Singleton;

/**
 * SQLite数据库帮助类
 */
@Singleton
public class DatabaseHelper extends SQLiteOpenHelper {

    private static final String DATABASE_NAME = "lingtxt.db";
    private static final int DATABASE_VERSION = 1;

    // 创建最近文件表的SQL语句
    private static final String CREATE_RECENT_FILES_TABLE = 
        "CREATE TABLE " + RecentFile.TABLE_NAME + " (" +
        RecentFile.COLUMN_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
        RecentFile.COLUMN_URI + " TEXT UNIQUE NOT NULL, " +
        RecentFile.COLUMN_FILE_NAME + " TEXT NOT NULL, " +
        RecentFile.COLUMN_FILE_PATH + " TEXT, " +
        RecentFile.COLUMN_FILE_SIZE + " INTEGER NOT NULL DEFAULT 0, " +
        RecentFile.COLUMN_LAST_MODIFIED + " INTEGER NOT NULL DEFAULT 0, " +
        RecentFile.COLUMN_LAST_OPENED + " INTEGER NOT NULL DEFAULT 0, " +
        RecentFile.COLUMN_ENCODING + " TEXT NOT NULL DEFAULT 'UTF-8', " +
        RecentFile.COLUMN_CURSOR_POSITION + " INTEGER NOT NULL DEFAULT 0, " +
        RecentFile.COLUMN_SCROLL_X + " INTEGER NOT NULL DEFAULT 0, " +
        RecentFile.COLUMN_SCROLL_Y + " INTEGER NOT NULL DEFAULT 0" +
        ");";

    // 创建索引的SQL语句
    private static final String CREATE_RECENT_FILES_URI_INDEX = 
        "CREATE INDEX idx_recent_files_uri ON " + RecentFile.TABLE_NAME + 
        " (" + RecentFile.COLUMN_URI + ");";

    private static final String CREATE_RECENT_FILES_LAST_OPENED_INDEX = 
        "CREATE INDEX idx_recent_files_last_opened ON " + RecentFile.TABLE_NAME + 
        " (" + RecentFile.COLUMN_LAST_OPENED + " DESC);";

    @Inject
    public DatabaseHelper(Context context) {
        super(context, DATABASE_NAME, null, DATABASE_VERSION);
    }

    @Override
    public void onCreate(SQLiteDatabase db) {
        // 创建表
        db.execSQL(CREATE_RECENT_FILES_TABLE);
        
        // 创建索引
        db.execSQL(CREATE_RECENT_FILES_URI_INDEX);
        db.execSQL(CREATE_RECENT_FILES_LAST_OPENED_INDEX);
    }

    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        // 目前只有版本1，暂时简单处理
        if (oldVersion < newVersion) {
            // 删除旧表并重新创建
            db.execSQL("DROP TABLE IF EXISTS " + RecentFile.TABLE_NAME);
            onCreate(db);
        }
    }

    @Override
    public void onDowngrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        // 降级时也重新创建表
        onUpgrade(db, oldVersion, newVersion);
    }

    @Override
    public void onConfigure(SQLiteDatabase db) {
        super.onConfigure(db);
        // 启用外键约束
        db.setForeignKeyConstraintsEnabled(true);
    }

    /**
     * 清空所有数据（用于测试或重置）
     */
    public void clearAllData() {
        SQLiteDatabase db = getWritableDatabase();
        db.delete(RecentFile.TABLE_NAME, null, null);
    }

    /**
     * 获取数据库大小（字节）
     */
    public long getDatabaseSize() {
        SQLiteDatabase db = getReadableDatabase();
        return db.getPath() != null ? new java.io.File(db.getPath()).length() : 0;
    }

    /**
     * 优化数据库（VACUUM操作）
     */
    public void optimizeDatabase() {
        SQLiteDatabase db = getWritableDatabase();
        db.execSQL("VACUUM");
    }
}