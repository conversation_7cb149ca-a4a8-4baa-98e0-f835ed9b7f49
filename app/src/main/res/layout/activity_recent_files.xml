<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">

    <!-- 顶部工具栏 -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fitsSystemWindows="true">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorSurface"
            app:title="@string/recent_files_title"
            app:titleTextColor="?attr/colorOnSurface"
            app:navigationIcon="@drawable/ic_arrow_back"
            app:menu="@menu/recent_files_menu" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- 主要内容 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <!-- 最近文件列表 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_recent_files"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:clipToPadding="false"
            android:paddingTop="8dp"
            android:paddingBottom="16dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            tools:listitem="@layout/item_recent_file" />

        <!-- 空状态 -->
        <LinearLayout
            android:id="@+id/layout_empty_state"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <ImageView
                android:layout_width="96dp"
                android:layout_height="96dp"
                android:src="@drawable/ic_history"
                android:alpha="0.6"
                android:layout_marginBottom="16dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/no_recent_files_title"
                android:textSize="18sp"
                android:textColor="?attr/colorOnSurface"
                android:layout_marginBottom="8dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/no_recent_files_message"
                android:textSize="14sp"
                android:textColor="?attr/colorOnSurfaceVariant"
                android:gravity="center" />

        </LinearLayout>

        <!-- 加载指示器 -->
        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 浮动操作按钮 -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_open_file"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:src="@drawable/ic_folder_open"
        android:contentDescription="@string/open_file"
        app:tint="?attr/colorOnPrimary" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>