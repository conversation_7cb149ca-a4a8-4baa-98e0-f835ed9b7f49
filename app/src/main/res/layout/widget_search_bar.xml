<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="?attr/colorSurface"
    android:elevation="4dp"
    android:padding="8dp">

    <!-- 搜索行 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="4dp">

        <!-- 搜索输入框 -->
        <AutoCompleteTextView
            android:id="@+id/et_search_text"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:hint="@string/search_hint"
            android:imeOptions="actionSearch"
            android:inputType="text"
            android:background="@drawable/bg_search_input"
            android:paddingHorizontal="12dp"
            android:textSize="14sp"
            android:layout_marginEnd="8dp"
            android:completionThreshold="1"
            android:dropDownHeight="200dp" />

        <!-- 导航按钮 -->
        <ImageButton
            android:id="@+id/btn_previous"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_arrow_up"
            android:contentDescription="@string/find_previous"
            android:layout_marginEnd="4dp" />

        <ImageButton
            android:id="@+id/btn_next"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_arrow_down"
            android:contentDescription="@string/find_next"
            android:layout_marginEnd="8dp" />

        <!-- 搜索结果显示 -->
        <com.lingtxt.editor.ui.widget.NonEditableTextView
            android:id="@+id/tv_search_result"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="0 / 0"
            android:textSize="12sp"
            android:textColor="?attr/colorOnSurface"
            android:layout_marginEnd="8dp"
            android:minWidth="48dp"
            android:gravity="center" />

        <!-- 关闭按钮 -->
        <ImageButton
            android:id="@+id/btn_close"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_close"
            android:contentDescription="@string/close" />

    </LinearLayout>

    <!-- 替换行 -->
    <LinearLayout
        android:id="@+id/layout_replace"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="4dp"
        android:visibility="gone">

        <!-- 替换输入框 -->
        <AutoCompleteTextView
            android:id="@+id/et_replace_text"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:hint="@string/replace_hint"
            android:imeOptions="actionDone"
            android:inputType="text"
            android:background="@drawable/bg_search_input"
            android:paddingHorizontal="12dp"
            android:textSize="14sp"
            android:layout_marginEnd="8dp"
            android:completionThreshold="1"
            android:dropDownHeight="200dp" />

        <!-- 替换按钮 -->
        <ImageButton
            android:id="@+id/btn_replace"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_replace_one"
            android:contentDescription="@string/replace_current"
            android:layout_marginEnd="4dp" />

        <ImageButton
            android:id="@+id/btn_replace_all"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_replace_all"
            android:contentDescription="@string/replace_all"
            android:layout_marginEnd="8dp" />

        <!-- 占位空间 -->
        <View
            android:layout_width="48dp"
            android:layout_height="1dp" />

        <!-- 占位空间 -->
        <View
            android:layout_width="36dp"
            android:layout_height="1dp" />

    </LinearLayout>

    <!-- 搜索选项 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <CheckBox
            android:id="@+id/cb_case_sensitive"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/case_sensitive"
            android:textSize="12sp"
            android:layout_marginEnd="16dp" />

        <CheckBox
            android:id="@+id/cb_whole_word"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/whole_word"
            android:textSize="12sp"
            android:layout_marginEnd="16dp" />

        <CheckBox
            android:id="@+id/cb_regex"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/regex"
            android:textSize="12sp" />

    </LinearLayout>

</LinearLayout>