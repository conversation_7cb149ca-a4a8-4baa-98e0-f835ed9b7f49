<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:background="@drawable/search_bar_background"
    android:padding="12dp"
    android:elevation="8dp">

    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:layout_marginEnd="8dp"
        app:boxBackgroundMode="none"
        app:hintEnabled="false">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_line_number"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/input_line_number_hint"
            android:inputType="number"
            android:textSize="16sp"
            android:background="@null"
            android:maxLines="1"
            android:imeOptions="actionGo" />

    </com.google.android.material.textfield.TextInputLayout>

    <TextView
        android:id="@+id/tv_max_line"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:text="最大行数: 0"
        android:textSize="12sp"
        android:textColor="?android:attr/textColorSecondary"
        android:layout_marginEnd="8dp" />

    <ImageButton
        android:id="@+id/btn_goto"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_gravity="center_vertical"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:src="@drawable/ic_arrow_forward"
        android:contentDescription="@string/goto_line"
        android:layout_marginEnd="4dp" />

    <ImageButton
        android:id="@+id/btn_close"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_gravity="center_vertical"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:src="@drawable/ic_close"
        android:contentDescription="@string/close" />

</LinearLayout>