<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="16dp"
    android:layout_marginVertical="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    app:strokeWidth="0dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- 文件类型图标 -->
        <ImageView
            android:id="@+id/iv_file_icon"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@drawable/ic_document_empty"
            android:scaleType="centerInside"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:src="@drawable/ic_document_empty" />

        <!-- 文件信息 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="8dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toEndOf="@id/iv_file_icon"
            app:layout_constraintEnd_toStartOf="@id/btn_more"
            app:layout_constraintBottom_toBottomOf="parent">

            <!-- 文件名 -->
            <TextView
                android:id="@+id/tv_file_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="16sp"
                android:textColor="?attr/colorOnSurface"
                android:textStyle="bold"
                android:maxLines="1"
                android:ellipsize="middle"
                tools:text="MainActivity.java" />

            <!-- 文件路径 -->
            <TextView
                android:id="@+id/tv_file_path"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                android:textColor="?attr/colorOnSurfaceVariant"
                android:maxLines="1"
                android:ellipsize="start"
                android:layout_marginTop="2dp"
                tools:text="/storage/emulated/0/Documents/project/src/main/java/MainActivity.java" />

            <!-- 文件详情 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="4dp">

                <TextView
                    android:id="@+id/tv_file_size"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="11sp"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    tools:text="2.5 KB" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text=" • "
                    android:textSize="11sp"
                    android:textColor="?attr/colorOnSurfaceVariant" />

                <TextView
                    android:id="@+id/tv_last_accessed"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="11sp"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    tools:text="2小时前" />

                <TextView
                    android:id="@+id/tv_encoding"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="11sp"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    android:layout_marginStart="8dp"
                    android:background="@drawable/bg_encoding_tag"
                    android:paddingHorizontal="6dp"
                    android:paddingVertical="2dp"
                    tools:text="UTF-8" />

            </LinearLayout>

        </LinearLayout>

        <!-- 更多操作按钮 -->
        <ImageButton
            android:id="@+id/btn_more"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_more_vert"
            android:contentDescription="更多操作"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>