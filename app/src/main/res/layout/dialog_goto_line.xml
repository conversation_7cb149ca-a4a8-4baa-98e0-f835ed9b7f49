<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp">

    <!-- 标题 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="跳转到行"
        android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall"
        android:textColor="?attr/colorOnSurface"
        android:layout_marginBottom="16dp"
        android:gravity="center" />

    <!-- 最大行数提示 -->
    <TextView
        android:id="@+id/tv_max_line"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="最大行数: 0"
        android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
        android:textColor="?attr/colorOnSurfaceVariant"
        android:layout_marginBottom="16dp"
        android:gravity="center" />

    <!-- 输入框 -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        app:boxStrokeColor="?attr/colorPrimary"
        app:hintTextColor="?attr/colorPrimary">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_line_number"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="输入行号"
            android:inputType="number"
            android:textSize="16sp"
            android:gravity="center" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- 按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="取消"
            android:layout_marginEnd="8dp"
            style="@style/Widget.Material3.Button.TextButton" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_goto"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="跳转" />

    </LinearLayout>

</LinearLayout>