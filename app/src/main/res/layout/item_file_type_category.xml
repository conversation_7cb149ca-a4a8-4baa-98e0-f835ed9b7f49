<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    app:strokeWidth="1dp"
    app:strokeColor="?attr/colorOutline">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

        <!-- 分类标题和数量 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/tv_category_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="编程语言"
                android:textAppearance="?attr/textAppearanceSubtitle1"
                android:textStyle="bold"
                android:textColor="?attr/colorPrimary" />

            <com.google.android.material.chip.Chip
                android:id="@+id/tv_extensions_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="25"
                android:textSize="12sp"
                style="@style/Widget.Material3.Chip.Assist" />

        </LinearLayout>

        <!-- 扩展名列表 -->
        <TextView
            android:id="@+id/tv_extensions_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="java, kt, scala, groovy, py, rb, php, js, ts, jsx, tsx, c, cpp, cc, cxx, h, hpp, cs, vb, fs, go, rs, swift, m, mm"
            android:textAppearance="?attr/textAppearanceCaption"
            android:textColor="?attr/colorOnSurfaceVariant"
            android:layout_marginTop="4dp"
            android:maxLines="3"
            android:ellipsize="end" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
