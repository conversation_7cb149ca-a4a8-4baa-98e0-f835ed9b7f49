<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="24dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="文件信息"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="?android:attr/textColorPrimary"
            android:layout_marginBottom="16dp"
            android:gravity="center" />

        <!-- 文件名 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="12dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="文件名："
                android:textStyle="bold"
                android:textColor="?android:attr/textColorPrimary" />

            <TextView
                android:id="@+id/tv_file_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:text="--"
                android:textColor="?android:attr/textColorSecondary"
                android:textIsSelectable="true" />

        </LinearLayout>

        <!-- 文件路径 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="12dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="文件路径："
                android:textStyle="bold"
                android:textColor="?android:attr/textColorPrimary" />

            <TextView
                android:id="@+id/tv_file_path"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:text="--"
                android:textColor="?android:attr/textColorSecondary"
                android:textIsSelectable="true" />

        </LinearLayout>

        <!-- 文件大小 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="12dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="文件大小："
                android:textStyle="bold"
                android:textColor="?android:attr/textColorPrimary" />

            <TextView
                android:id="@+id/tv_file_size"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:text="--"
                android:textColor="?android:attr/textColorSecondary"
                android:textIsSelectable="true" />

        </LinearLayout>

        <!-- 创建时间 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="12dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="创建时间："
                android:textStyle="bold"
                android:textColor="?android:attr/textColorPrimary" />

            <TextView
                android:id="@+id/tv_create_time"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:text="--"
                android:textColor="?android:attr/textColorSecondary"
                android:textIsSelectable="true" />

        </LinearLayout>

        <!-- 修改时间 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="12dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="修改时间："
                android:textStyle="bold"
                android:textColor="?android:attr/textColorPrimary" />

            <TextView
                android:id="@+id/tv_modify_time"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:text="--"
                android:textColor="?android:attr/textColorSecondary"
                android:textIsSelectable="true" />

        </LinearLayout>

        <!-- 读写属性 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="12dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="读写属性："
                android:textStyle="bold"
                android:textColor="?android:attr/textColorPrimary" />

            <TextView
                android:id="@+id/tv_permissions"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:text="--"
                android:textColor="?android:attr/textColorSecondary"
                android:textIsSelectable="true" />

        </LinearLayout>

        <!-- 编码格式 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="12dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="编码格式："
                android:textStyle="bold"
                android:textColor="?android:attr/textColorPrimary" />

            <TextView
                android:id="@+id/tv_encoding"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:text="--"
                android:textColor="?android:attr/textColorSecondary"
                android:textIsSelectable="true" />

        </LinearLayout>

        <!-- MIME类型 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="MIME类型："
                android:textStyle="bold"
                android:textColor="?android:attr/textColorPrimary" />

            <TextView
                android:id="@+id/tv_mime_type"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:text="--"
                android:textColor="?android:attr/textColorSecondary"
                android:textIsSelectable="true" />

        </LinearLayout>

        <Button
            android:id="@+id/btn_close"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="关闭" />

    </LinearLayout>

</ScrollView>
