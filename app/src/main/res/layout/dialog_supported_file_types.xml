<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 内置文件类型列表 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="内置支持的文件类型"
            android:textAppearance="?attr/textAppearanceHeadline6"
            android:layout_marginBottom="8dp" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_file_types"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:maxHeight="300dp"
            android:scrollbars="vertical" />

        <!-- 分隔线 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="?attr/colorOutline"
            android:layout_marginVertical="16dp" />

        <!-- 用户自定义扩展名 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="用户自定义扩展名"
            android:textAppearance="?attr/textAppearanceHeadline6"
            android:layout_marginBottom="8dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="点击右下角的 + 按钮添加自定义扩展名，点击扩展名的 × 图标可以删除"
            android:textAppearance="?attr/textAppearanceCaption"
            android:textColor="?attr/colorOnSurfaceVariant"
            android:layout_marginBottom="8dp" />

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:maxHeight="120dp">

            <com.google.android.material.chip.ChipGroup
                android:id="@+id/chip_group_custom"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:chipSpacingHorizontal="4dp"
                app:chipSpacingVertical="4dp" />

        </ScrollView>

        <!-- 统计信息 -->
        <TextView
            android:id="@+id/tv_stats"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="总计支持 120+ 种文件类型"
            android:textAppearance="?attr/textAppearanceCaption"
            android:textColor="?attr/colorPrimary"
            android:gravity="center"
            android:layout_marginTop="16dp" />

    </LinearLayout>

    <!-- 添加按钮 -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_add"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:src="@drawable/ic_add"
        android:contentDescription="添加自定义扩展名"
        app:fabSize="mini" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
