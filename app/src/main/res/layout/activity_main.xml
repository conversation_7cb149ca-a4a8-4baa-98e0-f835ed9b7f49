<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <import type="android.view.View" />
        <variable
            name="viewModel"
            type="com.lingtxt.editor.ui.main.MainViewModel" />
    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true"
        tools:context=".ui.main.MainActivity">

        <!-- 主要内容区域 -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/mainContentLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <!-- 顶部工具栏 - 简化设计，仅显示文件名和基本操作 -->
            <com.google.android.material.appbar.MaterialToolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:background="?attr/colorSurface"
                android:elevation="2dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:title="@{viewModel.fileName != null ? viewModel.fileName : @string/app_name}"
                app:titleTextColor="?attr/colorOnSurface"
                app:titleTextAppearance="@style/TextAppearance.Material3.TitleMedium">

                <!-- 文件信息按钮 -->
                <ImageButton
                    android:id="@+id/btnFileInfo"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:layout_gravity="end"
                    android:layout_marginEnd="8dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:contentDescription="@string/action_file_info"
                    android:src="@drawable/ic_info"
                    android:visibility="@{viewModel.fileName != null ? View.VISIBLE : View.GONE}" />

            </com.google.android.material.appbar.MaterialToolbar>

            <!-- 状态信息栏 - 显示光标位置、编码、文件大小 -->
            <LinearLayout
                android:id="@+id/statusBar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?attr/colorSurfaceVariant"
                android:orientation="horizontal"
                android:padding="8dp"
                android:visibility="@{viewModel.fileContent != null &amp;&amp; !viewModel.fileContent.empty ? View.VISIBLE : View.GONE}"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/toolbar">

                <TextView
                    android:id="@+id/tvStatusInfo"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="行1列1 | UTF-8 | 0B"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    android:textSize="12sp" />

            </LinearLayout>

            <!-- 文本编辑区域 - 占据主要空间 -->
            <com.lingtxt.editor.ui.editor.CodeEditText
                android:id="@+id/codeEditText"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:background="@android:color/transparent"
                android:gravity="top|start"
                android:inputType="textMultiLine|textNoSuggestions"
                android:text="@{viewModel.fileContent}"
                android:textColor="?attr/colorOnSurface"
                android:textSize="12sp"
                android:visibility="@{viewModel.fileContent != null &amp;&amp; !viewModel.fileContent.empty ? View.VISIBLE : View.GONE}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/statusBar"
                app:textSizeSp="@{viewModel.fontSize}"
                tools:text="public class MainActivity {\n    // 示例代码内容...\n    private void onCreate() {\n        super.onCreate();\n    }\n}" />

            <!-- 搜索栏 - 从底部弹出，手指可达区域 -->
            <com.lingtxt.editor.ui.widget.SearchBar
                android:id="@+id/searchBar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                android:elevation="16dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

            <!-- 加载指示器 -->
            <ProgressBar
                android:id="@+id/progressBar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible" />

            <!-- 空状态提示 -->
            <LinearLayout
                android:id="@+id/emptyStateLayout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical"
                android:visibility="@{viewModel.fileContent == null || viewModel.fileContent.empty ? View.VISIBLE : View.GONE}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:layout_width="64dp"
                    android:layout_height="64dp"
                    android:alpha="0.6"
                    android:src="@drawable/ic_document_empty"
                    android:tint="?attr/colorOnSurfaceVariant" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="@string/empty_state_title"
                    android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall"
                    android:textColor="?attr/colorOnSurfaceVariant" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="@string/empty_state_message"
                    android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                    android:textColor="?attr/colorOnSurfaceVariant" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnOpenFile"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:text="@string/action_open_file"
                    app:icon="@drawable/ic_folder_open" />

            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- 底部操作栏 - 优化为更紧凑的设计 -->
        <com.google.android.material.bottomappbar.BottomAppBar
            android:id="@+id/bottomAppBar"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:layout_gravity="bottom"
            app:elevation="8dp"
            app:menu="@menu/bottom_app_bar_menu" />

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</layout>