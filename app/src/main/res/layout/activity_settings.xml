<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="?attr/colorSurface">

    <!-- 工具栏 -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorSurface"
        android:elevation="2dp"
        app:title="@string/settings"
        app:titleTextColor="?attr/colorOnSurface"
        app:navigationIcon="@drawable/ic_arrow_back"
        app:navigationIconTint="?attr/colorOnSurface" />

    <!-- 设置列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="0dp"
        android:clipToPadding="false" />

</LinearLayout>
