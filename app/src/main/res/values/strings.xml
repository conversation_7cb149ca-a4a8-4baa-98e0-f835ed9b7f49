<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">LingTxt</string>
    <string name="app_name_chinese">灵文本</string>

    <!-- Action Buttons -->
    <string name="action_search">Search</string>
    <string name="action_goto_line">Go to Line</string>
    <string name="action_settings">Settings</string>
    <string name="action_open_file">Open</string>
    <string name="action_save_file">Save</string>
    <string name="action_save_as">Save As</string>
    <string name="action_more">More</string>
    <string name="action_about">About</string>
    <string name="action_edit_mode">Edit</string>
    <string name="action_exit_edit_mode">Exit Edit</string>
    <string name="action_file_info">File Info</string>
    <string name="action_quick_menu">Quick Menu</string>
    <string name="action_recent_files">Recent Files</string>

    <string name="file_saved">File Saved</string>
    <string name="file_save_failed">File Save Failed</string>
    <string name="file_not_found">File Not Found</string>
    <string name="permission_denied">Permission Denied</string>
    <string name="save_permission_required">Write permission required to save files</string>

    <!-- Empty State -->
    <string name="empty_state_title">Welcome to LingTxt</string>
    <string name="empty_state_message">Select a file to start editing</string>

    <!-- Error Messages -->
    <string name="error_file_not_found">File not found</string>
    <string name="error_file_access_denied">File access denied</string>
    <string name="error_encoding_error">Encoding error</string>
    <string name="error_file_too_large">File too large</string>
    <string name="error_unknown">Unknown error</string>

    <!-- Status Messages -->
    <string name="loading">Loading...</string>
    <string name="file_loaded_successfully">File loaded successfully</string>
    <string name="file_saved_successfully">File saved successfully</string>

    <!-- Editor Toolbar -->
    <string name="undo">Undo</string>
    <string name="redo">Redo</string>
    <string name="search">Search</string>
    <string name="replace">Replace</string>
    <string name="goto_line">Go to Line</string>
    <string name="settings">Settings</string>

    <!-- Settings -->
    <string name="category_appearance">Appearance</string>
    <string name="category_editor">Editor</string>

    <string name="setting_font_size">Font Size</string>
    <string name="setting_font_size_summary">Adjust editor font size</string>
    <string name="setting_font_family">Font Family</string>
    <string name="setting_font_family_summary">Select editor font</string>
    <string name="setting_theme">Theme</string>
    <string name="setting_theme_summary">Select app theme</string>
    <string name="setting_language">Language</string>
    <string name="setting_language_summary">Select interface language</string>
    <string name="setting_show_line_numbers">Show Line Numbers</string>
    <string name="setting_show_line_numbers_summary">Display line numbers in editor</string>
    <string name="setting_show_invisible_chars">Show Invisible Characters</string>
    <string name="setting_show_invisible_chars_summary">Display spaces, tabs and other invisible characters</string>

    <string name="theme_light">Light</string>
    <string name="theme_dark">Dark</string>
    <string name="theme_system">Follow System</string>

    <string name="language_chinese">Chinese</string>
    <string name="language_english">English</string>
    <string name="language_system">Follow System</string>

    <!-- Search Function -->
    <string name="search_hint">Search text</string>
    <string name="replace_hint">Replace text</string>
    <string name="find_previous">Find Previous</string>
    <string name="find_next">Find Next</string>
    <string name="replace_current">Replace Current</string>
    <string name="replace_all">Replace All</string>
    <string name="close">Close</string>
    <string name="case_sensitive">Case Sensitive</string>
    <string name="whole_word">Whole Word</string>
    <string name="regex">Regular Expression</string>
    <string name="search_not_found">Not found</string>
    <string name="search_regex_error">Regular expression syntax error</string>

    <!-- Messages and Feedback -->
    <string name="quick_action_menu_placeholder">Quick Action Menu - To be implemented</string>
    <string name="focus_mode_exited">Exited focus mode</string>
    <string name="invisible_chars_shown">Invisible characters shown</string>
    <string name="invisible_chars_hidden">Invisible characters hidden</string>
    <string name="file_manager_not_found">File manager app not found</string>

    <!-- Error Handling and User Feedback -->
    <string name="crash_recovery_title">App Crash Recovery</string>
    <string name="crash_recovery_message">The app crashed last time. Would you like to send a crash report to help us improve?</string>
    <string name="send_crash_report">Send Report</string>
    <string name="ignore_crash">Ignore</string>
    <string name="view_crash_details">View Details</string>
    <string name="crash_details_title">Crash Details</string>
    <string name="crash_report_sent">Crash report recorded, thank you for your feedback</string>

    <!-- Permission Related -->
    <string name="permission_denied_title">Permission Denied</string>
    <string name="permission_needed_title">Permission Required</string>
    <string name="go_to_settings">Go to Settings</string>
    <string name="grant_permission">Grant</string>
    <string name="permission_granted">Permission granted</string>
    <string name="permission_denied_some_features_unavailable">Permission denied, some features may not work</string>

    <!-- File Permission Messages -->
    <string name="storage_permission_denied">Basic file access permission denied\n\nYou can still use the app by:\n• Using the file picker (recommended)\n• Granting media access permission</string>
    <string name="audio_permission_denied">Audio recording permission denied, cannot use voice features\n\nPlease enable permission in settings.</string>
    <string name="storage_permission_rationale">LingTxt needs basic file access to browse media folders.\n\nFor better experience:\n• Grant \"Media only\" permission (safe)\n• Or use file picker to access any file (no permission needed)</string>
    <string name="audio_permission_rationale">LingTxt needs audio recording permission for voice input features. You can choose not to grant it, but voice features will be unavailable.</string>

    <!-- File Access Methods -->
    <string name="file_access_method_title">Choose File Access Method</string>
    <string name="use_file_picker">📁 Use File Picker</string>
    <string name="grant_media_permission">🎵 Grant Media Permission</string>
    <string name="grant_all_files_permission">📂 Grant All Files</string>


    <!-- Operation Feedback -->
    <string name="operation_success">%s successful</string>
    <string name="operation_failed">%s failed: %s</string>
    <string name="loading_file">Loading %s...</string>
    <string name="saving_file">Saving %s...</string>
    <string name="performance_optimization_enabled">🚀 %s enabled, enjoy smoother experience</string>
    <string name="large_file_warning">Large file (%s), performance optimization enabled</string>

    <!-- Search and Edit Feedback -->
    <string name="search_no_results">No matches found</string>
    <string name="search_results">Found %d matches, current: %d</string>
    <string name="replace_no_results">No content to replace</string>
    <string name="replace_results">Replaced %d occurrences</string>
    <string name="goto_line_result">Jumped to line %d</string>

    <!-- Settings Change Feedback -->
    <string name="setting_changed">%s set to: %s</string>
    <string name="feature_enabled">%s enabled</string>
    <string name="feature_disabled">%s disabled</string>
    <string name="language_switched_to">Language switched to: %s</string>
    <string name="status_format">Line %d Col %d | %s | %s | %s</string>

    <!-- File Operations -->
    <string name="no_file_to_save">No file to save</string>
    <string name="file_load_failed">File loading failed: %s</string>
    <string name="save_failed">Save failed: %s</string>
    <string name="unknown_file">Unknown file</string>
    <string name="search_error_prefix">Error: %s</string>

    <!-- Recent Files -->
    <string name="recent_files_title">Recent Files</string>
    <string name="recent_files_load_failed">Failed to load recent files: %s</string>
    <string name="no_recent_files">No recent files</string>
    <string name="clear_recent_files">Clear recent files</string>
    <string name="sort_by_name">Sort by name</string>
    <string name="sort_by_date">Sort by date</string>
    <string name="sort_by_size">Sort by size</string>
    <string name="replaced_count">Replaced %d occurrences</string>

    <!-- Recent Files UI -->
    <string name="no_recent_files_title">No recent files</string>
    <string name="no_recent_files_message">Files will appear here after opening</string>
    <string name="open_file">Open file</string>
    <string name="clear_list">Clear list</string>
    <string name="sort_method">Sort method</string>
    <string name="sort_by_time">By access time</string>
    <string name="open">Open</string>
    <string name="remove_from_list">Remove from list</string>

    <!-- Encoding -->
    <string name="encoding_utf8">UTF-8</string>
    <string name="encoding_gbk">GBK</string>
    <string name="encoding_ascii">ASCII</string>
    <string name="auto_detect_encoding">Auto Detect Encoding</string>

    <!-- Syntax Highlighting -->
    <string name="syntax_highlight">Syntax Highlighting</string>
    <string name="select_language">Select Language</string>
    <string name="syntax_highlight_enabled">Syntax highlighting enabled</string>
    <string name="syntax_highlight_disabled">Syntax highlighting disabled</string>
    <string name="select_syntax_language">Select syntax highlighting language</string>
    <string name="language_switched">Switched to %s</string>
    <string name="cancel">Cancel</string>

    <!-- Go to Line Function -->
    <string name="max_line_count">Max line count: %d</string>
    <string name="input_line_number_hint">Enter line number</string>
    <string name="please_input_line_number">Please enter line number</string>
    <string name="line_number_out_of_range">Line number out of range</string>

    <!-- Immersive Reading Function -->
    <string name="action_focus_mode">Focus Mode</string>
    <string name="action_show_invisible_chars">Show Invisible Characters</string>
    <string name="focus_mode_enabled">Focus mode enabled</string>
    <string name="focus_mode_disabled">Focus mode disabled</string>
    
</resources>