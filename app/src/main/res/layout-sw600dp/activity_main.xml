<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <import type="android.view.View" />
        <variable
            name="viewModel"
            type="com.lingtxt.editor.ui.main.MainViewModel" />
    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true"
        tools:context=".ui.main.MainActivity">

        <!-- 主要内容区域 - 平板优化 -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="?attr/actionBarSize">

            <!-- 顶部工具栏 - 平板上显示更多信息 -->
            <com.google.android.material.appbar.MaterialToolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:background="?attr/colorSurface"
                android:elevation="2dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:title="@{viewModel.fileName != null ? viewModel.fileName : @string/app_name}"
                app:titleTextColor="?attr/colorOnSurface"
                app:titleTextAppearance="@style/TextAppearance.Material3.TitleLarge">

                <!-- 工具栏按钮组 -->
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_gravity="end"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <!-- 字体大小调整 -->
                    <ImageButton
                        android:id="@+id/btnDecreaseFontSize"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:contentDescription="减小字体"
                        android:src="@drawable/ic_text_decrease" />

                    <ImageButton
                        android:id="@+id/btnIncreaseFontSize"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:contentDescription="增大字体"
                        android:src="@drawable/ic_text_increase" />

                    <!-- 行号切换 -->
                    <ImageButton
                        android:id="@+id/btnToggleLineNumbers"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:contentDescription="切换行号"
                        android:src="@drawable/ic_format_list_numbered" />

                    <!-- 文件信息 -->
                    <ImageButton
                        android:id="@+id/btnFileInfo"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_marginEnd="8dp"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:contentDescription="@string/action_file_info"
                        android:src="@drawable/ic_info"
                        android:visibility="@{viewModel.fileName != null ? View.VISIBLE : View.GONE}" />

                </LinearLayout>

            </com.google.android.material.appbar.MaterialToolbar>

            <!-- 文本编辑区域 - 平板优化，更大的编辑区域 -->
            <HorizontalScrollView
                android:id="@+id/horizontalScrollView"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:scrollbars="horizontal"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/toolbar">

                <ScrollView
                    android:id="@+id/verticalScrollView"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:scrollbars="vertical">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="8dp">

                        <!-- 行号显示区域 - 平板上更宽 -->
                        <TextView
                            android:id="@+id/lineNumbers"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="?attr/colorSurfaceVariant"
                            android:fontFamily="monospace"
                            android:gravity="end"
                            android:minWidth="60dp"
                            android:padding="12dp"
                            android:textColor="?attr/colorOnSurfaceVariant"
                            android:textSize="14sp"
                            android:visibility="@{viewModel.showLineNumbers ? View.VISIBLE : View.GONE}"
                            tools:text="1\n2\n3\n4\n5" />

                        <!-- 分隔线 -->
                        <View
                            android:layout_width="2dp"
                            android:layout_height="match_parent"
                            android:background="?attr/colorOutline"
                            android:visibility="@{viewModel.showLineNumbers ? View.VISIBLE : View.GONE}" />

                        <!-- 文本编辑器 - 平板上自动换行 -->
                        <com.lingtxt.editor.ui.editor.CodeEditText
                            android:id="@+id/codeEditText"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@android:color/transparent"
                            android:gravity="top|start"
                            android:inputType="textMultiLine|textNoSuggestions"
                            android:scrollHorizontally="false"
                            android:text="@{viewModel.fileContent}"
                            android:textColor="?attr/colorOnSurface"
                            android:textSize="16sp"
                            app:textSizeSp="@{viewModel.fontSize}"
                            android:visibility="@{viewModel.fileContent != null &amp;&amp; !viewModel.fileContent.empty ? View.VISIBLE : View.GONE}"
                            tools:text="public class MainActivity {\n    // 示例代码内容...\n    private void onCreate() {\n        super.onCreate();\n    }\n}" />

                    </LinearLayout>

                </ScrollView>

            </HorizontalScrollView>

            <!-- 加载指示器 -->
            <ProgressBar
                android:id="@+id/progressBar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible" />

            <!-- 空状态提示 - 平板优化 -->
            <LinearLayout
                android:id="@+id/emptyStateLayout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical"
                android:visibility="@{viewModel.fileContent == null || viewModel.fileContent.empty ? View.VISIBLE : View.GONE}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:layout_width="96dp"
                    android:layout_height="96dp"
                    android:alpha="0.6"
                    android:src="@drawable/ic_document_empty"
                    android:tint="?attr/colorOnSurfaceVariant" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:text="@string/empty_state_title"
                    android:textAppearance="@style/TextAppearance.Material3.HeadlineMedium"
                    android:textColor="?attr/colorOnSurfaceVariant" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:text="@string/empty_state_message"
                    android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                    android:textColor="?attr/colorOnSurfaceVariant" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnOpenFile"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="32dp"
                    android:text="@string/action_open_file"
                    app:icon="@drawable/ic_folder_open" />

            </LinearLayout>

            <!-- 搜索栏 - 从底部弹出，手指可达区域 -->
            <com.lingtxt.editor.ui.widget.SearchBar
                android:id="@+id/searchBar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

            <!-- 状态栏 - 显示文件信息和编辑状态 -->
            <TextView
                android:id="@+id/tvStatusInfo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?attr/colorSurfaceVariant"
                android:gravity="center"
                android:padding="6dp"
                android:textAppearance="@style/TextAppearance.Material3.LabelMedium"
                android:textColor="?attr/colorOnSurfaceVariant"
                android:visibility="gone"
                app:layout_constraintBottom_toTopOf="@id/searchBar"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                tools:text="行: 1, 列: 1 | UTF-8 | 1.2 KB"
                tools:visibility="visible" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- 底部操作栏 - 平板上显示更多操作 -->
        <com.google.android.material.bottomappbar.BottomAppBar
            android:id="@+id/bottomAppBar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:layout_gravity="bottom"
            app:elevation="8dp"
            app:menu="@menu/bottom_app_bar_menu_tablet" />

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</layout>