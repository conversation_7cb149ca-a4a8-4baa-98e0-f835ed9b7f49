package com.lingtxt.editor.config;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.annotation.Config;

import static org.junit.Assert.*;

/**
 * SupportedFileTypes配置测试
 */
@RunWith(RobolectricTestRunner.class)
@Config(sdk = 28)
public class SupportedFileTypesTest {

    @Test
    public void testIsSupportedExtension_shouldReturnTrueForCommonExtensions() {
        // 测试常见的文本文件扩展名
        assertTrue("txt should be supported", SupportedFileTypes.isSupportedExtension("txt"));
        assertTrue("java should be supported", SupportedFileTypes.isSupportedExtension("java"));
        assertTrue("py should be supported", SupportedFileTypes.isSupportedExtension("py"));
        assertTrue("js should be supported", SupportedFileTypes.isSupportedExtension("js"));
        assertTrue("html should be supported", SupportedFileTypes.isSupportedExtension("html"));
        assertTrue("css should be supported", SupportedFileTypes.isSupportedExtension("css"));
        assertTrue("xml should be supported", SupportedFileTypes.isSupportedExtension("xml"));
        assertTrue("json should be supported", SupportedFileTypes.isSupportedExtension("json"));
    }

    @Test
    public void testIsSupportedExtension_shouldReturnFalseForUnsupportedExtensions() {
        // 测试不支持的扩展名
        assertFalse("exe should not be supported", SupportedFileTypes.isSupportedExtension("exe"));
        assertFalse("jpg should not be supported", SupportedFileTypes.isSupportedExtension("jpg"));
        assertFalse("mp4 should not be supported", SupportedFileTypes.isSupportedExtension("mp4"));
        assertFalse("pdf should not be supported", SupportedFileTypes.isSupportedExtension("pdf"));
    }

    @Test
    public void testIsSupportedFile_shouldHandleFileNames() {
        // 测试完整文件名
        assertTrue("test.txt should be supported", SupportedFileTypes.isSupportedFile("test.txt"));
        assertTrue("MainActivity.java should be supported", SupportedFileTypes.isSupportedFile("MainActivity.java"));
        assertTrue("script.py should be supported", SupportedFileTypes.isSupportedFile("script.py"));
        
        // 测试不支持的文件
        assertFalse("image.jpg should not be supported", SupportedFileTypes.isSupportedFile("image.jpg"));
        assertFalse("video.mp4 should not be supported", SupportedFileTypes.isSupportedFile("video.mp4"));
        
        // 测试没有扩展名的文件
        assertFalse("README should not be supported", SupportedFileTypes.isSupportedFile("README"));
    }

    @Test
    public void testGetFileTypeDisplayName_shouldReturnCorrectNames() {
        // 测试显示名称
        assertEquals("Java源文件", SupportedFileTypes.getFileTypeDisplayName("Test.java"));
        assertEquals("Python脚本", SupportedFileTypes.getFileTypeDisplayName("script.py"));
        assertEquals("JavaScript文件", SupportedFileTypes.getFileTypeDisplayName("app.js"));
        assertEquals("HTML文档", SupportedFileTypes.getFileTypeDisplayName("index.html"));
        assertEquals("CSS样式表", SupportedFileTypes.getFileTypeDisplayName("style.css"));
        
        // 测试未知扩展名
        assertEquals("ABC文件", SupportedFileTypes.getFileTypeDisplayName("test.abc"));
        assertEquals("文件", SupportedFileTypes.getFileTypeDisplayName("noextension"));
    }

    @Test
    public void testFileTypeCategories() {
        // 测试编程语言文件
        assertTrue("Java is programming file", SupportedFileTypes.isProgrammingFile("Test.java"));
        assertTrue("Python is programming file", SupportedFileTypes.isProgrammingFile("script.py"));
        assertTrue("JavaScript is programming file", SupportedFileTypes.isProgrammingFile("app.js"));
        assertFalse("HTML is not programming file", SupportedFileTypes.isProgrammingFile("index.html"));
        
        // 测试配置文件
        assertTrue("JSON is config file", SupportedFileTypes.isConfigFile("config.json"));
        assertTrue("XML is config file", SupportedFileTypes.isConfigFile("settings.xml"));
        assertTrue("YAML is config file", SupportedFileTypes.isConfigFile("docker-compose.yml"));
        assertFalse("Java is not config file", SupportedFileTypes.isConfigFile("Test.java"));
        
        // 测试Web文件
        assertTrue("HTML is web file", SupportedFileTypes.isWebFile("index.html"));
        assertTrue("CSS is web file", SupportedFileTypes.isWebFile("style.css"));
        assertFalse("Java is not web file", SupportedFileTypes.isWebFile("Test.java"));
        
        // 测试脚本文件
        assertTrue("Shell is script file", SupportedFileTypes.isScriptFile("deploy.sh"));
        assertTrue("Batch is script file", SupportedFileTypes.isScriptFile("build.bat"));
        assertFalse("Java is not script file", SupportedFileTypes.isScriptFile("Test.java"));
    }

    @Test
    public void testPrimaryExtensions_shouldBeSubsetOfAllExtensions() {
        // 验证主要扩展名都在支持的扩展名列表中
        String[] primaryExtensions = SupportedFileTypes.getPrimaryExtensions();
        for (String ext : primaryExtensions) {
            assertTrue("Primary extension " + ext + " should be in supported list", 
                      SupportedFileTypes.isSupportedExtension(ext));
        }
    }

    @Test
    public void testCaseInsensitive() {
        // 测试大小写不敏感
        assertTrue("JAVA should be supported", SupportedFileTypes.isSupportedExtension("JAVA"));
        assertTrue("TXT should be supported", SupportedFileTypes.isSupportedExtension("TXT"));
        assertTrue("Test.JAVA should be supported", SupportedFileTypes.isSupportedFile("Test.JAVA"));
    }
}