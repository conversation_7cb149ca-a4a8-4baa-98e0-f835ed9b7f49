package com.lingtxt.editor.data.repository;

import android.content.Context;
import android.net.Uri;
import com.lingtxt.editor.data.model.FileContent;
import com.lingtxt.editor.data.model.FileInfo;
import io.reactivex.rxjava3.observers.TestObserver;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.annotation.Config;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * FileRepository单元测试
 */
@RunWith(RobolectricTestRunner.class)
@Config(sdk = 28)
public class FileRepositoryTest {

    @Mock
    private Context mockContext;

    @Mock
    private Uri mockUri;

    private FileRepository fileRepository;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        fileRepository = new FileRepositoryImpl(mockContext);
    }

    @Test
    public void testDetectEncoding_shouldReturnUTF8ForValidFile() {
        // Given
        when(mockUri.toString()).thenReturn("content://test/file.txt");

        // When
        TestObserver<String> testObserver = fileRepository.detectEncoding(mockUri).test();

        // Then
        testObserver.assertComplete();
        testObserver.assertNoErrors();
        // 注意：由于我们mock了Context，实际的编码检测可能不会工作
        // 这里主要测试方法调用是否正常
    }

    @Test
    public void testIsFileAccessible_shouldReturnFalseForInvalidUri() {
        // Given
        when(mockUri.toString()).thenReturn("invalid://uri");

        // When
        TestObserver<Boolean> testObserver = fileRepository.isFileAccessible(mockUri).test();

        // Then
        testObserver.assertComplete();
        testObserver.assertValue(false);
    }

    @Test
    public void testReadWithEncoding_shouldHandleUnsupportedEncoding() {
        // Given
        String unsupportedEncoding = "INVALID_ENCODING";

        // When
        TestObserver<String> testObserver = fileRepository
            .readWithEncoding(mockUri, unsupportedEncoding).test();

        // Then
        testObserver.assertError(IllegalArgumentException.class);
    }

    @Test
    public void testReadFileInChunks_shouldEmitChunks() {
        // Given
        String encoding = "UTF-8";
        int chunkSize = 1024;

        // When
        TestObserver<String> testObserver = fileRepository
            .readFileInChunks(mockUri, encoding, chunkSize).test();

        // Then
        // 由于我们使用了mock，这个测试主要验证方法签名和基本调用
        testObserver.assertComplete();
    }
}