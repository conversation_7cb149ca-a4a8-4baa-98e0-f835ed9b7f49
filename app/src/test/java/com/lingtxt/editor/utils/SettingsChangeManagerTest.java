package com.lingtxt.editor.utils;

import android.content.Context;
import com.lingtxt.editor.data.model.AppLanguage;
import com.lingtxt.editor.data.model.Theme;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.RuntimeEnvironment;

import static org.mockito.Mockito.*;

/**
 * SettingsChangeManager 单元测试
 */
@RunWith(RobolectricTestRunner.class)
public class SettingsChangeManagerTest {

    @Mock
    private SettingsChangeManager.SettingsChangeListener mockListener;

    private SettingsChangeManager settingsChangeManager;
    private Context context;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        settingsChangeManager = SettingsChangeManager.getInstance();
        context = RuntimeEnvironment.getApplication();
        
        // 清理之前的监听器
        settingsChangeManager.clearListeners();
    }

    @Test
    public void testRegisterAndUnregisterListener() {
        // 注册监听器
        settingsChangeManager.registerListener(mockListener);
        
        // 触发主题变更
        settingsChangeManager.applyThemeChange(Theme.DARK);
        
        // 验证监听器被调用
        verify(mockListener).onThemeChanged(Theme.DARK);
        
        // 注销监听器
        settingsChangeManager.unregisterListener(mockListener);
        
        // 再次触发变更
        settingsChangeManager.applyThemeChange(Theme.LIGHT);
        
        // 验证监听器不再被调用（只调用了一次）
        verify(mockListener, times(1)).onThemeChanged(any(Theme.class));
    }

    @Test
    public void testThemeChange() {
        settingsChangeManager.registerListener(mockListener);
        
        // 测试各种主题变更
        settingsChangeManager.applyThemeChange(Theme.LIGHT);
        verify(mockListener).onThemeChanged(Theme.LIGHT);
        
        settingsChangeManager.applyThemeChange(Theme.DARK);
        verify(mockListener).onThemeChanged(Theme.DARK);
        
        settingsChangeManager.applyThemeChange(Theme.SYSTEM);
        verify(mockListener).onThemeChanged(Theme.SYSTEM);
    }

    @Test
    public void testLanguageChange() {
        settingsChangeManager.registerListener(mockListener);
        
        // 测试语言变更
        settingsChangeManager.applyLanguageChange(context, AppLanguage.CHINESE);
        verify(mockListener).onLanguageChanged(AppLanguage.CHINESE);
        
        settingsChangeManager.applyLanguageChange(context, AppLanguage.ENGLISH);
        verify(mockListener).onLanguageChanged(AppLanguage.ENGLISH);
    }

    @Test
    public void testFontSizeChange() {
        settingsChangeManager.registerListener(mockListener);
        
        // 测试字体大小变更
        float fontSize = 16.0f;
        settingsChangeManager.applyFontSizeChange(fontSize);
        verify(mockListener).onFontSizeChanged(fontSize);
    }

    @Test
    public void testFontFamilyChange() {
        settingsChangeManager.registerListener(mockListener);
        
        // 测试字体族变更
        String fontFamily = "serif";
        settingsChangeManager.applyFontFamilyChange(fontFamily);
        verify(mockListener).onFontFamilyChanged(fontFamily);
    }

    @Test
    public void testOtherSettingChange() {
        settingsChangeManager.registerListener(mockListener);
        
        // 测试其他设置变更
        String key = "show_line_numbers";
        Boolean value = true;
        settingsChangeManager.applyOtherSettingChange(key, value);
        verify(mockListener).onOtherSettingChanged(key, value);
    }

    @Test
    public void testMultipleListeners() {
        SettingsChangeManager.SettingsChangeListener mockListener2 = mock(SettingsChangeManager.SettingsChangeListener.class);
        
        // 注册多个监听器
        settingsChangeManager.registerListener(mockListener);
        settingsChangeManager.registerListener(mockListener2);
        
        // 触发变更
        settingsChangeManager.applyThemeChange(Theme.DARK);
        
        // 验证所有监听器都被调用
        verify(mockListener).onThemeChanged(Theme.DARK);
        verify(mockListener2).onThemeChanged(Theme.DARK);
    }

    @Test
    public void testListenerExceptionHandling() {
        // 创建一个会抛出异常的监听器
        SettingsChangeManager.SettingsChangeListener faultyListener = new SettingsChangeManager.SettingsChangeListener() {
            @Override
            public void onThemeChanged(Theme theme) {
                throw new RuntimeException("Test exception");
            }

            @Override
            public void onLanguageChanged(AppLanguage language) {}

            @Override
            public void onFontSizeChanged(float fontSize) {}

            @Override
            public void onFontFamilyChanged(String fontFamily) {}

            @Override
            public void onOtherSettingChanged(String key, Object value) {}
        };
        
        // 注册正常监听器和异常监听器
        settingsChangeManager.registerListener(mockListener);
        settingsChangeManager.registerListener(faultyListener);
        
        // 触发变更，应该不会因为异常而中断
        settingsChangeManager.applyThemeChange(Theme.DARK);
        
        // 验证正常监听器仍然被调用
        verify(mockListener).onThemeChanged(Theme.DARK);
    }
}
