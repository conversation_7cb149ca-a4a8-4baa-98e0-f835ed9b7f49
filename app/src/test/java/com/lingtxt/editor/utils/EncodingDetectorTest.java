package com.lingtxt.editor.utils;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.annotation.Config;

import static org.junit.Assert.*;

/**
 * EncodingDetector单元测试
 */
@RunWith(RobolectricTestRunner.class)
@Config(sdk = 28)
public class EncodingDetectorTest {

    @Test
    public void testIsEncodingSupported_shouldReturnTrueForUTF8() {
        // When
        boolean isSupported = EncodingDetector.isEncodingSupported("UTF-8");

        // Then
        assertTrue("UTF-8 should be supported", isSupported);
    }

    @Test
    public void testIsEncodingSupported_shouldReturnFalseForInvalidEncoding() {
        // When
        boolean isSupported = EncodingDetector.isEncodingSupported("INVALID_ENCODING");

        // Then
        assertFalse("Invalid encoding should not be supported", isSupported);
    }

    @Test
    public void testGetEncodingDisplayName_shouldReturnCorrectDisplayName() {
        // Test UTF-8
        assertEquals("UTF-8", EncodingDetector.getEncodingDisplayName("UTF-8"));
        assertEquals("UTF-8", EncodingDetector.getEncodingDisplayName("utf-8"));

        // Test UTF-16
        assertEquals("UTF-16", EncodingDetector.getEncodingDisplayName("UTF-16"));
        assertEquals("UTF-16 BE", EncodingDetector.getEncodingDisplayName("UTF-16BE"));
        assertEquals("UTF-16 LE", EncodingDetector.getEncodingDisplayName("UTF-16LE"));

        // Test ASCII
        assertEquals("ASCII", EncodingDetector.getEncodingDisplayName("US-ASCII"));

        // Test Chinese encodings
        assertEquals("GBK", EncodingDetector.getEncodingDisplayName("GBK"));
        assertEquals("GB2312", EncodingDetector.getEncodingDisplayName("GB2312"));

        // Test unknown encoding
        assertEquals("UNKNOWN", EncodingDetector.getEncodingDisplayName("UNKNOWN"));
    }

    @Test
    public void testGetEncodingDisplayName_shouldHandleNullAndEmpty() {
        // Test null
        assertEquals("", EncodingDetector.getEncodingDisplayName(""));
        
        // Test empty string
        assertEquals("", EncodingDetector.getEncodingDisplayName(""));
    }
}